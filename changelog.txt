*** WooCommerce Tax Changelog ***

= 3.0.8 - 2025-xx-xx =
* Fix   - Improves performance when WooCommerce Shipping is not active.
* Tweak - WooCommerce 10.1 Compatibility.

= 3.0.7 - 2025-07-21 =
* Fix   - Missing release files.

= 3.0.6 - 2025-07-21 =
* Add   - Support for Itemized tax rates.
* Fix   - TaxJar error notices displaying incorrectly on block cart and checkout.

= 3.0.5 - 2025-07-14 =
* Tweak - WooCommerce 10.0 Compatibility.

= 3.0.4 - 2025-06-30 =
* Fix   - Corrected tax calculation for orders shipped within Arizona from stores based in Arizona.

= 3.0.3 - 2025-06-12 =
* Tweak - Update Org store screenshots.

= 3.0.2 - 2025-06-02 =
* Rename the plugin and updates the description in the Org store.

= 3.0.1 - 2025-05-22 =
* Fix   - Maintain label purchase functionality on iOS app for eligible installations.

= 3.0.0 - 2025-05-08 =
* Add   - Legacy site detection to maintain shipping functionality for existing installations.
* Tweak - Improve tax tracking.

= 2.8.9 - 2025-04-07 =
* Tweak - WordPress 6.8 & WooCommerce 9.8 Compatibility.

= 2.8.8 - 2025-03-03 =
* Tweak - WooCommerce 9.7 Compatibility.

= 2.8.7 - 2025-01-20 =
* Add   - Option to apply US Colorado Retail Delivery Fee tax by using `wc_services_apply_us_co_retail_delivery_fee` filter.

= 2.8.6 - 2025-01-06 =
* Tweak - PHP 8.4 compatibility.

= 2.8.5 - 2024-12-10 =
* Fix   - Fixed an issue that prevented editing an order when automated tax is enabled.

= 2.8.4 - 2024-12-09 =
* Fix   - Support High-Performance Order Storage in shipping label reports.

= 2.8.3 - 2024-10-29 =
* Tweak - WordPress 6.7 Compatibility.

= 2.8.2 - 2024-09-23 =
* Fix   - Keep live rates enabled for eligible stores when WCS&T is active alongside WooCommerce Shipping.
* Tweak - Hide shipping migration banner for all stores not eligible to buy shipping labels.
* Tweak - Try WooCommerce Shipping modal copy.

= 2.8.1 - 2024-09-09 =
* Tweak - Hide migration banner for merchants still using legacy functionality.

= 2.8.0 - 2024-09-03 =
* Add - A new shipping migration experience from this plugin to the newly released WooCommerce Shipping plugin.

= 2.7.0 - 2024-07-25 =
* Add - Parallel compatibility with WooCommerce Shipping plugin.

= 2.6.2 - 2024-07-16 =
* Fix - Require HS Tariff number on customs form for EU destination countries.

= 2.6.1 - 2024-07-02 =
* Tweak - WooCommerce 9.0 and WordPress 6.6 compatibility.

= 2.6.0 - 2024-06-04 =
* Add - Logger for "Live Rates" feature on the front-end.

= 2.5.7 - 2024-05-13 =
* Add - wc_connect_shipment_item_quantity_threshold and wc_connect_max_shipments_if_quantity_exceeds_threshold filter hooks to be able to cap the number of shipment splits possible for an item with very large quantity.

= 2.5.6 - 2024-05-06 =
* Tweak - WooCommerce 8.8 compatibility.

= 2.5.5 - 2024-04-29 =
* Add - Prevent upcoming Woo Shipping and Woo Tax plugins from running in parallel with this plugin unless both are active, then they will take over for this plugin.

= 2.5.4 - 2024-03-25 =
* Tweak - WordPress 6.5 compatibility.

= 2.5.3 - 2024-03-12 =
* Fix - Colorado tax nexus workaround should only apply to Colorado from addresses.

= 2.5.2 - 2024-03-04 =
* Fix - Miscalculation tax from TaxJar and decided to use nexus address.

= 2.5.1 - 2024-02-12 =
* Fix - Cannot call constructor in classes/wc-api-dev/class-wc-rest-dev-data-continents-controller.php.

= 2.5.0 - 2024-01-08 =
* Add - Ability to keep connected to WordPress.com after Jetpack is uninstalled.
* Fix - Deprecation notices for PHP 8.2.

= 2.4.2 - 2023-11-30 =
* Fix - When automated taxes are enabled, the order refund button will fail.

= 2.4.1 - 2023-11-28 =
* Fix - Street address is not included when recalculating the tax in edit order page.

= 2.4.0 - 2023-10-31 =
* Add - Ability to connect to WordPress.com without the Jetpack plugin.
* Fix - NUX banner display on Edit Order pages.

= 2.3.7 - 2023-10-23 =
* Add - Load Sift when printing a label.

= 2.3.6 - 2023-10-10 =
* Fix - Occasionally block user to checkout when using WooCommerce Blocks.
* Fix - Fix notice error when shipping location(s) is disabled in WooCommerce settings.

= 2.3.5 - 2023-09-20 =
* Tweak - Move Jetpack Connection requirement to the top in FAQ.

= 2.3.4 - 2023-09-05 =
* Fix - Shipping label reports to display proper HTML.

= 2.3.3 - 2023-08-22 =
* Tweak - Update .org assets.

= 2.3.2 - 2023-08-09 =
* Add   - Added QIT tools for development.

= 2.3.1 - 2023-07-17 =
* Fix    - Fix notice error on the WooCommerce tax settings page.

= 2.3.0 - 2023-07-11 =
* Add   - Add USPS HAZMAT support.

= 2.2.5 - 2023-05-23 =
* Update - Security update.

= 2.2.4 - 2023-03-14 =
* Fix   - Incompatibility with Kadence WooCommerce Email Designer.

= 2.2.3 - 2023-02-14 =
* Fix   - Link correction on Automated taxes description text.

= 2.2.2 - 2023-02-02 =
* Fix   - Adjust checkout US zipcode validation to run only when exactly 5 or 10 digits are typed.

= 2.2.1 - 2023-01-24 =
* Fix   - Fix warning on checkout page apper if zipcode doesn't match selected state.

= 2.2.0 - 2023-01-19 =
* Add   - Add option to let user pick whether to save the last package & service or not.

= 2.1.1 - 2023-01-02 =
* Fix   - Save the selected package box and do not skip the package step.

= 2.1.0 - 2022-11-30 =
* Tweak - Catch malformed zipcode and display WC notice.

= 2.0.0 - 2022-11-16 =
* Add   - High-Performance Order Storage compatibility.
* Add   - Add list of tax rate backup files for merchants to click and download.
* Tweak - Transition version numbering from SemVer to WordPress versioning.

= 1.26.3 - 2022-08-03 =
* Add   - Add filter to override TaxJar result.
* Fix   - Uncatch error when installing/connecting the Jetpack.
* Tweak - Always let the user to pick the package box.

= 1.26.2 - 2022-07-04 =
* Fix   - Change the wp-calypso commit to fix NPM Error when run `npm run prepare`.
* Fix   - E2E Tests: npm ci, update puppeteer to v2
* Fix   - JS Tests: npm ci
* Tweak - Replace colors npm package with chalk

= 1.26.1 - 2022-06-21 =
* Add   - Display warning if non-roman character is entered in address fields.
* Fix   - "Division by Zero" fatal error on PHP 8.
* Tweak - Allow electronically submitted commercial invoices to be downloaded.

= 1.26.0 - 2022-05-27 =
* Add   - Tool to clear cached Tax server responses from the transients.
* Tweak - Enable shipping tax by default if is Florida interstate shipping.

= 1.25.28 - 2022-05-12 =
* Fix   - Notice: Undefined index: 'from_country' when validating TaxJar request.

= 1.25.27 - 2022-05-03 =
* Fix   - Cart with non-taxable product still calculate the tax.
* Tweak - Validate the TaxJar request before calling the api and cache 404 and 400 TaxJar response error for 5 minutes.

= 1.25.26 - 2022-04-19 =
* Fix   - Display error on cart block and checkout block from WC Blocks plugin.
* Fix   - TaxJar does not calculate Quebec Sales Tax when shipping from Canadian address.

= 1.25.25 - 2022-03-29 =
* Fix   - TaxJar does not get the tax if the cart has non-taxable on the first item.
* Tweak - Use regex to check on WC Rest API route for WooCommerce Blocks compatibility.

= 1.25.24 - 2022-03-17 =
* Fix - Empty document is opened when Firefox is set to open PDF file using another program.
* Fix - Label purchase modal sections getting cut off.

= 1.25.23 - 2022-02-10 =
* Tweak - Make "Name" field optional if "Company" field is not empty.
* Fix   - Added "Delete California tax rates" tool.
* Fix   - Extract WC_Connect_TaxJar_Integration::backup_existing_tax_rates() for re-usability.

= 1.25.22 - 2022-02-02 =
* Fix   - TaxJar does not get the tax if the cart has non-taxable item.
* Tweak - Bump WP tested version to 5.9 and WC tested version to 6.1.

= 1.25.21 - 2022-01-26 =
* Fix - Use 'native' pdf support feature for Firefox version 94 or later.
* Fix - Only call WC Subscriptions API when "access_token_secret" value is saved in database.
* Fix - Add name field to fields sent for EasyPost API address verification.
* Fix - Display company name under origin and destination address when create shipping label.
* Fix - Don't override general "Enable Tax" setting with WC Services Automated Taxes setting.

= 1.25.20 - 2021-11-15 =
* Fix - Hide "Shipping Label" and "Shipment Tracking" metabox when the label setting is disabled.
* Fix - Synchronized subscription item pricing label bug.
* Fix - Wrap TaxJar API zipcodes with wc_normalize_postcode() before inserting into the database.
* Fix - Update shipping label to only show non-refunded order line items.
* Fix - Added 3 digits currency code on shipping label price for non USD.
* Fix - Defined WP_TESTS_PHPUNIT_POLYFILLS_PATH in PHPUnit bootstrap.php.

= 1.25.19 - 2021-10-14 =
* Add - Notice about tax nexus in settings.
* Fix - Country drop down list no longer showing currency name.

= 1.25.18 - 2021-08-16 =
* Add   - Added "Automated Taxes" health item on status page.
* Fix   - Show error when missing required destination phone for international shipments.
* Tweak - Bump WP tested version to 5.8.
* Tweak	- Bump WC Tested version to 5.5.
* Fix   - Prevent PHP notice when a label's `commercial_invoice_url` value is `null`.
* Fix   - Prevent fatal error when viewing draft order.

= 1.25.17 - 2021-07-13 =
* Tweak - Replace Calypso FormCheckbox with CheckboxControl.

= 1.25.16 - 2021-07-09 =
* Tweak - Replace components with @wordpress/components.

= 1.25.15 - 2021-06-30 =
* Fix   - Ensure shipping label metabox is displayed to users with the correct capabilities.
* Add   - Added `wcship_user_can_manage_labels` filter to check permissions to print shipping labels.
* Add   - Added `wcship_manage_labels` capability to check permissions to print shipping labels.

= 1.25.14 - 2021-06-15 =
* Fix   - Issue with printing blank label in Safari.
* Fix   - DHL Express labels - require customs form when shipping to Puerto Rico.
* Fix   - Update DHL Express pickup link.

= 1.25.13 - 2021-05-20 =
* Fix   - Prevent new sites from retrying failed connections.
* Fix   - Data encoding when entities are part of order meta.
* Tweak - Update WC version support in headers.
* Fix   - Plugin deletion when WooCommerce core is not present.
* Tweak - Rename automatic tax names for US.
* Fix   - Check Jetpack constant defined by name.
* Fix   - Sometimes taxes charged on shipping when they should not.

= 1.25.12 - 2021-04-21 =
* Fix   - UPS account connection form retry on invalid submission.
* Fix   - Fix PHP 5.6 compatibility issue.
* Tweak - Update plugin author name.
* Fix   - Removes unnecessary subscription debug error logs.

= 1.25.11 - 2021-04-06 =
* Fix	- Ensure status page is displayed on new WC navigation menu.
* Add   - Run phpcbf as a pre-commit rule.
* Fix   - Fix PHPUnit tests. Rename `test_` to `test-` to match our phpcs rules. Remove travis and move to github action.
* Tweak - Updated .nvmrc to use 10.16.0
* Tweak - Update the shipping label status endpoint to accept and return multiple ids.
* Tweak	- Display spinner icon during service data refresh.
* Add	- Adds Dockerized E2E tests with GitHub Action integration.
* Fix   - Handle DHL live rates notice creation and deletion errors.

= 1.25.10 - 2021-03-24 =
* Add   - Add an endpoint for shipping label creation eligibility and share code for store eligibility.
* Fix   - Shipping validation notice shown when no address entered.
* Tweak - Stop retrying to fetch /services when authentication fails on connect server.

= 1.25.9 - 2021-03-17 =
* Add   - WC Admin notice about DHL live rates.
* Add	- Live rates section in settings page.
* Tweak - Cleanup stripe functionality.
* Tweak - Display better errors on checkout page when address fields are missing / invalid.
* Tweak - Refresh on status page does not reload page.
* Fix   - UPS invoice number allows numbers and letters.
* Add 	- Tracks shipping services used at checkout.
* Add   - Update the existing endpoint `POST /connect/packages` to create shipping label packages, and add an endpoint `PUT /connect/packages` to update shipping label packages.
* Fix   - Only display shipping validation errors on the cart or checkout pages.
* Tweak - Removes deprecated Jetpack constant JETPACK_MASTER_USER
* Fix   - Revert radio button dot offset in the "Create shipping label" modal.

= 1.25.8 - 2021-03-02 =
* Tweak - Add support for new Jetpack 9.5 data connection.
* Tweak - Change minimum Jetpack version support to Jetpack 7.5.

= 1.25.7 - 2021-02-09 =
* Fix   - Prevent error notices on checkout page load.
* Tweak - Highlight rate call usage over limit on WooCommerce Shipping settings page.
* Fix   - Connect carrier account link broken on subdirectory installs.
* Fix   - Position dot in the center of radio buttons in "Create shipping label".
* Fix   - Adjust radio button dot style in "Create shipping label" in high contrast mode on Windows.

= 1.25.6 - 2021-01-26 =
* Fix 	- Refreshes shipping methods after registering or removing carrier accounts.
* Tweak	- Changed rates response caching method from cache to transient.
* Tweak	- Update "WC Tested up to" version.

= 1.25.5 - 2021-01-11 =
* Fix	- Redux DevTools usage update.
* Add	- Display subscriptions usage.
* Add	- Subscription activation.
* Add 	- Uses same DHL logo for all registered DHL accounts.
* Tweak - Adds WCCom access token and site ID to connect server request headers.

= 1.25.4 - 2020-12-08 =
* Tweak - Remove Stripe connect functionality.
* Tweak - Remove unused method in shipping settings view.
* Fix	- Breaking behavior on account registration page.
* Add	- Allows registration of additional accounts.
* Tweak - Carrier description on dynamic carrier registration form.
* Fix   - Adjust documentation links.

= 1.25.3 - 2020-11-24 =
* Add   - Initial code for WooCommerce.com subscriptions API.
* Add   - Dynamic carrier registration form.
* Fix   - When adding "signature required" to some packages, prices were not updating.
* Add   - DHL Schedule Pickup link within order notes.
* Fix   - UI fix for input validation for package dimensions and weights.
* Fix   - Correct validation for UPS fields in Carrier Account connect form.
* Tweak - Add message to explain automated tax requires tax-exclusive product pricing.
* Fix   - Disable USPS refunds for untracked labels only.

= 1.25.2 - 2020-11-10 =
* Tweak - Add ZIP code validation to UPS(beta) signup form.
* Fix   - Issue with printing labels in some iOS devices through Safari.
* Fix   - Prevents warning when using PHP 5.5 or lesser
* Add   - Add new API end point to retrieve carrier registration requirements.
* Add   - Add composer command to run PHPUnit.
* Tweak - Update readme with DHL information.

= 1.25.1 - 2020-10-28 =
* Tweak - DHL refund days copy adjustment
* Tweak - Stop using deprecated Jetpack method is_development_mode().
* Fix   - Update carrier name in tracking notification email.
* Add   - Add pre-commit and pre-push git hooks for linting and unit tests.
* Add   - Disable refunds for USPS letters.

= 1.25.0 - 2020-10-13 =
* Fix   - UPS connect redirect prompt
* Fix   - Allow UPS label purchase without payment method
* Fix   - PHP implode arguments order
* Fix   - Validate insurance value as both string and number
* Tweak - Adjusted messaging on label pointers
* Tweak - Update carrier logo
* Tweak - Plugin rename
* Add   - Link to print the customs form for all shipments that need it
* Tweak - Add new icons and banners with new plugin name

= 1.24.3 - 2020-09-16 =
* Fix   - Asset paths incompatible with some hosts.
* Fix   - Select all posts checkbox not working.
* Fix   - Use of deprecated jQuery.load.
* Tweak - Updating carrier logo and tracking links

= 1.24.2 - 2020-09-03 =
* Fix   - Optional preloading for wc-admin install compatibility
* Fix   - Remove duplicate rate errors
* Fix   - Compatibility with WooCommerce order page install prompt
* Add   - Introduce 'wc_connect_meta_box_payload' filter for modifying order data
* Tweak - Update UPS failed connection error message

= 1.24.1 - 2020-08-19 =
* Tweak - Zip/Postcode/Postal code messaging consistency
* Fix   - Services management CSS table layout
* Fix   - Carrier "disconnect modal" layout
* Fix   - Primary button busy state updated to match color
* Fix   - Remove padding from notice bar
* Fix   - Add missing box in rate step for how much customer paid for shipping
* Tweak - Bump WP tested version to 5.5
* Fix   - Issue with dismiss modal popup blocking access to edit order

= 1.24.0 - 2020-07-30 =
* Fix   - PHP 7.4 notice for taxes at checkout.
* Add   - Carrier logos next to rates.
* Tweak - Remove spinner from create shipping label button
* Add   - Upgrade React to 16.13
* Add   - Optimize bundle
* Fix   - Fix svg images not showing on dev
* Fix   - Fix 404 taxjar.js on new order page
* Add   - Add e2e tests for toggling shipping label
* Add   - Add e2e tests for label refund
* Fix   - Show which nexus automatted taxes will work with and link to doc
* Fix   - Fix localization issues
* Tweak - Improve service listing readability
* Add   - Support UPS as a carrier (beta)

= 1.23.2 - 2020-06-12 =
* Fix   - Refund not possible on order page.

= 1.23.1 - 2020-06-10 =
* Tweak - Update WooCommerce compatibility to 4.2
* Fix	  - Taxjar broken in admin's new order page
* Fix   - Tax recalculation on admin order screen missing street address.

= 1.23.0 - 2020-04-08 =
* Fix   - Hide paper selection until valid payment method is selected.
* Tweak - Shipping banner wording improvements.
* Add   - Link to carrier's schedule pickup page.
* Add   - Improved shipping service feature descriptions.
* Add   - Option to mark order complete when label is printed.

= 1.22.5 - 2020-03-17 =
* Add   - Admin asset API endpoint.
* Fix   - GB support for WC 4.0.
* Fix   - Jetpack staging check for PHP 5.3.
* Tweak - Bump WP tested version to 5.4.

= 1.22.4 - 2020-03-02 =
* Fix   - Stop using deprecated method Jetpack::is_staging_site() when Jetpack
8.1 is installed.

= 1.22.3 - 2020-01-22 =
* Add   - Preselect rate when there is only one rate available for given shipping configuration.
* Add   - Paper size selection into purchase modal sidebar and reprint modal which was previously removed.
* Add   - Show notice when WooCommerce is not installed or activated.
* Fix   - Use correct URL for variation products in Packages section.

= 1.22.2 - 2019-12-10 =
* Fix   - Packages weight total value formatting.
* Fix   - Allow fulfillment flow redo.

= 1.22.1 - 2019-11-14 =
* Fix   - Remove nuisance admin notification.

= 1.22.0 - 2019-11-14 =
* Add   - Display notices when base country/currency mismatches.
* Add   - Friendlier error codes when the API service is down.
* Add   - Introduce custom package setting on the label screen.
* Add   - Shipping Summary to the sidebar on the label screen.
* Add   - Shipping rates additional data - tracking, delivery time, signatures.
* Add   - Tracking modal.
* Add   - Ability to recreate labels for individual packages that have been refunded in a multi-label package scenario.
* Fix   - Deprecation notices for PHP 7.4.
* Fix   - Quantity is not being taken into account for the notice "n items are ready for shipment".
* Tweak - Credit card no longer required to configure shipping label.
* Tweak - Moved "Create Shipping Label" button from sidebar to the top of the order screen.
* Tweak - Preselect last used package on the label screen.
* Tweak - Remove rates from the dropdown and list them on the page after a package is selected.
* Tweak - Remove redundant "Paper Size" from the label screen since it's configurable from the Settings screen.
* Tweak - Show create label button in 'busy' state until data loads.
* Tweak - UX improvements for the label screen.

= 1.21.1 =
* Update WooCommerce compatibility to 3.7
* Support namespaced Jetpack methods

= 1.21.0 =
* Update WordPress compatibility to 5.2
* When there's only one credit card available, select it as the default for purchases
* Add ability to specify payment method during label purchase to enable choosing a credit card during purchase in the future

= 1.20.0 =
* Update WooCommerce compatibility to 3.6
* Improved wording for the Stripe Connect UI when disconnected
* Improve detection of when a Stripe account is connected
* Avoid overwriting Stripe keys after they've already been entered, and WooCommerce Services is connected for the first time
* Enable notices about WooCommerce Services to be displayed, for example when taxes were incorrectly calculated
* Update the 'print label' button text and style

= 1.19.0 =
* Communicate HTTPS requirement when connecting to Stripe
* Avoid showing 'Shipping options' at the bottom of the WooCommerce Services shipping settings page
* Fix a small misalignment of checkmarks in our checkboxes
* Fix the WC native tax override function by adding a missing return statement

= 1.18.0 =
* Add compatibility with WordPress 5.0
* Add compatibility with the WordPress.com eCommerce plan
* Add packing logs to the front-end (with debug enabled) and back-end (order detail screens)
* When purchasing a shipping label, allow addresses to be entered without verification
* Make the shipping label purchase process more robust, allowing retries when the label image failed to download
* UI improvements to the shipping label address form
* Allow connecting a Stripe account directly from the Stripe settings page
* Updated behavior of the shipping phone field in order to prevent conflicts with other plugins

= 1.17.1 =
* Fix the issue with disappearing shipping method settings when Stripe extension is enabled

= 1.17.0 =
* Restrict shipping methods to certain shipping classes
* Improve Stripe onboarding and show connected account in settings
* Fix label printing bug in iOS 12
* Make the HS Tariff Code optional for international shipments
* Hide ineligible package types for international shipments

= 1.16.1 =
* Clear shipping rates when changing package weight or signature requirement so they can be recalculated
* Correctly purchase a "First Class Envelope" or a "First Class Package Service" label depending if the package is an envelope or not

= 1.16.0 =
* Add international destinations support to USPS label printing
* Fix bug with Shipping Label pointer dismissal when creating a label for the first time
* Fix z-index issue with purchased shipping label ellipsis menu components
* Prevent checkout with invalid ZIP code when using Automated Taxes
* Fix tax calculation with multiple line items sharing product tax class
* Display native tax tables for custom rates
* Add street-level accuracy for tax calculation

= 1.15.1 =
* Fix the "Save changes" button staying disabled after failing to save the shipping method settings
* Remove emojis from the shipping rates messages, they were causing problems at checkout on sites with a non-default MySQL charset configuration

= 1.15.0 =
* Several bugfixes and improvements for Automated Taxes
* Immediately save the shipping method configuration when it's added to a shipping zone
* Allow to bypass the address verification when purchasing a shipping label
* Show which WordPress.com user can add a credit card for shipping label purchases
* Tweak the list of purchased labels for an order so it's less cluttered

= 1.14.1 =
* Fix error when adding shipping method price adjustments

= 1.14.0 =
* GDPR - Added WCS section to the privacy policy guide
* GDPR - Support for personal data export functionality
* GDPR - Support for personal data erasure functionality
* Minor changes to the settings page UI
* Fix the PHP warning on the Status page when logs are empty
* Fix log file retrieval on Status page to work with new WC 3.4 format that includes the date
* Add error message on manual service data refresh failure

= 1.13.3 =
* Fix broken admin after product featured in 'missing weight' notice is deleted

= 1.13.2 =
* Fix PHP Warning when the server cannot be reached for shipping rates or products are missing dimensions

= 1.13.1 =
* Fix PHP Warning for individually packed shipping rates

= 1.13.0 =
* Show customer selected shipping rate when purchasing a shipping label
* Add shipping labels to Reports
* Add USPS signature requirement support to label purchase
* Add link to view receipt for Shipping Labels
* Fix bug showing incorrect shipping label rates when changing packages
* Fix styling for purchasing shipping labels on mobile devices
* Prevent incompatible settings for Automated Taxes
* Fix duplicate "packages" section in Shipping Settings (Advanced Shipping Packages extension compatibility)
* Add "copy to clipboard" button to debug logs on Status page
* Improved error messaging
* Fix unnecessary shipping rates requests made in the Dashboard
* Fix PHP Warning when saving Tax settings
* Add caching to Shipping Rate requests

= 1.12.3 =
* Fixed PHP Fatal when PayPal Express Checkout has not fully initialized

= 1.12.2 =
* Fix some REST API calls being erroneously cached by certain hosting providers

= 1.12.1 =
* Fix missing file in 1.12.0 plugin release

= 1.12.0 =
* Add email receipts for purchased shipping labels
* Clean up Stripe account keys when deauthorized
* Fix bug in database migration script for older plugin versions
* Add "back to order" link when adding a credit card from order details
* Add frontend debugging messages for shipping rates
* Separate troubleshooting logs by feature (taxes, shipping, etc)
* Avoid making unnecessary automated tax requests
* Fix PHP Fatal bug in tax request error handling
* Integrate with WooCommerce Shipment Tracking extension
* Add Conditional Shipping and Payments compatibility

= 1.11.0 =
* Fix bug with TOS acceptance on WordPress Multisite
* Add PayPal Express Checkout payment authorization
* No longer require phone number for label purchases
* Fix bug with label print button on Firefox

= 1.10.1 =
* Fix bug with product variation names in Packaging description

= 1.10.0 =
* Add WooCommerce compatibility to plugin header
* Add ability to refresh server schemas from status page
* Fix tax calculations for subscription products
* Fix "limit usage to X items" coupon tax calculation
* Fix tax calculation for product bundles and add-ons
* Make phone number optional for shipping labels
* Only allow label printing for stores using USD
* Add label printing for stores in Puerto Rico

= 1.9.1 =
* Fix PHP Warning when Jetpack is disabled or missing
* Fix plain permalinks support

= 1.9.0 =
* Add tracking numbers to completed order emails
* Add USPS support for Puerto Rico
* Fix some tax bugs related to discount calculation and taxable address
* Updated Calypso-based UI

= 1.8.3 =
* Fix tax calculation in order total bug (WooCommerce 3.2+)

= 1.8.2 =
* Fix crash in the WooCommerce setup wizard when picking a supported country but an unsupported currency

= 1.8.1 =
* Fix label printing on iOS
* Fix NUX banner images and connect button

= 1.8.0 =
* Automatically configure live rates based on setup wizard choices
* Add automated tax calculation
* Add deferred Stripe account setup (if chosen in wizard)

= 1.7.1 =
* Support plain permalinks setting
* Fix PHP Fatal error when order contains a deleted product
* Fix use of non-SSL URLs in plugin header
* Fix some React console warnings
* Add asynchronous label purchase flow (will be used for future performance gains)

= 1.7.0 =
* Fix bug that allows accidental double click of label purchase button
* More immediate display of purchased labels on order detail screen
* Fix bug that showed settings screen links before Terms of Service are accepted
* No longer override package weight when changing type if specified by user
* Several small appearance tweaks (alignment, spacing)

= 1.6.2 =
* Fix spacing bug in purchased label display
* Fix bug with "create label" flow on WooCommerce 3.1.0

= 1.6.1 =
* Better UX when selecting shipping label payment method

= 1.6.0 =
* New streamlined onboarding process for plugin dependencies
* Better packaging workflow for orders not using live rates at checkout
* Improved discovery for label printing
* Fix bug with test label printing on status page

= 1.5.0 =
* Handle decoding errors retrieving older package data from orders
* Code cleanup - React 16 prep, better i18n support
* Fix PHP error when order has no packaging info
* Updated shipping label settings to include paper size
* Fix missing item bug with individually packaged products
* Fix display bug in "saved for later" item list
* Fix PHP error when using Jetpack in development mode

= 1.4.1 =
* Fix deleted product bug in labels UI
* Tweak Accept-Language header

= 1.4.0 =
* WooCommerce 3.0 compatibility
* Add test label print to system status page
* Remove shipping label preview
* Better handling of logged out users
* Add contextual plugin link to WooCommerce.com support form
* Fix bug when purchasing additional shipping labels without a page refresh
* Better NUX flow when Jetpack missing or inactive
* Temporarily remove label support for military addresses (requires customs form)
* Fix bug where predefined USPS packages weren't being sent in requests

= 1.3.2 =
* Hide destination address normalized order meta
* Log rate retrieval failures as errors with admin notice

= 1.3.1 =
* Fix compatibility bug with `mod_security`
* Update shipping address on order when corrected by API

= 1.3.0 =
* Fix label PDF viewing in IE, Microsoft Edge, and Firefox
* Fix label modal width issue in Internet Explorer 11
* Don't initially show red error highlights for missing label destination address
* Labels: Fix escaped HTML showing in product variation names
* Remove unused icon font, reducing CSS size
* Fix tax rate calculation for shipping
* Fix WP Super Cache label printing bug

= 1.2.0 =
* Improve label printing flow when origin or destination addresses need modification
* More descriptive text in pointer notice for setting up a WCS shipping method
* Remove plugin options during uninstall
* Prevent a label rates request when prerequisite data needs modification
* Include more information when JSON parsing fails
* Fix compatibility issues with Internet Explorer 11 and Microsoft Edge browsers
* Add version to plugin script and style includes
* Reduce size of CSS and JS files
* Fix incompatibility with existing WooCommerce USPS extension

= 1.1.1 =
* Update plugin support documentation link
* Fix bug related to tracking Terms of Service acceptance

= 1.1.0 =
* Fetch service schemas earlier - fix bug where no shipping methods displayed
* Fix bug related to tracking Terms of Service acceptance too early
* Display label number relative to order instead of ID
* Clear shipping rates cache when shipping settings change
* Ensure only the Jetpack connection owner can accept the Terms of Service
* Better visiblity into which shipping services are selected
* Add hint for first shipping method instance configuration

= 1.0.0 =
* Improve logging for rates request errors
* Fix a rates bug with individually packaged items
* Add an easier UI to select grouped shipping services
* Fix a database table prefix bug
* Fix a bug allowing an errant double-purchase of labels
* Always retrieve a refunded label's status
* Save the shipping label origin address for reuse earlier
* Add a fallback rate for USPS
* Recommend checking logs when errors occur
* Fix bug showing the wrong label purchase amount when requesting refunds
* Add tooltip explaining what shipping service price adjustments are
* Notify admins when products are missing dimensions when retrieving rates
* Improve terms of service message
* Improve user experience with form validation
* Move the settings under WooCommerce > Shipping
* Automatically enable flat rate packages when their corresponding service is enabled
* Improve the on-boarding experience for new users
* Show the card information being used to purchase labels
* Don't allow the labels flow to be entered without a selected payment method

= 0.9.5 =
* Add non-flat-rate predefined USPS packaging
* Add enhanced labels metabox with tracking info, reprint button, and refunds
* Allow payment methods to be managed in Jetpack "Developer Mode"
* Add messaging in labels dialog when no packages are configured
* Fix label preview display bug
* Fix a bug with variable product dimensions

= 0.9.4 =
* Fix a bug that caused a packages error in the labels UI when checkout used free shipping
* Tweak to the refund request success message
* Fix to avoid a WP_User error notice when editing payment methods
* Fix a React version error that was happening when editing shipping settings
* Only allow the primary Jetpack user to select payment method

= 0.9.3 =
* Improvements for phone validation error messages
* Increase the remote request timeout
* Adds USPS flat rate packages to the packaging manager
* Store the origin address when the label is purchased
* Clean-up a few React errors in the console
* Tweak the Jetpack status message
* Auto-accept address normalizations in labels UI if they are trivial
* Make sure notices appear on top of (not beneath) the labels UI
* CSS clean-up
* Fix an error where clicking outside the labels UI modal did not always close it
* Fix incorrect service counts in shipping method instance group headers
* Remove inappropriate target blank in certain anchors
* Fix timestamp presentation for label refunds
* Fix empty dimension handling and dimension casting

= 0.9.2 =
* Fix a fatal error that could happen on activation if WooCommerce or Jetpack were not present
* Send order shipping information with label rate requests to allow for pre-selection

= 0.9.1 =
* Update contributors to include a few we missed
* Add USPS flat-rate packaging support to the packaging manager
* Improvements to label preview
* Ensure self-help works even if connect is unable to download the service schemas
* Switch to PDF based multi-page labels

= 0.9.0 =
* Beta release
