# Calypso Development Values

Before diving into the code, here are few guiding principles we have followed when working on the project. We hope you will find them helpful for better understanding some of the decisions we’ve made.

## 0. We do it all for the user

Technology is not an end to itself, but is here to solve the problems for our users. Every technical decision we make directly impacts our users or helps us build specific features faster or better.

## 1. We optimize for iterating quickly

We aren’t trying to build software around a future we can’t predict. We don’t write code that we may not need. We know that no abstractions are better than bad abstractions.

## 2. We are here for the long haul

We understand that Calypso is here to stay and we put the extra effort into its design. We regularly act to make _the design simpler_ and more consistent. We invest in deep understanding of the tools, languages, and techniques we use. We learn constantly.

## 3. We don’t trust ourselves to be perfect

We get the product to our colleagues and users as early as possible. We review each other’s code. We show incomplete work and seek critical feedback. We let automated tools help us with tasks from checking coding standards to running tests. We keep an open mind and a beginner mindset.

## 4. We are in it together

We help each other, because no team or person can succeed if Calypso doesn’t succeed. We give candid feedback, regardless of hierarchy. We don’t own the code we write and the features we build. We are not afraid to work on areas outside of our direct responsibilities. We leave our egos at the door.

Previous: [Index](index.md) Next: [Hello, World!](hello-world.md)
