# Internationalization

If you add or modify text strings that will be displayed to users, consider whether they should be visible to non-English users.
In that case, wrap the strings in the `translate()` function from [i18n-calypso](../packages/i18n-calypso/README.md).
We extract the translatable texts wrapped inside the function and write a POT file that is exported to [translate.wordpress.com](https://translate.wordpress.com/projects/wpcom/).

Translated texts are [split into chunks loaded via JSON asynchronously](translation-chunks.md).

## Best Practices

The recommended way to use [i18n-calypso](../packages/i18n-calypso/README.md) in Calypso is through `useTranslate()` for function components and custom hooks, and `props.translate()` from the `localize()` higher-order component for class components.

Both methods ensure that the component subscribes to i18n events and re-renders when translation data is updated. This is crucial because translation data is loaded asynchronously, and components might render before translations are available. Otherwise, components could initially display English text and fail to update when translations are loaded.

Another key benefit of these methods is that they use the `i18n-calypso` instance [provided by context](https://github.com/Automattic/wp-calypso/blob/634d000d827b78e37e6c46458c928fdcdd917dd6/client/components/calypso-i18n-provider/index.tsx#L33), which may differ from the [default `i18n-calypso` instance in some cases](https://github.com/Automattic/wp-calypso/blob/bbc5cce795bff4f557f736bb79e65d9f72e2df27/client/controller/ssr-setup-locale.js#L15).

## String Freeze

If you're launching or updating a feature, and want to make sure that all new strings are translated before merging,
add the label [[Status] String Freeze](https://github.com/Automattic/wp-calypso/labels/%5BStatus%5D%20String%20Freeze) to your Pull Request.
Translatable texts from the `trunk` as well as branches with the `[Status] String Freeze` label are combined and imported into GlotPress.
In the example below, the translation coverage is 79% and the status check fails.

<img alt="Failed Coverage" src="https://user-images.githubusercontent.com/31164683/187543186-24274733-dcc3-433b-a784-a6a5899b2300.png">

Once the coverage reaches 100%, the status check passes.

## CSS for Multi-Directional Layouts

Use [CSS Logical Properties and Values](https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Logical_Properties) when
building layouts that support both left-to-right (LTR) and right-to-left (RTL) languages.
While the following is still supported:

```css
/* LTR-specific code */
.element {
	position: absolute;
	left: 0;
}
```

Better:

```css
/* Will render correctly in LTR or RTL */
.element {
	position: absolute;
	inset-inline-start: 0; /* inset-inline-start evaluates to left in LTR and right in RTL */
}
```

Similarly, the [text-align](https://developer.mozilla.org/en-US/docs/Web/CSS/text-align) property supports additional
values like `start` and `end`. For example, the following is still supported:

```css
/* LTR-specific code */
.is-right-align {
	text-align: right;
}
.is-left-align {
	text-align: left;
}
```

Better:

```css
/* Will render correctly in LTR or RTL */
.is-right-align {
	text-align: end; /* end evaluates to right in LTR and left in RTL */
}
.is-left-align {
	text-align: start; /* start evaluates to left in LTR and right in RTL */
}
```

Detailed information about the supported properties and values can be found at [css-tricks.com](https://css-tricks.com/building-multi-directional-layouts/).

### Caveats

CSS Logical Properties and Values is a [draft standard](https://drafts.csswg.org/css-logical/) (as of August 2022), and
some physical CSS properties, like [transform](https://developer.mozilla.org/en-US/docs/Web/CSS/transform), have no logical
equivalent. [Discussions](https://github.com/w3c/csswg-drafts/issues/1544) and [Proposals](https://github.com/w3c/fxtf-drafts/issues/311)
exist to standardize the behaviour, but have stalled.
In such instances, we can rely on the [Webpack RTL Plugin](https://github.com/Automattic/wp-calypso/tree/trunk/packages/webpack-rtl-plugin)
to transform the content to the correct location on the screen.

## Designing for Internationalization

### Leave room for text expansion

When text is translated from one language to another,
the length of the text can change. If a text container is too small or has a fixed size,
you might end up with truncated or overlapping text. While most users will decipher the meaning
of the text, this isn’t the best user experience.

Some tips:

- **Use flexible design** where possible.
- **Avoid fixed-sized containers** for buttons, menus, text boxes, etc.
- **Avoid narrow-width columns**. When longer text wraps, elements in the design may no longer line up.
- **Leave space for both width and height expansion**.
- **Build text containers that do not overlap other containers**.
- **Allow sufficient space in form and input fields**. Putting the form label above the form field allows for the most space.
- **Make sure truncated text doesn't alter meaning in different languages** if you can’t avoid truncating or clipping text.
  Think of the phrase “Media Assets” being truncated –– the result could be pretty bad…
- **Plan for an average of 40% text expansion.**

### Images, Graphics, Icons, and Other Media

- Choose images and icons that are applicable across all cultures or adapt the chosen imagery to the locale you are targeting.
- **Select neutral images and icons**.
- **Avoid images with embedded text**. Instead, put the text in the code. Using text-embedded images means having to create a separate image for each locale.
  This increases the amount of work and the number of assets to manage.
- **Be aware of different placement of symbols** in different languages, like currency or percentage signs.
- **Make sure media is localized or localizable**. Embedding a video or linking to a document that is only available in English creates a poor user experience.
  If the asset is only available in English, make that very clear to users or remove it from localized versions.

### Line Breaking, Sentence Splitting, and Word Wrapping

The general rule of thumb:

- **Keep sentences together**.
- **Wrap words** when possible.
  - Note that [word wrapping rules vary per language](https://www.w3.org/International/articles/typography/linebreak.en).
- **Avoid manual line breaks**, as this might split a sentence into 2 separate strings making it harder to translate.
  - **Use an inline break if you can’t avoid manual line breaks**, so both parts of the sentence are in the same translatable string.
- **Review and test translations in the final design**.

### Number, Date, and Time Formats

Don't make any assumptions about number, date, and time formats:

- Many regions use a 24-hour clock instead of AM and PM notation.
- Dates are not always displayed Month DD, YYYY. See [Date formats by country](https://en.wikipedia.org/wiki/Date_format_by_country).
- Weeks don’t always start on the same day of the week.
- Different locales use different number separators and decimal marks, i.e., **17,810.25** vs. **17 810,25** vs. **17.810,25**.
