# Code Reviews

## Why Are Code and UX Reviews Awesome?

Code reviews are an important part of the Calypso workflow. They help to keep code quality consistent, spread ownership of the code, and help every person working on Calypso improve over time. More importantly, each review instance is an opportunity to regularly act on making the design simpler, to identify common problems, and to refine or create new shared abstractions.

A good code review should catch:

1. Technical design issues.
2. UX / design problems.
3. Components or solutions that may already exist.
4. Problematic HTML and CSS at our scale.
5. Unnecessary copy and paste.
6. Code that doesn't adhere to our guidelines.
7. Inconsistencies with other code in the codebase.
8. Bugs.

Code reviews are also a great way for us to learn from each other in a distributed environment. Feel free to share techniques that you have picked up that make code simpler and more readable. At the end of the day, Calypso is our code, not the code of a set of individuals glued together.

Take a look at [our merge checklist](merge-checklist.md) for a list of things to test.

## The Code Review Mindset

Code reviews can be uncomfortable for both the author of the changes and also the reviewer. To make the process less painful, both the author and reviewer need to maintain a positive mindset. We're all working together to try to make Calypso and the WordPress user experience awesome.
