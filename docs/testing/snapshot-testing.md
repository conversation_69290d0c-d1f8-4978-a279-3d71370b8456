# Snapshot testing

This is an overview of [snapshot testing] and how to best use snapshot tests in Calypso.

## Broken snapshots

When a snapshot test fails, it just means that a component's rendering has changed. If that was
unintended, then the snapshot test just prevented a bug 😊

However, if the change was intentional, follow these steps to update the snapshot:

1. Run the following to update the snapshots:

   ```
   # --testPathPattern is optional but will be much faster by only running matching tests
   yarn run test-client -- --updateSnapshot --testPathPattern client/components
   ```

1. Review the diff and ensure the changes are expected and intentional

1. Commit

## What are snapshots?

Snapshots are just a representation of some data structure generated in our tests. Snapshots are
stored in files and committed alongside the tests. When the tests are run, the data structure
generated is compared with the snapshot on file.

It's very easy to make a snapshot:

```js
test( 'foobar test', () => {
	const foobar = { foo: 'bar' };
	expect( foobar ).toMatchSnapshot();
} );
```

This is the produced snapshot:

```js
exports[ `test foobar test 1` ] = `
  Object {
    "foo": "bar",
  }
`;
```

You should never interact with the snapshot directly, they are generated by tests. However you can
see that it's a representation the test data.

## Advantages

- Trivial and concise to write tests
- Protect against unintentional changes
- Simple to work with
- Reveal internal structures without running the application

## Disadvantages

- Snapshot tests are not expressive
- Only catch issues when changes are introduced
- Are problematic for anything non-deterministic

## Use cases

Snapshot are mostly targeted at component testing. They make us conscious of changes to a
component's structure which makes them _ideal_ for refactoring. If a snapshot is kept up to date
over the course of a series of commits, the snapshot diffs record the evolution of a component's
structure. Pretty cool 😎

```js
import { render, screen } from '@testing-library/react';
import LocaleSuggestions from '../locale-suggestions';
import MyComponent from '../my-component';

describe( 'MyComponent', () => {
	test( 'should render', () => {
		const { container } = render( <MyComponent /> );
		expect( container ).toMatchSnapshot();
	} );

	test( 'should render with locale suggestions if locale is provided', () => {
		const { container } = render( <MyComponent locale="es" /> );
		expect( container ).toMatchSnapshot();
		expect( screen.getByText( 'Also available in' ) ).toBeVisible();
	} );
} );
```

Reducer tests are also be a great fit for snapshots. The firsts snapshots introduced in Calypso were
actually [reducer tests](https://github.com/Automattic/wp-calypso/blob/e34d15f44c261fd7daa2212017e995883866d603/client/state/comments/test/selectors.js#L133-L142).
Reducers can be large, complex data structures that we don't want to change unexpectedly, exactly
what snapshots excel at!

<h2>Working with snapshots</h2>

You might be blindsided by CI tests failing when snapshots don't match. You'll need to
[update snapshots] if the changes are expected. The quick and dirty solution is to invoke Jest with
`--updateSnapshot`. In Calypso you can do that as follows:

```sh
yarn run test-client -- --updateSnapshot --testPathPattern path/to/match
```

`--testPathPattern` is not required, but specifying a path will avoid running the whole suite and
run much faster.

I strongly recommend that you keep `yarn run test-client:watch` in the background as you work. Jest
will run only the relevant tests for changed files, and when snapshot tests fail, just hit `u` to
update a snapshot!

## Pain points

Non-deterministic tests may not make consistent snapshots, so beware. When working with anything
random, time-based, or otherwise non-deterministic, snapshots will be problematic.

Connected components are tricky to work with. To snapshot a connected component you'll probably want
to export the unconnected component:

```
// my-component.js
export { MyComponent };
export default connect( mapStateToProps )( MyComponent );

// test/my-component.js
import { MyComponent } from '..';
// run those MyComponent tests…
```

The connected props will need need to be manually provided. This is a good opportunity to audit the
connected state.

## Best practices

If you're starting a refactor, snapshots are quite nice, you can add them as the first commit on a
branch and watch as they change.

Snapshots themselves don't express anything about what we expect. Snapshots are best used in
conjunction with other tests that do describe our expectations, like in the example above:

```
test( 'should render with locale suggestions if locale is provided', () => {
  const { container } = render( <MyComponent locale="es" /> );

  // Snapshot will catch unintended changes
  expect( container ).toMatchSnapshot();

  // This is what we actually expect to find in our test
  expect( screen.getByText( 'Also available in' ) ).toBeVisible();
} );
```

[snapshot testing]: https://facebook.github.io/jest/docs/en/snapshot-testing.html
[update snapshots]: https://facebook.github.io/jest/docs/en/snapshot-testing.html#updating-snapshots
