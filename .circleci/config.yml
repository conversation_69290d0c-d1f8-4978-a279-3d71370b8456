version: 2.1

orbs:
  win: circleci/windows@5.0.0

references:
  defaults: &defaults
    working_directory: ~/wp-calypso
    docker:
      - image: cimg/node:22.9.0
    environment:
      CIRCLE_ARTIFACTS: /tmp/artifacts
      CIRCLE_TEST_REPORTS: /tmp/test_results
      PLAYWRIGHT_SKIP_DOWNLOAD: 'true'
      SKIP_TSC: 'true'
      NODE_OPTIONS: --max-old-space-size=3072
      npm_config_cache: /home/<USER>/.cache/yarn
  desktop_defaults: &desktop_defaults
    working_directory: ~/wp-calypso

  setup-results-and-artifacts: &setup-results-and-artifacts
    name: Create Directories for Results and Artifacts
    command: |
      mkdir -p                                  \
        "$CIRCLE_ARTIFACTS/notifications-panel" \
        "$CIRCLE_ARTIFACTS/screenshots"         \
        "$CIRCLE_ARTIFACTS/wpcom-block-editor"  \
        "$CIRCLE_TEST_REPORTS/client"           \
        "$CIRCLE_TEST_REPORTS/eslint"           \
        "$CIRCLE_TEST_REPORTS/integration"      \
        "$CIRCLE_TEST_REPORTS/packages"         \
        "$CIRCLE_TEST_REPORTS/server"           \
        "$CIRCLE_TEST_REPORTS/e2ereports"       \
        "$HOME/jest-cache"

  # Git cache
  #
  # Calypso is a big repository with a lot of history. It can take a long time to do a full checkout.
  # By including the `.git` directory in the cache, we can speed things up by only needing to update
  # the local repository.
  #
  # We cache on the branch and revision, falling back to origin/HEAD, or any recent cache.
  #
  # More about the CircleCI cache: https://circleci.com/docs/2.0/caching
  restore-git-cache: &restore-git-cache
    name: Restore git cache
    keys:
      - v{{ .Environment.GLOBAL_CACHE_PREFIX }}-v2-git-{{ .Branch }}-{{ .Revision }}
      - v{{ .Environment.GLOBAL_CACHE_PREFIX }}-v2-git-{{ .Branch }}
      - v{{ .Environment.GLOBAL_CACHE_PREFIX }}-v2-git-trunk
      - v{{ .Environment.GLOBAL_CACHE_PREFIX }}-v2-git
  update-git: &update-git
    name: Update all branches
    command: git fetch --force origin
  save-git-cache: &save-git-cache
    name: Save git cache
    key: v{{ .Environment.GLOBAL_CACHE_PREFIX }}-v2-git-{{ .Branch }}-{{ .Revision }}
    paths:
      - '.git'

  # yarn cache
  #
  # yarn caches the modules it installs in ~/.cache/yarn.
  # We cache that cache to save time pulling modules from the network.
  #
  #
  # Some prior art:
  #   - https://github.com/Automattic/wp-calypso/pull/25431
  #   - https://github.com/Automattic/wp-calypso/pull/25487
  #   - https://github.com/Automattic/wp-calypso/pull/27180
  #
  # More about the CircleCI cache: https://circleci.com/docs/2.0/caching and https://circleci.com/docs/2.0/yarn/
  restore-yarn-cache: &restore-yarn-cache
    name: 'Restore yarn cache'
    keys:
      - v{{ .Environment.GLOBAL_CACHE_PREFIX }}-v3-yarn-cache-{{ arch }}-{{ checksum ".yarnrc.yml" }}
      - v{{ .Environment.GLOBAL_CACHE_PREFIX }}-v3-yarn-cache-{{ arch }}

  yarn-install: &yarn-install
    name: Install dependencies
    command: |
      source "$HOME/.nvm/nvm.sh"
      nvm use
      npm install -g yarn
      yarn install --immutable --inline-builds

  update-node: &update-node
    name: Update node
    command: |
      set +e
      set +x
      export NVM_DIR="$HOME/.nvm" && (
        git clone https://github.com/nvm-sh/nvm.git "$NVM_DIR"
        cd "$NVM_DIR"
        git checkout v0.35.3
      ) && \. "$NVM_DIR/nvm.sh" --no-use
      nvm install
      nvm use

  save-yarn-cache: &save-yarn-cache
    name: 'Save yarn cache'
    key: v{{ .Environment.GLOBAL_CACHE_PREFIX }}-v3-yarn-cache-{{ arch }}-{{ checksum ".yarnrc.yml" }}
    paths:
      # This is the default path when using enableGlobalCache:true
      - ~/.yarn/berry/cache

  desktop-cache-paths: &desktop-cache-paths
    - desktop/resource/certificates/win.p12
    - desktop/resource/certificates/mac.p12
  desktop-decrypt-certs: &desktop-decrypt-certs
    name: Decrypt desktop code signing certificates
    command: |
      openssl aes-256-cbc -md md5 -d -in desktop/resource/certificates/win.p12.enc -out desktop/resource/certificates/win.p12 -k "${CALYPSO_SECRETS_ENCRYPTION_KEY}"
  desktop-notify-github-success: &desktop-notify-github-success
    name: Notify Github Success
    when: on_success
    command: |
      source $HOME/.nvm/nvm.sh
      nvm use
      npm install -g yarn
      cd desktop && yarn ci:github:dismiss-review
  desktop-notify-github-failure: &desktop-notify-github-failure
    name: Notify Github Failure
    when: on_fail
    command: |
      source $HOME/.nvm/nvm.sh
      nvm use
      npm install -g yarn
      cd desktop && yarn ci:github:add-review

commands:
  store-artifacts-and-test-results:
    description: Stores artifacts and test results
    steps:
      - store_test_results:
          path: /tmp/test_results
      - store_artifacts:
          path: /tmp/test_results
      - store_artifacts:
          path: /tmp/artifacts

jobs:
  wp-desktop-assets:
    docker:
      - image: cimg/node:22.9.0-browsers
    <<: *desktop_defaults
    environment:
      VERSION: << pipeline.git.tag >>
      PLAYWRIGHT_SKIP_DOWNLOAD: 'true'
    working_directory: ~/wp-calypso
    steps:
      - checkout
      - attach_workspace:
          at: ~/wp-calypso
      - when:
          condition: << pipeline.git.tag >>
          steps:
            - run:
                name: Ensure package.json Version And Tag Match
                command: cd desktop/bin && node validate_tag.js $VERSION
      - run: *desktop-decrypt-certs
      - persist_to_workspace:
          root: ~/wp-calypso
          paths: *desktop-cache-paths
      - run: *desktop-notify-github-failure

  wp-desktop-mac:
    macos:
      xcode: '15.4.0'
    resource_class: macos.m1.medium.gen1
    shell: /bin/bash --login
    working_directory: /Users/<USER>/wp-calypso
    environment:
      CONFIG_ENV: release
      PLAYWRIGHT_SKIP_DOWNLOAD: 'true'
      # If, for testing purposes, you want to generate all artifacts in the CI of a PR, uncomment the following line,
      # and push a commit to your branch. Make sure to remove the commit before merging to trunk!
      #PR_DEBUG_RELEASE: 'true'
    steps:
      - checkout
      - attach_workspace:
          at: /Users/<USER>/wp-calypso
      - run:
          name: Set environment variables
          command: |
            if [[ "$PR_DEBUG_RELEASE" == "true" ]]; then
              echo "export RELEASE_BUILD=true" >> "$BASH_ENV"
              echo "export CSC_FOR_PULL_REQUEST=true" >> "$BASH_ENV"
              source "$BASH_ENV"
            fi

            if [[ $CIRCLE_TAG == desktop-v* ]]; then
              echo "export RELEASE_BUILD=true" >> "$BASH_ENV"
              source "$BASH_ENV"
            fi

            # If this is not a release build, only build the executable required for end-to-end testing.
            if [[ "$RELEASE_BUILD" != "true" ]]; then
              echo "export ELECTRON_BUILDER_ARGS='-c.mac.target=dir'" >> "$BASH_ENV"
              source "$BASH_ENV"
            fi

            echo RELEASE_BUILD=$RELEASE_BUILD
            echo CSC_FOR_PULL_REQUEST=$CSC_FOR_PULL_REQUEST
            echo ELECTRON_BUILDER_ARGS=$ELECTRON_BUILDER_ARGS
      - run: *update-node
      - restore_cache: *restore-yarn-cache
      - run: *yarn-install
      - save_cache: *save-yarn-cache
      - run:
          name: Build Desktop Mac
          no_output_timeout: 45m
          environment:
            USE_HARD_LINKS: 'false'
          command: |
            set +e
            source $HOME/.nvm/nvm.sh
            nvm use

            pushd desktop

            rbenv global $(cat .ruby-version) && rbenv rehash
            bundle install
            bundle exec fastlane configure_code_signing

            ELECTRON_BUILDER_ARGS=$ELECTRON_BUILDER_ARGS yarn run ci:build-mac
      - run:
          name: e2e Tests
          command: |
            source $HOME/.nvm/nvm.sh
            nvm use
            npm install -g yarn
            cd desktop && yarn run test:e2e
      - run:
          when: always
          name: Persist Mac Executable
          command: |
            # If this isn't a full artifact build, ensure to persist the built application for inspection
            if ! ls desktop/release/*.zip &>/dev/null
            then
              # Wildcard is to support both Intel and Apple Silicon builds.
              # Intel artifcats are in `/mac`, Apple Silicon is in `/mac-arm64`.
              ditto -ck --rsrc --sequesterRsrc desktop/release/mac* desktop/release/mac.app.zip
            fi
      - run:
          when: always
          name: Clean Up
          command: |
            rm -rf desktop/release/mac
            rm -rf desktop/release/mac-arm64
            rm desktop/release/builder-debug.yml
      - store_artifacts:
          when: always
          path: desktop/release
      - store_artifacts:
          when: always
          path: desktop/test/e2e/results/
      - persist_to_workspace:
          root: ~/wp-calypso
          paths:
            - desktop/release
      - run: *desktop-notify-github-success
      - run: *desktop-notify-github-failure

  # Publish all the artifacts generated by the previous wp-desktop- jobs to a
  # GitHub release and generate the release notes using the Git commits history
  wp-desktop-publish:
    docker:
      - image: cimg/go:1.20-node
    working_directory: /home/<USER>/wp-calypso
    environment:
      VERSION: << pipeline.git.tag >>
    steps:
      - checkout
      - attach_workspace:
          at: /home/<USER>/wp-calypso
      - run:
          name: Install Dependencies
          command: go install github.com/tcnksm/ghr@v0.16.0
      - run:
          name: Update wp-desktop repo README
          command: |
            node desktop/bin/github/update-desktop-repo-readme.js
      - run:
          name: Publish Github Release
          command: |
            SHORT_VERSION="${VERSION#desktop-}"
            echo "Publishing draft release for wp-desktop $SHORT_VERSION..."
            NAME="WP-Desktop ${SHORT_VERSION#?}"

            chmod +x desktop/bin/make-changelog.sh
            ./desktop/bin/make-changelog.sh > desktop/CHANGELOG.md

            ghr \
              --token "${WP_DESKTOP_SECRET}" \
              --username "${CIRCLE_PROJECT_USERNAME}" \
              --repository "wp-desktop" \
              --commitish "trunk" \
              --name "${NAME}" \
              --body "$(cat desktop/CHANGELOG.md)" \
              --delete \
              --draft \
              "${SHORT_VERSION}" desktop/release/

            echo "Publish complete"

workflows:
  version: 2
  wp-desktop:
    jobs:
      - wp-desktop-assets:
          filters:
            branches:
              only:
                - trunk
                - /circleci.*/
                - /release\/.*/
                - /desktop\/.*/
      - wp-desktop-mac:
          requires:
            - wp-desktop-assets
          filters:
            branches:
              only:
                - trunk
                - /circleci.*/
                - /release\/.*/
                - /desktop\/.*/

  wp-desktop-release:
    when: << pipeline.git.tag >>
    jobs:
      - wp-desktop-assets:
          filters:
            tags:
              only: /desktop-v.*/
      - wp-desktop-mac:
          requires:
            - wp-desktop-assets
          filters:
            tags:
              only: /desktop-v.*/

      - wp-desktop-publish:
          requires:
            - wp-desktop-mac
          filters:
            tags:
              only: /desktop-v.*/
