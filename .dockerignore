Dockerfile
Dockerfile.base
bin/build-docker.sh
/.tsc-cache
/.cache
.dockerignore
.git
.npm-debug.log*
.vscode
/.babel-cache
/build
/cached-requests.json
**/node_modules
/public
/packages/calypso-e2e/src/secrets/*.json
/client/server/bundler/*.json
/client/server/devdocs/components-usage-stats.json
/client/server/devdocs/proptypes-index.json
/client/server/devdocs/search-index.js

# Tests do not need to be included in build
/client/**/test/**

# Monorepo output
/apps/*/dist/
/apps/*/types/
/packages/*/dist/
