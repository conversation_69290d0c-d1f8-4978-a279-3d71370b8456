{"name": "wp-e2e-tests", "version": "0.0.1", "description": "Automated end to end acceptance tests for the Calypso project.", "homepage": "https://github.com/Automattic/wp-calypso", "license": "GPL-2.0-or-later", "author": "Automattic Inc.", "private": true, "repository": {"type": "git", "url": "git+https://github.com/Automattic/wp-calypso.git", "directory": "test/e2e"}, "bugs": "https://github.com/Automattic/wp-calypso/issues", "scripts": {"encrypt-secrets": "yarn workspace @automattic/calypso-e2e run encrypt-secrets", "decrypt-secrets": "yarn workspace @automattic/calypso-e2e run decrypt-secrets", "clean": "yarn clean:packages && yarn clean:output && rm -rf ./cookies && rm -rf ./node_modules", "clean:packages": "yarn workspace @automattic/jest-circus-allure-reporter clean && yarn workspace @automattic/calypso-e2e clean", "clean:output": "rm -rf ./allure-results && rm -rf ./results", "build": "yarn workspace @automattic/jest-circus-allure-reporter build && yarn workspace @automattic/calypso-e2e build", "test": "yarn jest", "test:mobile": "VIEWPORT_NAME=mobile yarn jest", "debug": "PWDEBUG=1 DEBUG=pw:api yarn run test", "debug:mobile": "PWDEBUG=1 DEBUG=pw:api yarn run test:mobile"}, "devDependencies": {"@automattic/calypso-eslint-overrides": "workspace:^", "@automattic/calypso-typescript-config": "workspace:^", "@babel/core": "^7.16.0", "webpack": "^5.63.0"}, "dependencies": {"@automattic/calypso-babel-config": "workspace:^", "@automattic/calypso-build": "workspace:^", "@automattic/calypso-e2e": "workspace:^", "@automattic/jest-circus-allure-reporter": "workspace:^", "@automattic/languages": "workspace:^", "@playwright/browser-chromium": "1.48.2", "@types/archiver": "^5.3.1", "archiver": "^5.3.0", "asana-phrase": "^0.0.8", "babel-jest": "^29.6.1", "babel-plugin-dynamic-import-node": "^2.3.0", "eslint": "^8.34.0", "eslint-plugin-jest": "^25.3.0", "esm": "^3.2.25", "form-data": "^4.0.0", "jest": "^29.6.1", "jest-docblock": "^29.4.3", "jest-environment-node": "^29.6.1", "jest-runner": "^29.6.1", "jest-runner-groups": "^2.2.0", "jest-teamcity": "^1.12.0", "lodash": "^4.17.20", "mailosaur": "^8.4.0", "playwright": "1.48.2", "postcss": "^8.3.11"}}