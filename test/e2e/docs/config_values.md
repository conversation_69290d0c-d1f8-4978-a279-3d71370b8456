Return to [Top Page](../README.md).

# Config Values

This list may be out of date as configuration value are added/removed.

When in doubt, the values contained within the `config/*.json` files should be taken as authoritative.

| Name                       | Description                                                                                                                                                                                                                                              | Example                                                                                                                                                                     | Required                        | Store in file? |
| -------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------- | -------------- |
| accounts                   | A JSON list of wordpress.com account information (as JSON objects).                                                                                                                                                                                      | [ { "username": "username", "password": "password", "site": "site.wordpress.com", "features": [] }, { "email": "<EMAIL>", "features": [ "passwordless" ] } ] | No                              | **NO**         |
| afterHookTimeoutMS         | This is the maximum total time in milliseconds that an after test hook can take including capturing the screenshots                                                                                                                                      | 20000                                                                                                                                                                       | Yes                             | Yes            |
| artifacts                  | The directory structure for test artifacts (video, screenshots, logs).                                                                                                                                                                                   | { 'screenshots': 'screenshots'}                                                                                                                                             | Yes                             | Yes            |
| browser                    | The browser to use: either `firefox` or `chrome`                                                                                                                                                                                                         | `chrome`                                                                                                                                                                    | Yes                             | Yes            |
| calypsoBaseURL             | The home page for calypso                                                                                                                                                                                                                                | <https://wordpress.com>                                                                                                                                                     | Yes                             | Yes            |
| calypsoDocker              | A boolean indicating whether the tests will be run against a local Calypso Docker instance (required to ensure login works)                                                                                                                              | true                                                                                                                                                                        | No                              | Yes            |
| closeBrowserOnComplete     | Specifies whether to close the browser window when the tests are done                                                                                                                                                                                    | `true`                                                                                                                                                                      | Yes                             | Yes            |
| domainsInboxId             | This is an inbox id from mailosaur used for **domains** email testing                                                                                                                                                                                    | sad34id44ss                                                                                                                                                                 | Yes                             | **NO**         |
| emailPrefix                | A string to stick on the beginning of the e-mail addresses used for invites and signups                                                                                                                                                                  | username                                                                                                                                                                    | No                              | **NO**         |
| explicitWaitMS             | The explicit wait time in milliseconds to wait for an element to appear - for example a widget loading data via an API                                                                                                                                   | 10000                                                                                                                                                                       | Yes                             | Yes            |
| highlightElements          | Boolean to indicate whether to visually highlight elements being interacted with on the page                                                                                                                                                             | true                                                                                                                                                                        | No                              | Yes            |
| inviteInboxId              | This is an inbox id from mailosaur used for **invite** email testing                                                                                                                                                                                     | sad34id44ss                                                                                                                                                                 | Yes                             | **NO**         |
| locale                     | Default locale to be used unless overridden.                                                                                                                                                                                                             | en                                                                                                                                                                          | Yes                             | Yes            |
| mochaTimeoutMS             | This is the maximum total time in milliseconds a single mocha end to end test can take to finish - otherwise it will time out.                                                                                                                           | 120000                                                                                                                                                                      | Yes                             | Yes            |
| privateSiteForInvites      | This is a wordpress.com **private** site that is used for testing invitations                                                                                                                                                                            | e2eflowtestingprivate.wordpress.com                                                                                                                                         | Yes                             | **NO**         |
| proxy                      | The type of proxy to use: either `system` to use whatever your system is configured to use, or `direct` to use no proxy. Also supports `charles` to send web traffic through the [Charles Proxy](https://www.charlesproxy.com/) app for troubleshooting. | `direct`                                                                                                                                                                    | Yes                             | Yes            |
| publicizeTwitterAccount    | This is the name of the test twitter account connected to your test username                                                                                                                                                                             | @endtoendtesting                                                                                                                                                            | Yes                             | **NO**         |
| reportWarningsToSlack      | Specifies whether warnings should be reported to Slack - should be used for CI builds                                                                                                                                                                    | `false`                                                                                                                                                                     | Yes                             | Yes            |
| restApiApplication         | A JSON object with your [WordPress REST API app](https://developer.wordpress.com/apps/) client ID, redirect URI, and client secret                                                                                                                       | {"client_id": "YOUR_CLIENT_ID", "redirect_uri": "YOUR_REDIRECT_URI", "client_secret": "YOUR CLIENT_SECRET"}                                                                 | Yes (for REST API scripts only) | **NO**         |
| sauceConfigurations        | Config values for launching browsers on Sauce Labs                                                                                                                                                                                                       | `{ "osx-chrome": { "browserName": "chrome", "platform": "OS X 10.11", "screenResolution": "2048x1536", "version": "50.0" } }`                                               | Yes (if using Sauce)            | Yes            |
| saveAllScreenshots         | Whether screenshots should be saved for all steps, including those that pass                                                                                                                                                                             | `false`                                                                                                                                                                     | Yes                             | Yes            |
| signupInboxId              | This is an inbox id from mailosaur used for **signup** email testing                                                                                                                                                                                     | sad34id44ss                                                                                                                                                                 | Yes                             | **NO**         |
| slackChannelForScreenshots | String name (including the `#`) of the channel to receive screenshots                                                                                                                                                                                    | #e2eflowtesting-notif                                                                                                                                                       | No                              | Yes            |
| slackHook                  | This is a Slack incoming webhook where notifications are sent for test status (<https://my.slack.com/services/new/incoming-webhook> -- requires Slack login)                                                                                             | <https://hooks.slack.com/services/XXXXXXXX/XXXXXXXXX/XXXXXXXXXXXXXXXXXXXXXXXX>                                                                                              | No                              | **NO**         |
| slackTokenForScreenshots   | This is a Slack token used for uploading screenshots (<https://api.slack.com/custom-integrations/legacy-tokens> -- requires Slack login)                                                                                                                 | XXXX-XXXXXXXXXX-XXXXXXXXXX-XXXXXXXXXXX-XXXXXXXXXX                                                                                                                           | No                              | **NO**         |
| startAppTimeoutMS          | This is the maximum total time in milliseconds that the app can take to start for mobile testing - this is different from test time as we want it to fail fast                                                                                           | 240000                                                                                                                                                                      | Yes (for app testing only)      | Yes            |
| startBrowserTimeoutMS      | This is the maximum total time in milliseconds that the browser can take to start - this is different from test time as we want it to fail fast                                                                                                          | 30000                                                                                                                                                                       | Yes                             | Yes            |
| storeSandboxCookieValue    | This is a secret cookie value used for testing payments                                                                                                                                                                                                  |                                                                                                                                                                             | No                              | **NO**         |
| testAccounts (deprecated)  | A JSON object with username/password pairs assigned to keynames for easy retrieval. The necessary accounts can be found in the config/local.example.json file.                                                                                           | {"defaultUser": ["username1","password1"], "multiSiteUser": ["username2","password2"] }                                                                                     | No                              | **NO**         |
| testPassword               | This is the password for the test WordPress.com account                                                                                                                                                                                                  | testpassword\$\$\$%###                                                                                                                                                      | Yes                             | **NO**         |
| testPasswordJetpack        | This is the password for the test WordPress.com account **that has a single jetpack site**                                                                                                                                                               | testpassword\$\$\$%###                                                                                                                                                      | Yes                             | **NO**         |
| testPasswordMultiSite      | This is the password for the test WordPress.com account **that has multiple sites**                                                                                                                                                                      | testpassword\$\$\$%###                                                                                                                                                      | Yes                             | **NO**         |
| testSiteForInvites         | This is wordpress.com site that is used for testing invitations                                                                                                                                                                                          | e2eflowtesting.wordpress.com                                                                                                                                                | Yes                             | **NO**         |
| testUserName               | This is an existing test WordPress.com account for testing purposes - this account should have a **single** site                                                                                                                                         | testuser123                                                                                                                                                                 | Yes                             | **NO**         |
| testUserNameJetpack        | This is an existing test WordPress.com account for testing purposes **that has a single jetpack site**                                                                                                                                                   | testuser123                                                                                                                                                                 | Yes                             | **NO**         |
| testUserNameMultiSite      | This is an existing test WordPress.com account for testing purposes **that has multiple sites**                                                                                                                                                          | testuser123                                                                                                                                                                 | Yes                             | **NO**         |
| viewportName               | Default viewport name to be used unless overridden.                                                                                                                                                                                                      | desktop                                                                                                                                                                     | Yes                             | Yes            |
| viewportSize               | Viewport size specified as width and height in pixels for each viewportName that is supported.                                                                                                                                                           | desktop                                                                                                                                                                     | No                              | Yes            |

<!-- When adding new rows, run the following command to sort the rows by alphabetical order:

cd test/e2e/docs
head -n 49  config_values.md | tail +9 | sort --field-separator=\| --key=1

Adjust the value of `head -n <x>` to be the last row of the table to be sorted.
Adjust the value of `tail +x` to be the first row of the table to be sorted.

 -->
