<div style="width: 45%; float:left" align="left"><p></p> </div>
<div style="width: 5%; float:left" align="center"><a href="./../README.md">Top</a></div>
<div style="width: 45%; float:right"align="right"><a href="./setup.md">Setup --></a> </div>

<br><br>

# Overview

<!-- TOC -->

- [Overview](#overview)
  - [What is this?](#what-is-this)
  - [Our Goals](#our-goals)
  - [What is tested?](#what-is-tested)

<!-- /TOC -->

## What is this?

End-to-end tests (`e2e` for short) describes the automated functional tests that simulate user interaction with the system under test.

A well-maintained suite of e2e tests, in conjunction with CI (continuous integration) pipelines will ensure that regressions to key user flows are caught as early as possible in the development cycle.

## Our Goals

To accelerate development by being a force for continuous improvement, and help identify, prioritize and mitigate bottlenecks from the system.

<sup>(taken from Quality Squad internal memo)</sup>

## What is tested?

These end-to-end test focus on several key areas:

- Calypso features.
- interaction between Calypso and Gutenberg editor.
- internationalization and localization.
- Gutenberg blocks added by WordPress.com.
