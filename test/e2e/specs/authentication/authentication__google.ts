/**
 * @group authentication
 *
 * Google blocks Chrome-based browsers that are controlled via automation
 * from performing login to their services.
 * The workaround is to use a non-Google browser, such as Firefox.
 * See: https://stackoverflow.com/questions/********/automation-google-login-with-python-and-selenium-shows-this-browser-or-app-may
 * @browser firefox
 */

import { LoginPage, SecretsManager, GoogleLoginPage, TOTPClient } from '@automattic/calypso-e2e';
import { <PERSON>, Browser } from 'playwright';

declare const browser: Browser;

describe( 'Authentication: Google', function () {
	const credentials = SecretsManager.secrets.testAccounts.googleLoginUser;

	let page: Page;
	let googlePopupPage: Page;
	let loginPage: LoginPage;
	let googleLoginPage: GoogleLoginPage;

	const totpClient = new TOTPClient( credentials.totpKey as string );
	let code: string;

	describe( 'WordPress.com', function () {
		beforeAll( async () => {
			page = await browser.newPage();
		} );

		it( 'Navigate to /login', async function () {
			loginPage = new LoginPage( page );
			await loginPage.visit();

			await page.waitForURL( /log-in/ );

			// Differently from the "WooCommerce" scenario below (see the comment below),
			// this doesn't seem to affect the click of the button, but I added it here for
			// consistency purposes.
			await page.waitForLoadState( 'networkidle' );
		} );

		it( 'Click on "Continue with Google" button', async function () {
			googlePopupPage = await loginPage.clickLoginWithGoogle();
			await googlePopupPage.waitForURL( /accounts\.google\.com/ );
		} );

		it( 'Enter Google username', async function () {
			await googlePopupPage.waitForURL( /identifier/ );

			googleLoginPage = new GoogleLoginPage( googlePopupPage );
			await googleLoginPage.enterUsername( credentials.username );
			await googleLoginPage.clickButton( 'Next' );
		} );

		it( 'Enter Google password', async function () {
			await googlePopupPage.waitForURL( /challenge/ );

			await googleLoginPage.enterPassword( credentials.password );
			await googleLoginPage.clickButton( 'Next' );
		} );

		it( 'Enter 2FA challenge if required - Challenge 1', async function () {
			code = totpClient.getToken();

			await googleLoginPage.enter2FACode( code );
			await googleLoginPage.clickButton( 'Next' );
		} );

		// In a game of cat and mouse, Google now appears to require
		// two 2FA challege for our `googleLoginUser` account.
		// It is not known why, but the most likely explanation is that
		// this spec has tripped something in the Google backend.
		// The result being the user is required to enter two sets of
		// TOTP codes, and because the TOTP codes regenerate every 30s
		// this means the spec has to wait until the TOTP code updates.
		// This behavior was confirmed manually by @worldomonation as well.
		it( 'Enter 2FA challenge if required - Challenge 2', async function () {
			await page.waitForLoadState( 'networkidle' );
			// Check if the unique text indicating the second 2FA challenge is present
			const isSecondChallengePresent = await googleLoginPage.isVisible( 'text="Verify it’s you"' );

			if ( ! isSecondChallengePresent ) {
				return;
			}

			// Wait until the TOTP code generated by the client
			// changes, meaning the 30s window has rolled over.
			while ( totpClient.getToken() === code ) {
				console.log(
					`Google Authentication: second 2FA challenge encountered, waiting for TOTP code to change from ${ code }`
				);
				await new Promise( ( resolve ) => setTimeout( resolve, 1000 ) );
			}

			code = totpClient.getToken();

			await googleLoginPage.enter2FACode( code );
			await googleLoginPage.clickButton( 'Next' );
		} );

		it( 'Click the "Continue" button in the login confirmation screen', async function () {
			const googlePopupPageClosePromise = googlePopupPage.waitForEvent( 'close' );
			// The next screen has a prompt that asks the user to confirm the login by clicking 'Continue'.
			await googleLoginPage.clickButton( 'Continue' );
			// The popup should close after clicking the button,
			// if it does not, something has gone wrong.
			await googlePopupPageClosePromise;
		} );

		it( 'Redirected to /home upon successful login', async function () {
			await page.waitForURL( /.*\/home\/.*/ );
		} );
	} );

	describe( 'WooCommerce', function () {
		beforeAll( async () => {
			page = await browser.newPage();
		} );

		it( 'Navigate to /login', async function () {
			loginPage = new LoginPage( page );
			await loginPage.visit( {
				path: SecretsManager.secrets.wooLoginPath,
			} );

			await page.waitForURL( /log-in/ );

			// This is needed or PW won't click the "Continue with Google" 99% of the time.
			await page.waitForLoadState( 'networkidle' );
		} );

		it( 'Click on "Continue with Google" button', async function () {
			googlePopupPage = await loginPage.clickLoginWithGoogle();
			await googlePopupPage.waitForURL( /accounts\.google\.com/ );
		} );

		it( 'Enter Google username', async function () {
			await googlePopupPage.waitForURL( /identifier/ );

			googleLoginPage = new GoogleLoginPage( googlePopupPage );
			await googleLoginPage.enterUsername( credentials.username );
			await googleLoginPage.clickButton( 'Next' );
		} );

		it( 'Enter Google password', async function () {
			await googlePopupPage.waitForURL( /challenge/ );

			await googleLoginPage.enterPassword( credentials.password );
			await googleLoginPage.clickButton( 'Next' );
		} );

		it( 'Enter 2FA challenge if required - Challenge 1', async function () {
			// Wait until the TOTP code generated by the client
			// changes, meaning the 30s window has rolled over.
			while ( totpClient.getToken() === code ) {
				console.log(
					`Google Authentication: second 2FA challenge encountered, waiting for TOTP code to change from ${ code }`
				);
				await new Promise( ( resolve ) => setTimeout( resolve, 1000 ) );
			}

			code = totpClient.getToken();

			await googleLoginPage.enter2FACode( code );
			await googleLoginPage.clickButton( 'Next' );
		} );

		// In a game of cat and mouse, Google now appears to require
		// two 2FA challege for our `googleLoginUser` account.
		// It is not known why, but the most likely explanation is that
		// this spec has tripped something in the Google backend.
		// The result being the user is required to enter two sets of
		// TOTP codes, and because the TOTP codes regenerate every 30s
		// this means the spec has to wait until the TOTP code updates.
		// This behavior was confirmed manually by @worldomonation as well.
		it( 'Enter 2FA challenge if required - Challenge 2', async function () {
			// Check if the unique text indicating the second 2FA challenge is present
			const isSecondChallengePresent = await googleLoginPage.isVisible( 'text="Verify it’s you"' );

			if ( ! isSecondChallengePresent ) {
				return;
			}

			// Wait until the TOTP code generated by the client
			// changes, meaning the 30s window has rolled over.
			while ( totpClient.getToken() === code ) {
				console.log(
					`Google Authentication: second 2FA challenge encountered, waiting for TOTP code to change from ${ code }`
				);
				await new Promise( ( resolve ) => setTimeout( resolve, 1000 ) );
			}

			await googleLoginPage.enter2FACode( totpClient.getToken() );
			await googleLoginPage.clickButton( 'Next' );
		} );

		it( 'Click the "Continue" button in the login confirmation screen', async function () {
			const googlePopupPageClosePromise = googlePopupPage.waitForEvent( 'close' );
			// The next screen has a prompt that asks the user to confirm the login by clicking 'Continue'.
			await googleLoginPage.clickButton( 'Continue' );
			// The popup should close after clicking the button,
			// if it does not, something has gone wrong.
			await googlePopupPageClosePromise;
		} );

		it( 'Redirected to woo.com upon successful login', async function () {
			await page.waitForURL( /.*woo\.com*/ );
		} );
	} );
} );
