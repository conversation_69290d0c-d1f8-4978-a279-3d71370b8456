{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "71174687d29929eebc9fbfb7c430573e", "packages": [], "packages-dev": [{"name": "automattic/jetpack-codesniffer", "version": "v2.8.0", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-codesniffer.git", "reference": "5efad0a3887ac8e3a18dcf0928255f57a396d0b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-codesniffer/zipball/5efad0a3887ac8e3a18dcf0928255f57a396d0b8", "reference": "5efad0a3887ac8e3a18dcf0928255f57a396d0b8", "shasum": ""}, "require": {"automattic/vipwpcs": "^2.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7", "mediawiki/mediawiki-codesniffer": "^41.0", "phpcompatibility/phpcompatibility-wp": "^2.1", "sirbrillig/phpcs-variable-analysis": "^2.10", "wp-coding-standards/wpcs": "^2.3"}, "require-dev": {"automattic/jetpack-changelogger": "^3.3.4", "wp-coding-standards/wpcs": "2023-01-04 as 2.3.1", "yoast/phpunit-polyfills": "1.0.4"}, "type": "phpcodesniffer-standard", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-codesniffer", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-codesniffer/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "2.8.x-dev"}}, "autoload": {"psr-4": {"Automattic\\Jetpack\\Sniffs\\": "Jetpack/Sniffs"}, "classmap": ["hacks/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Jetpack Coding Standards. Based on the WordPress Coding Standards, with some additions.", "keywords": ["codesniffer", "dev", "jetpack", "phpcs", "standards", "testing"], "support": {"source": "https://github.com/Automattic/jetpack-codesniffer/tree/v2.8.0"}, "time": "2023-06-07T18:33:19+00:00"}, {"name": "automattic/vipwpcs", "version": "2.3.4", "source": {"type": "git", "url": "https://github.com/Automattic/VIP-Coding-Standards.git", "reference": "b8610e3837f49c5f2fcc4b663b6c0a7c9b3509b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/VIP-Coding-Standards/zipball/b8610e3837f49c5f2fcc4b663b6c0a7c9b3509b6", "reference": "b8610e3837f49c5f2fcc4b663b6c0a7c9b3509b6", "shasum": ""}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^0.4.1 || ^0.5 || ^0.6.2 || ^0.7 || ^1.0", "php": ">=5.4", "sirbrillig/phpcs-variable-analysis": "^2.11.17", "squizlabs/php_codesniffer": "^3.7.1", "wp-coding-standards/wpcs": "^2.3"}, "require-dev": {"php-parallel-lint/php-console-highlighter": "^1.0.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcompatibility/php-compatibility": "^9", "phpcsstandards/phpcsdevtools": "^1.0", "phpunit/phpunit": "^4 || ^5 || ^6 || ^7"}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Contributors", "homepage": "https://github.com/Automattic/VIP-Coding-Standards/graphs/contributors"}], "description": "PHP_CodeSniffer rules (sniffs) to enforce WordPress VIP minimum coding conventions", "keywords": ["phpcs", "standards", "static analysis", "wordpress"], "support": {"issues": "https://github.com/Automattic/VIP-Coding-Standards/issues", "source": "https://github.com/Automattic/VIP-Coding-Standards", "wiki": "https://github.com/Automattic/VIP-Coding-Standards/wiki"}, "time": "2023-08-24T15:11:13+00:00"}, {"name": "composer/semver", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "3953f23262f2bff1919fc82183ad9acb13ff62c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/3953f23262f2bff1919fc82183ad9acb13ff62c9", "reference": "3953f23262f2bff1919fc82183ad9acb13ff62c9", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.4", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.3.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-04-01T19:23:25+00:00"}, {"name": "composer/spdx-licenses", "version": "1.5.8", "source": {"type": "git", "url": "https://github.com/composer/spdx-licenses.git", "reference": "560bdcf8deb88ae5d611c80a2de8ea9d0358cc0a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/spdx-licenses/zipball/560bdcf8deb88ae5d611c80a2de8ea9d0358cc0a", "reference": "560bdcf8deb88ae5d611c80a2de8ea9d0358cc0a", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.55", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Spdx\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "SPDX licenses list and validation library.", "keywords": ["license", "spdx", "validator"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/spdx-licenses/issues", "source": "https://github.com/composer/spdx-licenses/tree/1.5.8"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2023-11-20T07:44:33+00:00"}, {"name": "dealerdirect/phpcodesniffer-composer-installer", "version": "v0.7.2", "source": {"type": "git", "url": "https://github.com/Dealerdirect/phpcodesniffer-composer-installer.git", "reference": "1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Dealerdirect/phpcodesniffer-composer-installer/zipball/1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db", "reference": "1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.3", "squizlabs/php_codesniffer": "^2.0 || ^3.1.0 || ^4.0"}, "require-dev": {"composer/composer": "*", "php-parallel-lint/php-parallel-lint": "^1.3.1", "phpcompatibility/php-compatibility": "^9.0"}, "type": "composer-plugin", "extra": {"class": "Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin"}, "autoload": {"psr-4": {"Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.frenck.nl", "role": "Developer / IT Manager"}, {"name": "Contributors", "homepage": "https://github.com/Dealerdirect/phpcodesniffer-composer-installer/graphs/contributors"}], "description": "PHP_CodeSniffer Standards Composer Installer Plugin", "homepage": "http://www.dealerdirect.com", "keywords": ["PHPCodeSniffer", "PHP_CodeSniffer", "code quality", "codesniffer", "composer", "installer", "phpcbf", "phpcs", "plugin", "qa", "quality", "standard", "standards", "style guide", "stylecheck", "tests"], "support": {"issues": "https://github.com/dealerdirect/phpcodesniffer-composer-installer/issues", "source": "https://github.com/dealerdirect/phpcodesniffer-composer-installer"}, "time": "2022-02-04T12:51:07+00:00"}, {"name": "doctrine/instantiator", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^1.2", "phpstan/phpstan": "^1.9.4", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5.27", "vimeo/psalm": "^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/2.0.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-12-30T00:23:10+00:00"}, {"name": "mediawiki/mediawiki-codesniffer", "version": "v41.0.0", "source": {"type": "git", "url": "https://github.com/wikimedia/mediawiki-tools-codesniffer.git", "reference": "0ccdbc10ad819e86d7872175e1649037a3352000"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wikimedia/mediawiki-tools-codesniffer/zipball/0ccdbc10ad819e86d7872175e1649037a3352000", "reference": "0ccdbc10ad819e86d7872175e1649037a3352000", "shasum": ""}, "require": {"composer/semver": "3.3.2", "composer/spdx-licenses": "~1.5.2", "ext-json": "*", "ext-mbstring": "*", "php": ">=7.4.0", "squizlabs/php_codesniffer": "3.7.2", "symfony/polyfill-php80": "^1.26.0"}, "require-dev": {"mediawiki/mediawiki-phan-config": "0.12.0", "mediawiki/minus-x": "1.1.1", "php-parallel-lint/php-console-highlighter": "1.0.0", "php-parallel-lint/php-parallel-lint": "1.3.2", "phpunit/phpunit": "9.5.28"}, "type": "phpcodesniffer-standard", "autoload": {"psr-4": {"MediaWiki\\Sniffs\\": "MediaWiki/Sniffs/", "MediaWiki\\Sniffs\\Tests\\": "MediaWiki/Tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "MediaWiki CodeSniffer Standards", "homepage": "https://www.mediawiki.org/wiki/Manual:Coding_conventions/PHP", "keywords": ["codesniffer", "<PERSON><PERSON><PERSON>"], "support": {"source": "https://github.com/wikimedia/mediawiki-tools-codesniffer/tree/v41.0.0"}, "time": "2023-02-25T22:10:21+00:00"}, {"name": "myclabs/deep-copy", "version": "1.11.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "7284c22080590fb39f2ffa3e9057f10a4ddd0e0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/7284c22080590fb39f2ffa3e9057f10a4ddd0e0c", "reference": "7284c22080590fb39f2ffa3e9057f10a4ddd0e0c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3,<3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.11.1"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2023-03-08T13:26:56+00:00"}, {"name": "nikic/php-parser", "version": "v4.17.1", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "a6303e50c90c355c7eeee2c4a8b27fe8dc8fef1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/a6303e50c90c355c7eeee2c4a8b27fe8dc8fef1d", "reference": "a6303e50c90c355c7eeee2c4a8b27fe8dc8fef1d", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.17.1"}, "time": "2023-08-13T19:53:39+00:00"}, {"name": "phar-io/manifest", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "97803eca37d319dfa7826cc2437fc020857acb53"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/97803eca37d319dfa7826cc2437fc020857acb53", "reference": "97803eca37d319dfa7826cc2437fc020857acb53", "shasum": ""}, "require": {"ext-dom": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.3"}, "time": "2021-07-20T11:28:43+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "phpcompatibility/php-compatibility", "version": "9.3.5", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibility.git", "reference": "9fb324479acf6f39452e0655d2429cc0d3914243"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibility/zipball/9fb324479acf6f39452e0655d2429cc0d3914243", "reference": "9fb324479acf6f39452e0655d2429cc0d3914243", "shasum": ""}, "require": {"php": ">=5.3", "squizlabs/php_codesniffer": "^2.3 || ^3.0.2"}, "conflict": {"squizlabs/php_codesniffer": "2.6.2"}, "require-dev": {"phpunit/phpunit": "~4.5 || ^5.0 || ^6.0 || ^7.0"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5 || This Composer plugin will sort out the PHPCS 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://github.com/wimg", "role": "lead"}, {"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCompatibility/PHPCompatibility/graphs/contributors"}], "description": "A set of sniffs for PHP_CodeSniffer that checks for PHP cross-version compatibility.", "homepage": "http://techblog.wimgodden.be/tag/codesniffer/", "keywords": ["compatibility", "phpcs", "standards"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibility/issues", "source": "https://github.com/PHPCompatibility/PHPCompatibility"}, "time": "2019-12-27T09:44:58+00:00"}, {"name": "phpcompatibility/phpcompatibility-paragonie", "version": "1.3.2", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie.git", "reference": "bba5a9dfec7fcfbd679cfaf611d86b4d3759da26"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibilityParagonie/zipball/bba5a9dfec7fcfbd679cfaf611d86b4d3759da26", "reference": "bba5a9dfec7fcfbd679cfaf611d86b4d3759da26", "shasum": ""}, "require": {"phpcompatibility/php-compatibility": "^9.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7", "paragonie/random_compat": "dev-master", "paragonie/sodium_compat": "dev-master"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7 || This Composer plugin will sort out the PHP_CodeSniffer 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "lead"}, {"name": "<PERSON>", "role": "lead"}], "description": "A set of rulesets for PHP_CodeSniffer to check for PHP cross-version compatibility issues in projects, while accounting for polyfills provided by the Paragonie polyfill libraries.", "homepage": "http://phpcompatibility.com/", "keywords": ["compatibility", "paragonie", "phpcs", "polyfill", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie/issues", "source": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie"}, "time": "2022-10-25T01:46:02+00:00"}, {"name": "phpcompatibility/phpcompatibility-wp", "version": "2.1.4", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibilityWP.git", "reference": "b6c1e3ee1c35de6c41a511d5eb9bd03e447480a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibilityWP/zipball/b6c1e3ee1c35de6c41a511d5eb9bd03e447480a5", "reference": "b6c1e3ee1c35de6c41a511d5eb9bd03e447480a5", "shasum": ""}, "require": {"phpcompatibility/php-compatibility": "^9.0", "phpcompatibility/phpcompatibility-paragonie": "^1.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7 || This Composer plugin will sort out the PHP_CodeSniffer 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "lead"}, {"name": "<PERSON>", "role": "lead"}], "description": "A ruleset for PHP_CodeSniffer to check for PHP cross-version compatibility issues in projects, while accounting for polyfills provided by WordPress.", "homepage": "http://phpcompatibility.com/", "keywords": ["compatibility", "phpcs", "standards", "static analysis", "wordpress"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibilityWP/issues", "source": "https://github.com/PHPCompatibility/PHPCompatibilityWP"}, "time": "2022-10-24T09:00:36+00:00"}, {"name": "phpcsstandards/phpcsextra", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHPCSExtra.git", "reference": "746c3190ba8eb2f212087c947ba75f4f5b9a58d5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHPCSExtra/zipball/746c3190ba8eb2f212087c947ba75f4f5b9a58d5", "reference": "746c3190ba8eb2f212087c947ba75f4f5b9a58d5", "shasum": ""}, "require": {"php": ">=5.4", "phpcsstandards/phpcsutils": "^1.0.8", "squizlabs/php_codesniffer": "^3.7.1"}, "require-dev": {"php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcsstandards/phpcsdevcs": "^1.1.6", "phpcsstandards/phpcsdevtools": "^1.2.1", "phpunit/phpunit": "^4.5 || ^5.0 || ^6.0 || ^7.0"}, "type": "phpcodesniffer-standard", "extra": {"branch-alias": {"dev-stable": "1.x-dev", "dev-develop": "1.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHPCSExtra/graphs/contributors"}], "description": "A collection of sniffs and standards for use with PHP_CodeSniffer.", "keywords": ["PHP_CodeSniffer", "phpcbf", "phpcodesniffer-standard", "phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCSStandards/PHPCSExtra/issues", "source": "https://github.com/PHPCSStandards/PHPCSExtra"}, "time": "2023-09-20T22:06:18+00:00"}, {"name": "phpcsstandards/phpcsutils", "version": "1.0.8", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHPCSUtils.git", "reference": "69465cab9d12454e5e7767b9041af0cd8cd13be7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHPCSUtils/zipball/69465cab9d12454e5e7767b9041af0cd8cd13be7", "reference": "69465cab9d12454e5e7767b9041af0cd8cd13be7", "shasum": ""}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^0.4.1 || ^0.5 || ^0.6.2 || ^0.7 || ^1.0", "php": ">=5.4", "squizlabs/php_codesniffer": "^3.7.1 || 4.0.x-dev@dev"}, "require-dev": {"ext-filter": "*", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcsstandards/phpcsdevcs": "^1.1.6", "yoast/phpunit-polyfills": "^1.0.5 || ^2.0.0"}, "type": "phpcodesniffer-standard", "extra": {"branch-alias": {"dev-stable": "1.x-dev", "dev-develop": "1.x-dev"}}, "autoload": {"classmap": ["PHPCSUtils/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHPCSUtils/graphs/contributors"}], "description": "A suite of utility functions for use with PHP_CodeSniffer", "homepage": "https://phpcsutils.com/", "keywords": ["PHP_CodeSniffer", "phpcbf", "phpcodesniffer-standard", "phpcs", "phpcs3", "standards", "static analysis", "tokens", "utility"], "support": {"docs": "https://phpcsutils.com/", "issues": "https://github.com/PHPCSStandards/PHPCSUtils/issues", "source": "https://github.com/PHPCSStandards/PHPCSUtils"}, "time": "2023-07-16T21:39:41+00:00"}, {"name": "phpunit/php-code-coverage", "version": "9.2.29", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "6a3a87ac2bbe33b25042753df8195ba4aa534c76"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-code-coverage/zipball/6a3a87ac2bbe33b25042753df8195ba4aa534c76", "reference": "6a3a87ac2bbe33b25042753df8195ba4aa534c76", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.15", "php": ">=7.3", "phpunit/php-file-iterator": "^3.0.3", "phpunit/php-text-template": "^2.0.2", "sebastian/code-unit-reverse-lookup": "^2.0.2", "sebastian/complexity": "^2.0", "sebastian/environment": "^5.1.2", "sebastian/lines-of-code": "^1.0.3", "sebastian/version": "^3.0.1", "theseer/tokenizer": "^1.2.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.29"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-09-19T04:57:46+00:00"}, {"name": "phpunit/php-file-iterator", "version": "3.0.6", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/3.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2021-12-02T12:48:52+00:00"}, {"name": "phpunit/php-invoker", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/3.1.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T05:58:55+00:00"}, {"name": "phpunit/php-text-template", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T05:33:50+00:00"}, {"name": "phpunit/php-timer", "version": "5.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebastian<PERSON>mann/php-timer/tree/5.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:16:10+00:00"}, {"name": "phpunit/phpunit", "version": "9.6.13", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "f3d767f7f9e191eab4189abe41ab37797e30b1be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/f3d767f7f9e191eab4189abe41ab37797e30b1be", "reference": "f3d767f7f9e191eab4189abe41ab37797e30b1be", "shasum": ""}, "require": {"doctrine/instantiator": "^1.3.1 || ^2", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.10.1", "phar-io/manifest": "^2.0.3", "phar-io/version": "^3.0.2", "php": ">=7.3", "phpunit/php-code-coverage": "^9.2.28", "phpunit/php-file-iterator": "^3.0.5", "phpunit/php-invoker": "^3.1.1", "phpunit/php-text-template": "^2.0.3", "phpunit/php-timer": "^5.0.2", "sebastian/cli-parser": "^1.0.1", "sebastian/code-unit": "^1.0.6", "sebastian/comparator": "^4.0.8", "sebastian/diff": "^4.0.3", "sebastian/environment": "^5.1.3", "sebastian/exporter": "^4.0.5", "sebastian/global-state": "^5.0.1", "sebastian/object-enumerator": "^4.0.3", "sebastian/resource-operations": "^3.0.3", "sebastian/type": "^3.2", "sebastian/version": "^3.0.2"}, "suggest": {"ext-soap": "To be able to generate mocks based on WSDL files", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "9.6-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "security": "https://github.com/sebastian<PERSON>mann/phpunit/security/policy", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/9.6.13"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "time": "2023-09-19T05:39:22+00:00"}, {"name": "sebastian/cli-parser", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "442e7c7e687e42adc03470c7b668bc4b2402c0b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/442e7c7e687e42adc03470c7b668bc4b2402c0b2", "reference": "442e7c7e687e42adc03470c7b668bc4b2402c0b2", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/1.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T06:08:49+00:00"}, {"name": "sebastian/code-unit", "version": "1.0.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/1fc9f64c0927627ef78ba436c9b17d967e68e120", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/1.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:08:54+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/2.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T05:30:19+00:00"}, {"name": "sebastian/comparator", "version": "4.0.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "fa0f136dd2334583309d32b62544682ee972b51a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/comparator/zipball/fa0f136dd2334583309d32b62544682ee972b51a", "reference": "fa0f136dd2334583309d32b62544682ee972b51a", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/diff": "^4.0", "sebastian/exporter": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/4.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-09-14T12:41:17+00:00"}, {"name": "sebastian/complexity", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "739b35e53379900cc9ac327b2147867b8b6efd88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/739b35e53379900cc9ac327b2147867b8b6efd88", "reference": "739b35e53379900cc9ac327b2147867b8b6efd88", "shasum": ""}, "require": {"nikic/php-parser": "^4.7", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/2.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T15:52:27+00:00"}, {"name": "sebastian/diff", "version": "4.0.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "74be17022044ebaaecfdf0c5cd504fc9cd5a7131"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/diff/zipball/74be17022044ebaaecfdf0c5cd504fc9cd5a7131", "reference": "74be17022044ebaaecfdf0c5cd504fc9cd5a7131", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3", "symfony/process": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/4.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-05-07T05:35:17+00:00"}, {"name": "sebastian/environment", "version": "5.1.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "830c43a844f1f8d5b7a1f6d6076b784454d8b7ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/830c43a844f1f8d5b7a1f6d6076b784454d8b7ed", "reference": "830c43a844f1f8d5b7a1f6d6076b784454d8b7ed", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/5.1.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:03:51+00:00"}, {"name": "sebastian/exporter", "version": "4.0.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "ac230ed27f0f98f597c8a2b6eb7ac563af5e5b9d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/ac230ed27f0f98f597c8a2b6eb7ac563af5e5b9d", "reference": "ac230ed27f0f98f597c8a2b6eb7ac563af5e5b9d", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/4.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-09-14T06:03:37+00:00"}, {"name": "sebastian/global-state", "version": "5.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "bde739e7565280bda77be70044ac1047bc007e34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bde739e7565280bda77be70044ac1047bc007e34", "reference": "bde739e7565280bda77be70044ac1047bc007e34", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/5.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-02T09:26:13+00:00"}, {"name": "sebastian/lines-of-code", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "c1c2e997aa3146983ed888ad08b15470a2e22ecc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/lines-of-code/zipball/c1c2e997aa3146983ed888ad08b15470a2e22ecc", "reference": "c1c2e997aa3146983ed888ad08b15470a2e22ecc", "shasum": ""}, "require": {"nikic/php-parser": "^4.6", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/1.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-28T06:42:11+00:00"}, {"name": "sebastian/object-enumerator", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/5c9eeac41b290a3712d88851518825ad78f45c71", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/4.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:12:34+00:00"}, {"name": "sebastian/object-reflector", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:14:26+00:00"}, {"name": "sebastian/recursion-context", "version": "4.0.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1", "reference": "e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/4.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:07:39+00:00"}, {"name": "sebastian/resource-operations", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "0f4443cb3a1d92ce809899753bc0d5d5a8dd19a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/resource-operations/zipball/0f4443cb3a1d92ce809899753bc0d5d5a8dd19a8", "reference": "0f4443cb3a1d92ce809899753bc0d5d5a8dd19a8", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "support": {"issues": "https://github.com/sebastian<PERSON>mann/resource-operations/issues", "source": "https://github.com/sebastian<PERSON>mann/resource-operations/tree/3.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T06:45:17+00:00"}, {"name": "sebastian/type", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/type/zipball/75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7", "reference": "75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/3.2.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:13:03+00:00"}, {"name": "sebastian/version", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c6c1022351a901512170118436c764e473f6de8c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c6c1022351a901512170118436c764e473f6de8c", "reference": "c6c1022351a901512170118436c764e473f6de8c", "shasum": ""}, "require": {"php": ">=7.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/3.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T06:39:44+00:00"}, {"name": "sirbrillig/phpcs-variable-analysis", "version": "v2.11.17", "source": {"type": "git", "url": "https://github.com/sirbrillig/phpcs-variable-analysis.git", "reference": "3b71162a6bf0cde2bff1752e40a1788d8273d049"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sirbrillig/phpcs-variable-analysis/zipball/3b71162a6bf0cde2bff1752e40a1788d8273d049", "reference": "3b71162a6bf0cde2bff1752e40a1788d8273d049", "shasum": ""}, "require": {"php": ">=5.4.0", "squizlabs/php_codesniffer": "^3.5.6"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7 || ^1.0", "phpcsstandards/phpcsdevcs": "^1.1", "phpstan/phpstan": "^1.7", "phpunit/phpunit": "^4.8.36 || ^5.7.21 || ^6.5 || ^7.0 || ^8.0 || ^9.0", "sirbrillig/phpcs-import-detection": "^1.1", "vimeo/psalm": "^0.2 || ^0.3 || ^1.1 || ^4.24 || ^5.0@beta"}, "type": "phpcodesniffer-standard", "autoload": {"psr-4": {"VariableAnalysis\\": "VariableAnalysis/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A PHPCS sniff to detect problems with variables.", "keywords": ["phpcs", "static analysis"], "support": {"issues": "https://github.com/sirbrillig/phpcs-variable-analysis/issues", "source": "https://github.com/sirbrillig/phpcs-variable-analysis", "wiki": "https://github.com/sirbrillig/phpcs-variable-analysis/wiki"}, "time": "2023-08-05T23:46:11+00:00"}, {"name": "squizlabs/php_codesniffer", "version": "3.7.2", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "reference": "ed8e00df0a83aa96acf703f8c2979ff33341f879"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/ed8e00df0a83aa96acf703f8c2979ff33341f879", "reference": "ed8e00df0a83aa96acf703f8c2979ff33341f879", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "bin": ["bin/phpcs", "bin/phpcbf"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "lead"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/squizlabs/PHP_CodeSniffer", "keywords": ["phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/squizlabs/PHP_CodeSniffer/issues", "source": "https://github.com/squizlabs/PHP_CodeSniffer", "wiki": "https://github.com/squizlabs/PHP_CodeSniffer/wiki"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}], "time": "2023-02-22T23:07:41+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "87b68208d5c1188808dd7839ee1e6c8ec3b02f1b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/87b68208d5c1188808dd7839ee1e6c8ec3b02f1b", "reference": "87b68208d5c1188808dd7839ee1e6c8ec3b02f1b", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "34a41e998c2183e22995f158c581e7b5e755ab9e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/34a41e998c2183e22995f158c581e7b5e755ab9e", "reference": "34a41e998c2183e22995f158c581e7b5e755ab9e", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.1"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2021-07-28T10:34:58+00:00"}, {"name": "wp-coding-standards/wpcs", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/WordPress/WordPress-Coding-Standards.git", "reference": "7da1894633f168fe244afc6de00d141f27517b62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WordPress/WordPress-Coding-Standards/zipball/7da1894633f168fe244afc6de00d141f27517b62", "reference": "7da1894633f168fe244afc6de00d141f27517b62", "shasum": ""}, "require": {"php": ">=5.4", "squizlabs/php_codesniffer": "^3.3.1"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5 || ^0.6", "phpcompatibility/php-compatibility": "^9.0", "phpcsstandards/phpcsdevtools": "^1.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.6 || This Composer plugin will sort out the PHPCS 'installed_paths' automatically."}, "type": "phpcodesniffer-standard", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Contributors", "homepage": "https://github.com/WordPress/WordPress-Coding-Standards/graphs/contributors"}], "description": "PHP_CodeSniffer rules (sniffs) to enforce WordPress coding conventions", "keywords": ["phpcs", "standards", "wordpress"], "support": {"issues": "https://github.com/WordPress/WordPress-Coding-Standards/issues", "source": "https://github.com/WordPress/WordPress-Coding-Standards", "wiki": "https://github.com/WordPress/WordPress-Coding-Standards/wiki"}, "time": "2020-05-13T23:57:56+00:00"}, {"name": "yoast/phpunit-polyfills", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/Yoast/PHPUnit-Polyfills.git", "reference": "e6faedf5e34cea4438e341f660e2f719760c531d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Yoast/PHPUnit-Polyfills/zipball/e6faedf5e34cea4438e341f660e2f719760c531d", "reference": "e6faedf5e34cea4438e341f660e2f719760c531d", "shasum": ""}, "require": {"php": ">=5.4", "phpunit/phpunit": "^4.8.36 || ^5.7.21 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "require-dev": {"php-parallel-lint/php-console-highlighter": "^1.0.0", "php-parallel-lint/php-parallel-lint": "^1.4.0", "yoast/yoastcs": "^3.1.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.x-dev"}}, "autoload": {"files": ["phpunitpolyfills-autoload.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "Team Yoast", "email": "<EMAIL>", "homepage": "https://yoast.com"}, {"name": "Contributors", "homepage": "https://github.com/Yoast/PHPUnit-Polyfills/graphs/contributors"}], "description": "Set of polyfills for changed PHPUnit functionality to allow for creating PHPUnit cross-version compatible tests", "homepage": "https://github.com/Yoast/PHPUnit-Polyfills", "keywords": ["phpunit", "polyfill", "testing"], "support": {"issues": "https://github.com/Yoast/PHPUnit-Polyfills/issues", "security": "https://github.com/Yoast/PHPUnit-Polyfills/security/policy", "source": "https://github.com/Yoast/PHPUnit-Polyfills"}, "time": "2025-02-09T18:13:44+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": false, "prefer-lowest": false, "platform": {}, "platform-dev": {}, "platform-overrides": {"php": "8.2"}, "plugin-api-version": "2.6.0"}