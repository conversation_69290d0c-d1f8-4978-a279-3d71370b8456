#### cache image
#### This image is not pushed to any repository and it shouldn't be used as base image for any other docker build.
#### Its main goal is to create a `/calypso/.cache` that can be copied over other images that can benefit from a warm cache.
#### Note that yarn v3 cache lives in `/calypso/.yarn`
FROM node:22.9.0-bullseye-slim as cache
SHELL ["/bin/bash", "-o", "pipefail", "-c"]

ARG node_memory=8192
ARG commit_sha="(unknown)"
WORKDIR /calypso
ENV NPM_CONFIG_CACHE=/calypso/.cache
ENV PERSISTENT_CACHE=true
ENV GENERATE_CACHE_IMAGE=true
ENV PROFILE=true
ENV SKIP_TSC=true
ENV CALYPSO_ENV=production
ENV BUILD_TRANSLATION_CHUNKS=true
ENV NODE_OPTIONS=--max-old-space-size=$node_memory
ENV HOME=/calypso
ENV IS_CI=true
ENV COMMIT_SHA $commit_sha

COPY . .

RUN yarn --inline-builds \
	# Prime webpack caches, including sourcemaps.
	&& NODE_ENV=production SOURCEMAP=hidden-source-map yarn build-client

ENTRYPOINT [ "/bin/bash" ]

#### base image
#### This image can be used as a base image for other builds, or to test and build calypso.
FROM node:22.9.0-bullseye-slim as base
SHELL ["/bin/bash", "-o", "pipefail", "-c"]

ARG node_memory=8192
ARG user=calypso
ARG UID=1003

WORKDIR /calypso
ENV NPM_CONFIG_CACHE=/calypso/.cache
ENV NODE_OPTIONS=--max-old-space-size=$node_memory
ENV HOME=/calypso
ENV SKIP_TSC=true
ENV PLAYWRIGHT_SKIP_DOWNLOAD=true

# Add user calypso with uid 1003, give it sudo permissions
RUN apt-get update \
	&& apt-get install -y sudo zip jq curl git \
	&& adduser --uid $UID --disabled-password $user \
	&& echo "$user ALL=(ALL) NOPASSWD: ALL" > /etc/sudoers.d/$user \
	&& chmod 0440 /etc/sudoers.d/$user \
	&& chown $UID /calypso \
	# Remove unnecessary packages
	&& apt-get autoremove --purge \
	&& apt-get autoclean \
	&& rm -rf /var/lib/apt/lists/* \
	# Set bash as the default shell
	&& rm /bin/sh \
	&& ln -s /bin/bash /bin/sh

# Copy all other caches (webpack, babel, yarn...)
COPY --from=cache --chown=$UID /calypso/.cache /calypso/.cache
COPY --from=cache --chown=$UID /calypso/.yarn /calypso/.yarn

ENTRYPOINT [ "/bin/bash" ]

#### ci-e2e image
#### This image is used to run E2E tests.
FROM base as ci-e2e
SHELL ["/bin/bash", "-o", "pipefail", "-c"]

ENV PLAYWRIGHT_SKIP_DOWNLOAD=false
ENV DISPLAY=:99

RUN apt-get update \
	&& apt-get install -y --no-install-recommends \
		# For wp-calypso-test-results dashboard
		awscli \
		# Install non-Latin fonts
		fonts-noto-cjk \
		fonts-noto-core \
		# Other required packages
		git-restore-mtime \
		libasound2 \
		libatspi2.0-0 \
		libdbus-glib-1-2 \
		libgtk-3-0 \
		libgbm1 \
		libnspr4 \
		libnss3 \
		libsecret-1-0 \
		libx11-xcb1 \
		libxss1 \
		libxtst6 \
		openssl \
		xauth \
		xvfb \
	&& apt-get autoremove --purge \
	&& apt-get autoclean \
	&& rm -rf /var/lib/apt/lists/*

ENTRYPOINT [ "/bin/bash" ]

#### ci-wpcom image
#### This image is used to test and build WPCOM plugins in Calypso repo.
FROM base as ci-wpcom
SHELL ["/bin/bash", "-o", "pipefail", "-c"]

COPY --from=composer:latest /usr/bin/composer /usr/local/bin/composer
COPY --from=cache --chown=$UID /calypso/composer.* /calypso/

RUN apt-get update && \
	apt-get install -y apt-transport-https wget

# libffi6 is no longer available from apt as of bullseye.
RUN wget http://mirrors.kernel.org/ubuntu/pool/main/libf/libffi/libffi6_3.2.1-8_amd64.deb && \
	dpkg -i libffi6_3.2.1-8_amd64.deb && \
	wget https://packages.sury.org/php/apt.gpg -O /etc/apt/trusted.gpg.d/php-sury.gpg && \
	echo "deb https://packages.sury.org/php/ bullseye main" > /etc/apt/sources.list.d/php-sury.list && \
	apt-get update && \
 	apt-get upgrade -y && \
	apt-get install -y php7.4-cli php7.4-xml php7.4-mbstring docker-compose && \
	composer install && \
	# Install sentry-cli. We pin it to a version to make it easier to know at a
	# glance what is the version being used. It's important to keep the JS SDK and
	# the CLI updated to their latest stable versions.
	wget -O - https://sentry.io/get-cli/ | SENTRY_CLI_VERSION="2.18.1" bash

ENTRYPOINT [ "/bin/bash" ]
