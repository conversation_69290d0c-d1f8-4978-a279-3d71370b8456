name: QIT Tests
on:
  workflow_dispatch:
  pull_request:
permissions:
  pull-requests: write
jobs:
  qit_tests:
    name: QIT Tests
    runs-on: ubuntu-latest
    env:
      NO_COLOR: 1
      QIT_DISABLE_ONBOARDING: yes
    steps:
      - name: Set Git to use HTTPS instead of SSH
        run: git config --global url.https://github.com/.insteadOf git://github.com/
      - name: Checkout code
        uses: actions/checkout@v3
        with:
            submodules: recursive
      - uses: actions/setup-node@v2
        with:
          node-version-file: '.nvmrc'
      - name: Install NPM dependencies
        run: npm ci
      - name: Build plugin zip
        run: npm run build

      - name: Unzip woocommerce-services.zip
        run: unzip woocommerce-services.zip -d woocommerce-services

      - name: Re-zip woocommerce-services folder
        run: zip -r woocommerce-services.zip woocommerce-services

      - name: Require woocommerce/qit-cli
        run: composer require woocommerce/qit-cli
      - name: Add Partner
        run: ./vendor/bin/qit partner:add --user='${{ secrets.PARTNER_USER }}' --application_password='${{ secrets.PARTNER_SECRET }}'
      - name: Create results dir
        run: mkdir ${{ github.workspace }}/qit-results
      - name: Run activation test
        if: always()
        id: run-activation-test
        run: ./vendor/bin/qit run:activation woocommerce-services --zip=woocommerce-services.zip --wait > ${{ github.workspace }}/qit-results/qit-activation-results.txt
      - name: Echo activation results
        if: always()
        run: cat ${{ github.workspace }}/qit-results/qit-activation-results.txt
      - name: Run security test
        if: always()
        id: run-security-test
        run: ./vendor/bin/qit run:security woocommerce-services --zip=woocommerce-services.zip --wait > ${{ github.workspace }}/qit-results/qit-security-results.txt
      - name: Echo security results
        if: always()
        run: cat ${{ github.workspace }}/qit-results/qit-security-results.txt
      - name: Upload results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: qit-results
          path: ${{ github.workspace }}/qit-results/
