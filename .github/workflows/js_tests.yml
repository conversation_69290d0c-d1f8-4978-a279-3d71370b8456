name: JS Tests

on: [pull_request, workflow_dispatch]

jobs:
  js_build_and_test:
    name: JS Eslint and Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          submodules: recursive
      - uses: actions/setup-node@v2
        with:
          node-version: '10.16.0'
      - run: git config --global url.https://github.com/.insteadOf git://github.com/
      - run: npm ci
      - run: npm run eslint
      - run: npm run test
