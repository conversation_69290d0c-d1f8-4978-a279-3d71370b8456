name: <PERSON><PERSON> Tests

on: [pull_request, workflow_dispatch]

jobs:
  php_lint:
    name: PHP Syntax Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout latest WCS
        uses: actions/checkout@v2
        with:
          submodules: recursive
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '7.4'
      - name: Lint
        run: find . -type f -name "*.php" -exec php -l {} \;
  find_latest_versions:
    name: Get supported WP & WC versions
    runs-on: ubuntu-latest
    outputs:
      wc-versions: ${{ steps.get_wc_versions_for_matrix.outputs.wc-versions }}
      wp-versions: ${{ steps.get_wp_versions_for_matrix.outputs.wp-versions }}
    steps:
      - name: Checkout WCS&T
        uses: actions/checkout@v2
      - name: Get the 3 latest WP minor versions
        id: get_wp_versions_for_matrix
        run: echo "::set-output name=wp-versions::$(git ls-remote --tags --refs https://github.com/WordPress/WordPress.git | sed 's/.*refs\/tags\///' | php ./bin/get-latest-version.php)"
      - name: Get the 3 latest WC minor versions
        id: get_wc_versions_for_matrix
        run: echo "::set-output name=wc-versions::$(git ls-remote --tags --refs https://github.com/woocommerce/woocommerce.git | sed 's/.*refs\/tags\///' | php ./bin/get-latest-version.php)"
  php_build_and_test:
    name: PHP Tests (WP ${{ matrix.wp-version }} WC ${{ matrix.wc-version }})
    needs: find_latest_versions
    runs-on: ubuntu-latest
    services:
      mariadb:
        image: mariadb:10.9
        ports:
          - 3306:3306
        env:
          MYSQL_USER: wp_test
          MYSQL_PASSWORD: wp_test
          MYSQL_DATABASE: wordpress_default
          MYSQL_ROOT_PASSWORD: root
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
    strategy:
      matrix:
        wp-version: ${{ fromJSON(needs.find_latest_versions.outputs.wp-versions) }}
        wc-version: ${{ fromJSON(needs.find_latest_versions.outputs.wc-versions) }}
    steps:
      - name: Install SVN if not already installed
        run: |
          if ! svn --version &> /dev/null
          then
            echo "SVN is not installed. Installing SVN..."
            sudo apt-get update && sudo apt-get install -y subversion
          else
            echo "SVN is already installed."
          fi
      - name: Checkout WCS&T
        uses: actions/checkout@v2
        with:
          submodules: recursive
      - name: Checkout WooCommerce
        run: git clone --depth=1 --branch=${{ matrix.wc-version }} https://github.com/woocommerce/woocommerce.git /tmp/woocommerce
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
      - name: Get WC pinned pnpm version
        id: get_wc_pnpm_version
        run: echo "WC_PNPM=$( grep packageManager /tmp/woocommerce/package.json | sed -nr 's/.+packageManager.+pnpm@([[:digit:].]+).+/\1/p' )" >> "$GITHUB_OUTPUT"
      - name: Setup PNPM
        uses: pnpm/action-setup@v2
        with:
          version: ${{ steps.get_wc_pnpm_version.outputs.WC_PNPM }}
      - uses: actions/setup-node@v3
        with:
          node-version: 'v16'

      - name: Build WooCommerce essentials for running tests
        run: |
          cd /tmp/woocommerce/plugins/woocommerce
          composer install
          php bin/generate-feature-config.php
      - name: Setup WordPress
        run: bash /tmp/woocommerce/plugins/woocommerce/tests/bin/install.sh wordpress_test root root 127.0.0.1 ${{ matrix.wp-version }}
      - name: Use PHPUnit v8 for WC < 7.7.0
        if: contains(fromJSON('["7.5.1", "7.6.1"]'), matrix.wc-version)
        run: composer require -W phpunit/phpunit:^8
      - name: Install Composer dependencies
        run: composer install
      - name: Run PHPUnit tests
        run: ./vendor/bin/phpunit -c phpunit.xml.dist
