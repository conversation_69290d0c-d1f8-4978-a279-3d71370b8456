### Basic Yarn settings. ###
compressionLevel: 0
nodeLinker: node-modules
enableGlobalCache: true
yarnPath: .yarn/releases/yarn-4.0.2.cjs

### Log filters to discard irrelevant warnings. ###
logFilters:
  # WordPress packages often have transitive dependencies with incorrect react-related peer dependencies.
  # This matcher discards those log messages, while allowing non-react or non-wordpress related violations
  # to be handled separately. (For example, npm packages can be handled via packageExtensions.)
  #
  # WordPress will need to update/change the following packages to versions which include React 18 support,
  # and then we'll need to update to those WordPress package versions before we can remove this.
  # - react-autosize-textarea@npm:7.1.0
  #
  # The matcher works like so:
  # 1. Match any workspace package, such as @automattic/global-styles@workspace, calypso@workspace, happy-blocks@workspace
  # 2. Match any path within the workspace, such as @workspace:packages/page-pattern-modal [cd6b3], @workspace:client, or @workspace:.
  # 3. Match a react or react-dom on the current version which a workspace package provides.
  # 4. Match only violations coming from within @wordpress packages.
  - level: discard
    pattern: "+(react|react-dom) is listed by your project with version 18.3.1, which doesn't satisfy what +(@wordpress/*|@types/wordpress__*) and other dependencies request*"

### Package extensions to provide missing peer dependencies. ###
packageExtensions:
  '@signal-noise/stylelint-scales@2.0.3':
    peerDependencies:
      postcss: '*'
  fake-indexeddb@3.1.8:
    peerDependencies:
      core-js-bundle: '*'
      regenerator-runtime: '*'
  realistic-structured-clone@2.0.2:
    peerDependencies:
      core-js-bundle: '*'
      regenerator-runtime: '*'
  typeson-registry@1.0.0-alpha.35:
    peerDependencies:
      core-js-bundle: '*'
      regenerator-runtime: '*'
