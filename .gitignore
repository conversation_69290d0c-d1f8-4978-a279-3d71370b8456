/nbproject/private/
/node_modules/
project.xml
project.properties
.DS_Store
Thumbs.db
build
.buildpath
.project
.settings*
.vscode
sftp-config.json
/deploy/
/wc-apidocs/
i18n/
release/

# Ignore all log files except for .htaccess
/logs/*
!/logs/.htaccess

/vendor/

tests/e2e/config/local-*
.eslintcache
woocommerce-services.zip
dist/
.cache/
tests/e2e-tests/screenshots

# docker data for local development
docker/data
docker/wordpress
docker/logs
docker/bin/jt
.idea
docker-compose.override.yml
