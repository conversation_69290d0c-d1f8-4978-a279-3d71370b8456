.webpack-cache
.sass-cache
.babel-cache
.vagrant
.unison
.env
.DS_Store
.idea
.vscode/settings.json
.nova
*.code-workspace
*.iml
*.iws
/.tsc-cache/
/.cache/
/tags
node_modules
/npm-debug.log*
*.sw?
!*.swf
.eslintcache
test-results*.xml
/stats.json
/chart.json
/style.json
/client/stats.json
/client/stats-server.json
/client/chart.json
/client/style.json
*xunit_*.xml
/vendor
checkstyle.xml
/apps/notifications/stats.json

# added during build
/config/secrets.json

# config overrides
/config/*.local.json

/coverage

/build
/packages/webpack-inline-constant-exports-plugin/test/build

/public/

/server/devdocs/search-index.js
/server/devdocs/components-usage-stats.json
/server/devdocs/proptypes-index.json
/client/server/devdocs/search-index.js
/client/server/devdocs/components-usage-stats.json
/client/server/devdocs/proptypes-index.json
/client/languages/languages-meta.json

*.rdb
*.db

/calypso-strings.pot
/chunks-map.*.json
cover.html
.test.log

cached-requests.json

# Monorepo output
/apps/*/dist/
/apps/*/build/
/apps/*/types/
/packages/*/dist/
/build-tools/dist/
/apps/*/.cache/
/packages/*/.cache/
/desktop/.cache/
/apps/*/release-files/
/apps/**/*/build_meta.json

# webpack assets
/assets*.json
/desktop/assets*.json
/server/bundler/assets*.json
/client/server/bundler/assets*.json

# Desktop app
/desktop/dist
/desktop/build
/desktop/client
/desktop/config
/desktop/public
/desktop/release*
/desktop/resource/certificates/mac.p12
/desktop/resource/certificates/win.p12
/client/desktop/config.json
/desktop/id_rsa
/desktop/e2e/logs
/desktop/e2e/screenshots
/desktop/e2e/result.xml
/desktop/test/.cache/
/desktop/test/e2e/results/

# Typescript
*.tsbuildinfo

# TeamCity Kotlin compilation
.teamcity/target

# Yarn
yarn-error.log
.yarn/*
desktop/.yarn/*
!.yarn/patches
!.yarn/releases
!.yarn/plugins
!.yarn/sdks
!.yarn/versions
.pnp.*
yarn-json-output.json

# Profile results:
/profiles/

# Storybook
storybook-static
packages/plans-grid-next/static/mockServiceWorker.js

# PHPUnit cache
.phpunit.result.cache
.phpunit.cache

.docusaurus
