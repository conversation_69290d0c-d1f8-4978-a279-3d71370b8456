{
	"name": "@automattic/{{ kebabCase name }}",
	"version": "1.0.0",
	"description": "{{ description }}",
	"homepage": "https://github.com/Automattic/wp-calypso",
	"license": "GPL-2.0-or-later",
	"author": "Automattic Inc.",
	"main": "dist/cjs/index.js",
	"module": "dist/esm/index.js",
	"types": "dist/types/index.d.ts",
	"calypso:src": "src/index.tsx",
	"exports": {
		".": {
			"calypso:src": "./src/index.tsx",
			"types": "./dist/types/index.d.ts",
			"import": "./dist/esm/index.js",
			"require": "./dist/cjs/index.js"
		}
	},
	"sideEffects": false,
	"repository": {
		"type": "git",
		"url": "git+https://github.com/Automattic/wp-calypso.git",
		"directory": "packages/{{ kebabCase name }}"
	},
	"publishConfig": {
		"access": "public"
	},
	"bugs": "https://github.com/Automattic/wp-calypso/issues",
	"files": [
		"dist",
		"src"
	],
	"scripts": {
		"clean": "tsc --build ./tsconfig.json ./tsconfig-cjs.json --clean && rm -rf dist",
		"build": "tsc --build ./tsconfig.json ./tsconfig-cjs.json",
		"prepack": "yarn run clean && yarn run build",
		"watch": "tsc --build ./tsconfig.json --watch"
	},
	"dependencies": {
		"tslib": "^2.3.0"
	},
	"devDependencies": {
		"@automattic/calypso-typescript-config": "workspace:^",
		"typescript": "^4.7.4"
	},
{{#if public}}
	"private": false
{{else}}
	"private": true
{{/if}}
}
