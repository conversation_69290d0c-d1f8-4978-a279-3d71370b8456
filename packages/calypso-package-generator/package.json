{"name": "@automattic/calypso-package-generator", "version": "1.0.0", "description": "Generate a sub-package under the Calypso monorepo.", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/Automattic/wp-calypso.git", "directory": "packages/generators"}, "author": "Automattic Inc.", "license": "GPL-2.0-or-later", "bin": {"generate-calypso-package": "cli.js"}, "bugs": "https://github.com/Automattic/wp-calypso/issues", "homepage": "https://github.com/Automattic/wp-calypso/tree/HEAD/packages/generators#readme", "type": "module", "scripts": {"generate": "generate-calypso-package"}, "dependencies": {"plop": "^3.1.2"}, "devDependencies": {"@automattic/calypso-eslint-overrides": "workspace:^", "@automattic/calypso-typescript-config": "workspace:^"}, "private": true}