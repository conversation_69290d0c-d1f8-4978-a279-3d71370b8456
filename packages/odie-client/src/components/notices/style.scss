.odie-notices {
	display: flex;
	flex-direction: column;
	gap: 8px;
	font-size: 0.75rem;
	width: 100%;

	.odie-notice {
		display: flex;
		justify-content: space-between;
		background-color: var( --studio-yellow-0 );
		border-left: 4px solid var( --studio-yellow-20 );
		padding: 8px 16px;
		margin: 0;
		gap: 40px;

		p {
			margin: 0;
		}

		button {
			align-self: flex-start;
			background: none;
			border: none;
			&:hover {
				cursor: pointer;
			}
		}

		.odie-notice__view-conversation {
			button {
				color: #3858e9;
				font-size: 0.75rem;
				&:hover {
					text-decoration: underline;
				}
			}
		}
	}
}
