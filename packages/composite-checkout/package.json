{"name": "@automattic/composite-checkout", "version": "2.0.1", "description": "A set of React components and helpers that can be used to create a checkout flow.", "main": "dist/cjs/public-api.js", "module": "dist/esm/public-api.js", "types": "dist/types/public-api.d.ts", "calypso:src": "src/public-api.ts", "exports": {".": {"calypso:src": "./src/public-api.ts", "types": "./dist/types/public-api.d.ts", "import": "./dist/esm/public-api.js", "require": "./dist/cjs/public-api.js"}}, "sideEffects": false, "scripts": {"clean": "tsc --build ./tsconfig.json ./tsconfig-cjs.json --clean && rm -rf dist", "build": "tsc --build ./tsconfig.json ./tsconfig-cjs.json", "prepack": "yarn run clean && yarn run build", "watch": "tsc --build ./tsconfig.json --watch", "storybook:start": "storybook dev"}, "files": ["dist", "src"], "keywords": ["checkout", "payments", "automattic"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/Automattic/wp-calypso.git", "directory": "packages/composite-checkout"}, "author": "Automattic Inc.", "license": "GPL-2.0-or-later", "bugs": "https://github.com/Automattic/wp-calypso/issues", "homepage": "https://github.com/Automattic/wp-calypso/tree/HEAD/packages/composite-checkout#readme", "dependencies": {"@automattic/color-studio": "^4.1.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@wordpress/i18n": "^5.23.0", "@wordpress/react-i18n": "^4.23.0", "debug": "^4.4.1", "prop-types": "^15.8.1"}, "devDependencies": {"@automattic/calypso-storybook": "workspace:^", "@automattic/calypso-typescript-config": "workspace:^", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "react": "^18.3.1", "react-dom": "^18.3.1", "storybook": "^8.6.14", "typescript": "^5.8.3"}, "peerDependencies": {"react": "^18.3.1", "react-dom": "^18.3.1"}}