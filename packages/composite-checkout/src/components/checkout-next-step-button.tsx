import * as React from 'react';
import Button, { ButtonProps } from './button';

function CheckoutNextStepButton( {
	value,
	onClick,
	ariaLabel,
	...props
}: CheckoutNextStepButtonProps & ButtonProps & React.ButtonHTMLAttributes< HTMLButtonElement > ) {
	return (
		<Button
			className="checkout-next-step-button"
			onClick={ onClick }
			buttonType="primary"
			aria-label={ ariaLabel }
			{ ...props }
		>
			{ value }
		</Button>
	);
}

interface CheckoutNextStepButtonProps {
	value: React.ReactNode;
	onClick: () => void;
	ariaLabel: string;
}

export default CheckoutNextStepButton;
