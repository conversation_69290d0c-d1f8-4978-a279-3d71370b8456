export function VisaLogo( { className }: { className?: string } ) {
	return (
		<svg
			className={ className }
			width="41"
			height="17"
			viewBox="0 0 41 17"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			aria-hidden="true"
			focusable="false"
		>
			<path
				d="M28.3326 3.41187C27.7955 3.19215 26.9166 2.97244 25.8669 2.97244C23.1327 2.97244 21.2041 4.38837 21.2041 6.39019C21.1797 7.87935 22.5712 8.70938 23.621 9.19763C24.6951 9.71029 25.0613 10.0276 25.0613 10.4915C25.0613 11.175 24.2069 11.4924 23.4012 11.4924C22.3027 11.4924 21.7168 11.3459 20.7891 10.9553L20.4229 10.7844L20.0323 13.1036C20.667 13.3966 21.8633 13.6407 23.1083 13.6651C26.0134 13.6651 27.8931 12.2736 27.9176 10.1253C27.9176 8.9535 27.1852 8.05024 25.5984 7.31786C24.6219 6.82961 24.036 6.51225 24.036 6.024C24.036 5.58458 24.5486 5.12074 25.6228 5.12074C26.526 5.09633 27.1852 5.31604 27.6978 5.51134L27.942 5.6334L28.3326 3.41187ZM35.4122 3.16774H33.2883C32.6292 3.16774 32.1409 3.36304 31.848 4.02218L27.7467 13.5186H30.6517C30.6517 13.5186 31.1156 12.2492 31.2376 11.9562C31.555 11.9562 34.3624 11.9562 34.7775 11.9562C34.8507 12.3224 35.1192 13.5186 35.1192 13.5186H37.6825L35.4122 3.16774ZM32.0189 9.83235C32.2386 9.24645 33.1174 6.92726 33.1174 6.92726C33.093 6.95168 33.3371 6.31695 33.4836 5.95077L33.6789 6.85403C33.6789 6.85403 34.216 9.31969 34.3136 9.85676L32.0189 9.83235ZM15.5648 13.5186L17.2981 3.16774H20.0567L18.3234 13.5186H15.5648ZM13.2457 3.16774L10.5359 10.2229L10.2429 8.78261C9.73025 7.12256 8.16785 5.34045 6.41016 4.43719L8.87582 13.4942H11.8053L16.1507 3.14333L13.2457 3.16774Z"
				fill="#265697"
			/>
			<path
				d="M8.02162 3.14371H3.55414L3.52972 3.36342C6.9963 4.21786 9.29107 6.29292 10.2432 8.78299L9.26666 4.02256C9.09577 3.36342 8.63193 3.16812 8.02162 3.14371Z"
				fill="#D97B16"
			/>
		</svg>
	);
}

export function MastercardLogo( { className }: { className?: string } ) {
	return (
		<svg
			className={ className }
			aria-hidden="true"
			focusable="false"
			width="41"
			height="17"
			viewBox="0 0 41 17"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path d="M17.5324 13.7026H23.6991V3.63513H17.5324V13.7026Z" fill="#FF5F00" />
			<path
				d="M18.1634 8.68104C18.1634 10.6431 19.0744 12.4885 20.6161 13.7031C17.8364 15.8988 13.8187 15.4083 11.6231 12.6286C9.42736 9.84896 9.91789 5.83131 12.6975 3.63561C15.01 1.81365 18.2802 1.81365 20.6161 3.63561C19.0744 4.85025 18.1634 6.71893 18.1634 8.68104Z"
				fill="#EB001B"
			/>
			<path
				d="M30.9638 8.68038C30.9638 5.15326 28.0907 2.28017 24.5636 2.28017C23.1387 2.28017 21.7372 2.7707 20.616 3.65832C23.3956 5.85401 23.8862 9.87167 21.6905 12.6513C21.3868 13.0484 21.0131 13.4221 20.616 13.7258C23.3956 15.9215 27.4133 15.431 29.609 12.6513C30.4966 11.5068 30.9638 10.1052 30.9638 8.68038Z"
				fill="#F79E1B"
			/>
		</svg>
	);
}

export function AmexLogo( { className }: { className?: string } ) {
	return (
		<svg
			className={ className }
			width="42"
			height="16"
			viewBox="0 0 42 16"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			aria-hidden="true"
			focusable="false"
		>
			<path
				d="M5.21231 2.90482L0.583511 13.1482H6.12484L6.8118 11.515H8.38205L9.06901 13.1482H15.1685V11.9017L15.7119 13.1482H18.8671L19.4106 11.8753V13.1482H32.0957L33.6382 11.5574L35.0825 13.1482L41.5979 13.1614L36.9544 8.05511L41.5979 2.90482H35.1835L33.6821 4.46623L32.2832 2.90482H18.4834L17.2984 5.54872L16.0856 2.90482H10.5559V4.10893L9.94072 2.90482C9.94072 2.90482 5.21231 2.90482 5.21231 2.90482ZM6.28453 4.35939H8.98563L12.0559 11.3055V4.35939H15.0149L17.3863 9.33971L19.5719 4.35939H22.516V11.7097H20.7246L20.7099 5.95004L18.0982 11.7097H16.4956L13.8692 5.95004V11.7097H10.1838L9.4851 10.0619H5.71035L5.0131 11.7083H3.03849C3.03849 11.7083 6.28453 4.35939 6.28453 4.35939ZM24.155 4.35939H31.4395L33.6674 6.76601L35.9672 4.35939H38.1952L34.81 8.05366L38.1952 11.7054H35.8662L33.6382 9.27079L31.3267 11.7054H24.155V4.35939ZM7.59853 5.60302L6.3549 8.53852H8.84072L7.59853 5.60302ZM25.9539 5.88128V7.22307H29.9279V8.71862H25.9539V10.1835H30.4113L32.4824 8.02587L30.4992 5.87999H25.9539V5.88128Z"
				fill="#26A6D1"
			/>
		</svg>
	);
}

export function JcbLogo( { className }: { className?: string } ) {
	return (
		<svg
			className={ className }
			aria-hidden="true"
			focusable="false"
			width="42"
			height="16"
			viewBox="0 0 42 16"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M31.1327 12.8422C31.1327 14.5877 29.7024 15.996 27.9296 15.996H10.5042V3.51977C10.5042 1.77428 11.9345 0.365997 13.7073 0.365997H31.1125L31.1327 12.8422Z"
				fill="white"
			/>
			<path
				d="M25.4517 8.4587C25.9553 8.47854 26.4589 8.43887 26.9424 8.47854C27.446 8.57771 27.5669 9.31161 27.1237 9.56946C26.8215 9.72814 26.4589 9.62897 26.1366 9.6488H25.4718L25.4517 8.4587ZM27.2244 7.10992C27.3252 7.48678 26.9626 7.84381 26.5798 7.78431H25.4315C25.4315 7.42728 25.4114 7.03058 25.4517 6.69338C25.915 6.71322 26.3784 6.67355 26.8215 6.71322C27.023 6.75289 27.1841 6.91157 27.2244 7.10992ZM29.9843 1.41727C30.0044 2.15116 29.9843 2.92473 29.9843 3.67846V12.8026C29.9642 13.9332 28.9368 14.9249 27.7885 14.9646H24.3437V10.343C25.5927 10.343 26.8618 10.3629 28.1108 10.343C28.695 10.3034 29.3195 9.92649 29.3598 9.29177C29.4202 8.65705 28.8159 8.22068 28.2518 8.16118C28.0302 8.16118 28.0302 8.10167 28.2518 8.08183C28.7957 7.96282 29.2389 7.40744 29.0778 6.85206C28.9368 6.25701 28.272 6.03883 27.7281 6.03883H24.3639C24.3639 5.18592 24.3437 4.31318 24.384 3.46027C24.4646 2.34951 25.5323 1.41727 26.6604 1.4371L29.9843 1.41727Z"
				fill="#4BAC33"
			/>
			<path
				d="M11.6525 3.55945C11.6726 2.42885 12.7201 1.4371 13.8684 1.41727H17.3132V12.8422C17.2729 13.9728 16.2455 14.9249 15.1174 14.9447H11.6726V10.1843C12.7806 10.4422 13.9691 10.5612 15.0972 10.3827C15.7822 10.2835 16.5275 9.94633 16.7491 9.2521C16.9103 8.65705 16.8297 8.02233 16.8498 7.42728V6.01899H14.8756C14.8756 6.95124 14.8958 7.90332 14.8555 8.83557C14.7951 9.41078 14.231 9.76781 13.6669 9.74798C12.982 9.74798 11.6323 9.2521 11.6323 9.2521C11.6323 7.50662 11.6525 5.30493 11.6525 3.55945Z"
				fill="#0466B2"
			/>
			<path
				d="M18.0383 6.59425C17.9376 6.61409 18.0182 6.23722 17.998 6.09837C17.998 5.2058 17.9779 4.33306 18.0182 3.44048C18.0988 2.30989 19.1664 1.39747 20.3147 1.41731H23.6588V12.8423C23.6185 13.9729 22.5911 14.925 21.463 14.9448H18.0182V9.74802C18.8038 10.3827 19.8715 10.4819 20.8385 10.4819C21.5838 10.4819 22.3091 10.3629 23.0343 10.2042V9.25214C22.2285 9.64884 21.2817 9.9067 20.3751 9.66868C19.7506 9.51 19.3075 8.91495 19.3075 8.28023C19.2269 7.62567 19.6298 6.93145 20.2946 6.7331C21.1205 6.47524 22.0069 6.67359 22.7724 7.01079C22.9335 7.09013 23.0947 7.20914 23.0343 6.93145V6.17771C21.745 5.88019 20.3953 5.76118 19.106 6.09837C18.7031 6.17771 18.3405 6.33639 18.0383 6.59425Z"
				fill="#D40737"
			/>
		</svg>
	);
}

export function DinersLogo( { className }: { className?: string } ) {
	return (
		<svg
			className={ className }
			aria-hidden="true"
			focusable="false"
			width="42"
			height="17"
			viewBox="0 0 42 17"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M22.3904 15.145C26.3558 15.164 29.9753 11.911 29.9753 7.95362C29.9753 3.62602 26.3558 0.634731 22.3904 0.63617H18.9777C14.9647 0.634731 11.6616 3.62691 11.6616 7.95362C11.6616 11.9118 14.9647 15.164 18.9777 15.145H22.3904Z"
				fill="#0079BE"
			/>
			<path
				d="M18.9938 1.23581C15.3269 1.23696 12.3554 4.20925 12.3546 7.87765C12.3554 11.5454 15.3268 14.5174 18.9938 14.5186C22.6616 14.5174 25.6336 11.5454 25.6342 7.87765C25.6336 4.20925 22.6616 1.23696 18.9938 1.23581ZM14.7854 7.87765C14.7888 6.08506 15.9084 4.55646 17.4871 3.94902V11.8054C15.9084 11.1983 14.7888 9.6705 14.7854 7.87765ZM20.5 11.8071V3.94876C22.0792 4.55473 23.2005 6.0842 23.2034 7.87765C23.2005 9.67165 22.0792 11.2 20.5 11.8071Z"
				fill="white"
			/>
			<path
				d="M22.3904 15.145C26.3558 15.164 29.9753 11.911 29.9753 7.95362C29.9753 3.62602 26.3558 0.634731 22.3904 0.63617H18.9777C14.9647 0.634731 11.6616 3.62691 11.6616 7.95362C11.6616 11.9118 14.9647 15.164 18.9777 15.145H22.3904Z"
				fill="#0079BE"
			/>
			<path
				d="M18.9938 1.23581C15.3269 1.23696 12.3554 4.20925 12.3546 7.87765C12.3554 11.5454 15.3268 14.5174 18.9938 14.5186C22.6616 14.5174 25.6336 11.5454 25.6342 7.87765C25.6336 4.20925 22.6616 1.23696 18.9938 1.23581ZM14.7854 7.87765C14.7888 6.08506 15.9084 4.55646 17.4871 3.94902V11.8054C15.9084 11.1983 14.7888 9.6705 14.7854 7.87765ZM20.5 11.8071V3.94876C22.0792 4.55473 23.2005 6.0842 23.2034 7.87765C23.2005 9.67165 22.0792 11.2 20.5 11.8071Z"
				fill="white"
			/>
		</svg>
	);
}

export function UnionpayLogo( { className }: { className?: string } ) {
	return (
		<svg
			className={ className }
			aria-hidden="true"
			focusable="false"
			width="42"
			height="16"
			viewBox="0 0 42 16"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M13.7639 1.00259H19.5529C20.361 1.00259 20.8636 1.66125 20.6751 2.47202L17.9799 14.0434C17.7897 14.8514 16.9806 15.5106 16.1719 15.5106H10.3836C9.5766 15.5106 9.07287 14.8514 9.26138 14.0434L11.9577 2.47202C12.1462 1.66125 12.9548 1.00259 13.7639 1.00259Z"
				fill="#E21836"
			/>
			<path
				d="M19.0711 1.00211H25.7283C26.5362 1.00211 26.1719 1.66077 25.9818 2.47153L23.2871 14.043C23.098 14.8509 23.157 15.5101 22.3473 15.5101H15.6902C14.8806 15.5101 14.3795 14.8509 14.5697 14.043L17.2643 2.47153C17.4556 1.66077 18.2626 1.00211 19.0711 1.00211Z"
				fill="#00447C"
			/>
			<path
				d="M25.4642 1.00211H31.2532C32.0624 1.00211 32.565 1.66077 32.3749 2.47153L29.6802 14.043C29.49 14.8509 28.6803 15.5101 27.8713 15.5101H22.085C21.2753 15.5101 20.7732 14.8509 20.9628 14.043L23.658 2.47153C23.8465 1.66077 24.6546 1.00211 25.4642 1.00211Z"
				fill="#007B84"
			/>
		</svg>
	);
}

export function DiscoverLogo( { className }: { className?: string } ) {
	return (
		<svg
			className={ className }
			aria-hidden="true"
			focusable="false"
			width="42"
			height="17"
			viewBox="0 0 42 17"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M1.06596 5.46762H3.29818C3.68509 5.46762 4.04225 5.52582 4.36964 5.64223C4.7268 5.75864 5.02443 5.93325 5.29229 6.16606C5.56016 6.39887 5.7685 6.68989 5.94708 7.0391C6.12566 7.38832 6.18518 7.79574 6.18518 8.26137C6.18518 8.72699 6.09589 9.16352 5.91732 9.51274C5.73874 9.86195 5.50063 10.153 5.203 10.3858C4.90538 10.6186 4.60775 10.735 4.25059 10.8514C3.89343 10.9678 3.53628 11.026 3.20889 11.026H1.09572L1.06596 5.46762ZM2.85173 10.1239C3.17912 10.1239 3.47675 10.0948 3.74462 10.0075C4.01249 9.92016 4.28035 9.83285 4.48869 9.68735C4.69703 9.54184 4.87561 9.33813 4.99466 9.10531C5.11372 8.8725 5.17324 8.58149 5.17324 8.23227C5.17324 7.88305 5.11372 7.62114 5.02443 7.35922C4.93514 7.09731 4.75656 6.9227 4.57798 6.77719C4.3994 6.63168 4.1613 6.51528 3.89344 6.45707C3.62557 6.39887 3.32794 6.36977 3.00055 6.36977H2.0779V10.153H2.85173V10.1239ZM7.1376 5.46762H8.14954V11.026H7.1376V5.46762ZM12.1973 6.66078C12.0782 6.51528 11.9592 6.39887 11.7806 6.34067C11.602 6.28246 11.4235 6.22426 11.2151 6.22426C11.0961 6.22426 10.977 6.22426 10.8877 6.25336C10.7984 6.28246 10.6794 6.31157 10.5901 6.36977C10.5008 6.42797 10.4413 6.51528 10.3818 6.60258C10.3222 6.68989 10.2627 6.80629 10.2627 6.9227C10.2627 7.09731 10.3222 7.24282 10.471 7.35922C10.6199 7.47563 10.7687 7.53383 10.977 7.62114C11.1854 7.70844 11.3937 7.76664 11.6318 7.82485C11.8699 7.88305 12.0782 7.99946 12.2866 8.11586C12.4949 8.23227 12.6437 8.40688 12.7926 8.58149C12.9414 8.7852 13.0009 9.04711 13.0009 9.36723C13.0009 9.65824 12.9414 9.92016 12.8223 10.153C12.7033 10.3858 12.5247 10.5895 12.3461 10.735C12.1675 10.8805 11.9294 10.9969 11.6913 11.0551C11.4532 11.1133 11.1854 11.1715 10.8877 11.1715C10.5306 11.1715 10.2032 11.1133 9.87579 10.9969C9.54839 10.8805 9.28053 10.6768 9.04242 10.4149L9.81626 9.68735C9.93531 9.86195 10.1139 10.0075 10.2925 10.1239C10.5901 10.2694 10.9473 10.3276 11.2746 10.2403C11.3937 10.2112 11.5127 10.153 11.602 10.0948C11.6913 10.0366 11.7806 9.94926 11.8401 9.86195C11.8997 9.77465 11.9294 9.65824 11.9294 9.54184C11.9294 9.33813 11.8699 9.19262 11.7211 9.07621C11.5723 8.95981 11.4235 8.8725 11.2151 8.7852C11.0068 8.69789 10.7984 8.63969 10.5603 8.58149C10.3222 8.52328 10.1139 8.40688 9.90555 8.29047C9.69721 8.17406 9.54839 8.02856 9.39958 7.82485C9.25076 7.62114 9.19124 7.35922 9.19124 7.0391C9.19124 6.74809 9.25076 6.51528 9.36982 6.28246C9.48887 6.04965 9.63768 5.90414 9.84602 5.75864C10.0544 5.61313 10.2627 5.52582 10.5306 5.43852C11.0663 5.29301 11.6318 5.29301 12.1675 5.46762C12.4652 5.55493 12.7033 5.70043 12.9414 5.90414L12.1973 6.66078ZM17.9415 6.77719C17.7332 6.54438 17.5249 6.39887 17.3463 6.34067C17.1677 6.28246 16.9594 6.22426 16.751 6.22426C16.4534 6.22426 16.1855 6.28246 15.9474 6.36977C15.7093 6.45707 15.501 6.60258 15.3224 6.80629C15.1438 7.01 15.0248 7.18461 14.9355 7.44653C14.8462 7.70844 14.7867 7.94125 14.7867 8.23227C14.7867 8.52328 14.8462 8.8143 14.9355 9.04711C15.0248 9.27992 15.1736 9.51274 15.3224 9.68735C15.501 9.86195 15.7093 10.0075 15.9474 10.1239C16.1855 10.2403 16.4534 10.2694 16.751 10.2694C16.9891 10.2694 17.1975 10.2112 17.4356 10.0948C17.6737 9.97836 17.8523 9.80375 18.0308 9.57094L18.8642 10.153C18.5963 10.5022 18.2987 10.7641 17.9118 10.9096C17.5249 11.0551 17.1379 11.1424 16.7213 11.1424C16.2748 11.1424 15.8879 11.0842 15.501 10.9387C15.1141 10.7932 14.8164 10.5895 14.5486 10.3567C14.2807 10.1239 14.0724 9.80375 13.9235 9.45453C13.7747 9.10531 13.6854 8.69789 13.6854 8.26137C13.6854 7.82485 13.7747 7.41742 13.9235 7.0391C14.0724 6.66078 14.2807 6.36977 14.5486 6.10785C14.8164 5.84594 15.1438 5.64223 15.501 5.52582C15.8581 5.40942 16.2748 5.32211 16.7213 5.32211C17.1082 5.32211 17.4653 5.38032 17.7927 5.52582C18.1201 5.67133 18.4178 5.87504 18.7154 6.19516L17.9415 6.77719ZM25.5609 5.46762H26.7216L28.2693 9.65824L29.8765 5.46762H30.9777L28.686 11.026H27.8228L25.5609 5.46762ZM31.573 5.46762H35.3529V6.36977H32.5849V7.73754H35.204V8.63969H32.5849V10.153H35.4719V11.026H31.573V5.46762ZM36.4839 5.46762H38.4482C38.7161 5.46762 38.9839 5.49672 39.2518 5.55493C39.5197 5.61313 39.728 5.70043 39.9363 5.81684C40.1447 5.93325 40.2935 6.10785 40.4126 6.31157C40.5316 6.51528 40.5911 6.77719 40.5911 7.09731C40.5911 7.50473 40.4721 7.82485 40.2637 8.11586C40.0554 8.40688 39.6982 8.55238 39.2816 8.61059L40.7995 11.0842H39.5792L38.2696 8.72699H37.5256V11.026H36.5136L36.4839 5.46762ZM38.2994 7.82485C38.4482 7.82485 38.597 7.82485 38.7458 7.79574C38.8946 7.76664 39.0137 7.73754 39.1328 7.70844C39.2518 7.67934 39.3411 7.59203 39.4304 7.47563C39.5197 7.35922 39.5494 7.24281 39.5494 7.0391C39.5494 6.86449 39.5197 6.74809 39.4304 6.63168C39.3411 6.51528 39.2816 6.45707 39.1625 6.39887C39.0435 6.34067 38.9244 6.31157 38.7756 6.31157C38.6268 6.31157 38.5077 6.28246 38.3887 6.28246H37.5256V7.82485H38.2994Z"
				fill="black"
			/>
			<path
				d="M22.4956 11.2006C24.1558 11.2006 25.5016 9.88468 25.5016 8.26137C25.5016 6.63806 24.1558 5.32211 22.4956 5.32211C20.8354 5.32211 19.4895 6.63806 19.4895 8.26137C19.4895 9.88468 20.8354 11.2006 22.4956 11.2006Z"
				fill="#E8883D"
			/>
		</svg>
	);
}
