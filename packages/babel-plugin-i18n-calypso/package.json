{"name": "@automattic/babel-plugin-i18n-calypso", "version": "1.2.0", "description": "A Babel plugin to generate a POT file for translate calls.", "main": "src/index.js", "homepage": "https://github.com/Automattic/wp-calypso", "bugs": "https://github.com/Automattic/wp-calypso/issues", "dependencies": {"gettext-parser": "^4.0.3", "lodash": "^4.17.21"}, "devDependencies": {"@automattic/calypso-typescript-config": "workspace:^"}, "peerDependencies": {"@babel/core": "^7.27.1"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/Automattic/wp-calypso.git", "directory": "packages/babel-plugin-i18n-calypso"}, "keywords": [], "author": "Automattic Inc.", "license": "GPL-2.0-or-later"}