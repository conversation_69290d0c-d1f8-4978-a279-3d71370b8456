{"name": "@automattic/interpolate-components", "version": "1.2.1", "description": "Convert strings into structured React components.", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "calypso:src": "src/index.js", "exports": {".": {"calypso:src": "./src/index.js", "types": "./types/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "sideEffects": false, "repository": {"type": "git", "url": "git+https://github.com/Automattic/wp-calypso.git", "directory": "packages/interpolate-components"}, "author": "Automattic Inc.", "license": "GPL-2.0-or-later", "publishConfig": {"access": "public"}, "bugs": "https://github.com/Automattic/wp-calypso/issues", "homepage": "https://github.com/Automattic/wp-calypso/tree/HEAD/packages/interpolate-components#readme", "devDependencies": {"@automattic/calypso-typescript-config": "workspace:^", "react-dom": "^18.3.1"}, "peerDependencies": {"@types/react": ">=16.14.65", "react": ">=16.2.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "scripts": {"clean": "rm -rf dist", "build": "transpile", "prepack": "yarn run clean && yarn run build"}, "types": "types"}