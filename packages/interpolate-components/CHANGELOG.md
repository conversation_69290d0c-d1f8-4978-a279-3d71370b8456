# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## [1.2.1]

### Changed

- @types/react peer dependency increased from >=16.2.0 to >=16.14.23

## [1.2.0]

### Added

- Add TypeScript type declarations [#58711](https://github.com/Automattic/wp-calypso/pull/58711)

## [1.1.2]

### Changed

- Rename package from `interpolate-components` to `@automattic/interpolate-components` ([#56933](https://github.com/Automattic/wp-calypso/pull/56933))
- Move into [Automattic/wp-calypso](https://github.com/Automattic/wp-calypso/tree/trunk/packages/interpolate-components) repository ([#56933](https://github.com/Automattic/wp-calypso/pull/56933))

### Removed

- Drop support for React versions below `16.2` ([#56933](https://github.com/Automattic/wp-calypso/pull/56933), [#57564](https://github.com/Automattic/wp-calypso/pull/57564))

## [Older releases]

`1.1.2` is the first release of `@automattic/interpolate-components`. Older releases can be found
under `interpolate-components`.

[unreleased]: https://github.com/Automattic/wp-calypso/tree/HEAD/packages/interpolate-components
[1.2.0]: https://github.com/Automattic/wp-calypso/tree/%40automattic/interpolate-components%401.2.0/packages/interpolate-components
[1.1.2]: https://github.com/Automattic/wp-calypso/tree/%40automattic/interpolate-components%401.1.2/packages/interpolate-components
[older releases]: https://github.com/Automattic/interpolate-components
