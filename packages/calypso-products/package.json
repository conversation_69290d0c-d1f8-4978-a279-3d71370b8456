{"name": "@automattic/calypso-products", "version": "1.2.2", "description": "This module contains information about the various products sold within calypso, such as product slugs, plan intervals, etc.", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "types": "dist/types/index.d.ts", "calypso:src": "src/index.ts", "exports": {".": {"calypso:src": "./src/index.ts", "types": "./dist/types/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "sideEffects": false, "scripts": {"clean": "tsc --build ./tsconfig.json ./tsconfig-cjs.json --clean && rm -rf dist", "build": "tsc --build ./tsconfig.json ./tsconfig-cjs.json && run -T copy-assets", "prepare": "yarn run build", "prepack": "yarn run clean && yarn run build", "watch": "tsc --build ./tsconfig.json --watch"}, "files": ["dist", "src"], "keywords": ["checkout", "payments", "automattic"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/Automattic/wp-calypso.git", "directory": "packages/calypso-products"}, "author": "Automattic Inc.", "license": "GPL-2.0-or-later", "bugs": "https://github.com/Automattic/wp-calypso/issues", "homepage": "https://github.com/Automattic/wp-calypso/tree/HEAD/packages/calypso-products#readme", "dependencies": {"@automattic/calypso-config": "workspace:^", "@automattic/components": "workspace:^", "@automattic/i18n-utils": "workspace:^", "@automattic/number-formatters": "^1.0.1", "@automattic/shopping-cart": "workspace:^", "@automattic/urls": "workspace:^", "i18n-calypso": "workspace:^"}, "devDependencies": {"@automattic/calypso-typescript-config": "workspace:^", "@wordpress/data": "^10.23.0", "react": "^18.3.1", "react-dom": "^18.3.1", "typescript": "^5.8.3"}, "peerDependencies": {"@wordpress/data": "^10.23.0", "react": "^18.3.1"}}