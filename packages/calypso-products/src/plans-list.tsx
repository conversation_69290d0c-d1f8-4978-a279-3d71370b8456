import { isEnabled } from '@automattic/calypso-config';
import { formatNumber } from '@automattic/number-formatters';
import i18n, { translate } from 'i18n-calypso';
import {
	FEATURE_13GB_STORAGE,
	FEATURE_200GB_STORAGE,
	FEATURE_3GB_STORAGE,
	FEATURE_1GB_STORAGE,
	FEATURE_50GB_STORAGE,
	FEATURE_6GB_STORAGE,
	FEATURE_ACCEPT_PAYMENTS,
	FEATURE_ACTIVITY_LOG,
	FEATURE_ACTIVITY_LOG_1_YEAR_V2,
	FEATURE_ADVANCED_DESIGN_CUSTOMIZATION,
	FEATURE_ADVANCED_SEO,
	FEATURE_ADVANCED_SEO_EXPANDED_ABBR,
	FEATURE_ADVANCED_SEO_TOOLS,
	FEATURE_ALL_BUSINESS_FEATURES,
	FEATURE_ALL_FREE_FEATURES,
	FEATURE_ALL_FREE_FEATURES_JETPACK,
	FEATURE_ALL_PERSONAL_FEATURES,
	FEATURE_ALL_PREMIUM_FEATURES,
	FEATURE_ALL_PREMIUM_FEATURES_JETPACK,
	FEATURE_ANTISPAM_V2,
	FEATURE_WAF,
	FEATURE_AUDIO_UPLOADS,
	FEATURE_AUTOMATED_RESTORES,
	FEATURE_AUTOMATIC_SECURITY_FIXES,
	FEATURE_BACKUP_ARCHIVE_30,
	FEATURE_BACKUP_ARCHIVE_UNLIMITED,
	FEATURE_BACKUP_DAILY_V2,
	FEATURE_BACKUP_REALTIME_V2,
	FEATURE_BACKUP_STORAGE_SPACE_UNLIMITED,
	FEATURE_BLANK,
	FEATURE_BLOG_DOMAIN,
	FEATURE_COLLECT_PAYMENTS_V2,
	FEATURE_COMMUNITY_SUPPORT,
	FEATURE_CRM_V2,
	FEATURE_CUSTOM_DOMAIN,
	FEATURE_EARN_AD,
	FEATURE_EASY_SITE_MIGRATION,
	FEATURE_ECOMMERCE_MARKETING,
	FEATURE_EMAIL_FORWARDING_EXTENDED_LIMIT,
	FEATURE_FAST_SUPPORT_FROM_EXPERTS,
	FEATURE_FREE_BLOG_DOMAIN,
	FEATURE_FREE_DOMAIN,
	FEATURE_FREE_THEMES,
	FEATURE_FREE_THEMES_SIGNUP,
	FEATURE_FREE_WORDPRESS_THEMES,
	FEATURE_GOOGLE_ANALYTICS,
	FEATURE_GOOGLE_MY_BUSINESS,
	FEATURE_HOSTING,
	FEATURE_INSTALL_PLUGINS,
	WPCOM_FEATURES_INSTALL_PURCHASED_PLUGINS,
	FEATURE_JETPACK_1TB_BACKUP_STORAGE,
	FEATURE_JETPACK_ADVANCED,
	FEATURE_JETPACK_ALL_BACKUP_SECURITY_FEATURES,
	FEATURE_JETPACK_ANTI_SPAM_BI_YEARLY,
	FEATURE_JETPACK_ANTI_SPAM,
	FEATURE_JETPACK_ANTI_SPAM_MONTHLY,
	FEATURE_JETPACK_BACKUP_DAILY,
	FEATURE_JETPACK_BACKUP_DAILY_MONTHLY,
	FEATURE_JETPACK_BACKUP_REALTIME,
	FEATURE_JETPACK_BACKUP_REALTIME_MONTHLY,
	FEATURE_JETPACK_BACKUP_T0_MONTHLY,
	FEATURE_JETPACK_BACKUP_T0_YEARLY,
	FEATURE_JETPACK_BACKUP_T1_MONTHLY,
	FEATURE_JETPACK_BACKUP_T1_YEARLY,
	FEATURE_JETPACK_BACKUP_T1_BI_YEARLY,
	FEATURE_JETPACK_BACKUP_T2_MONTHLY,
	FEATURE_JETPACK_BACKUP_T2_YEARLY,
	FEATURE_JETPACK_CRM,
	FEATURE_JETPACK_CRM_MONTHLY,
	FEATURE_JETPACK_ESSENTIAL,
	FEATURE_JETPACK_PRODUCT_BACKUP,
	FEATURE_JETPACK_PRODUCT_VIDEOPRESS,
	FEATURE_JETPACK_REAL_TIME_MALWARE_SCANNING,
	FEATURE_JETPACK_SCAN_DAILY,
	FEATURE_JETPACK_SCAN_DAILY_MONTHLY,
	FEATURE_JETPACK_SEARCH_BI_YEARLY,
	FEATURE_JETPACK_SEARCH,
	FEATURE_JETPACK_SEARCH_MONTHLY,
	FEATURE_JETPACK_VIDEOPRESS_BI_YEARLY,
	FEATURE_JETPACK_VIDEOPRESS,
	FEATURE_JETPACK_VIDEOPRESS_MONTHLY,
	FEATURE_MALWARE_SCANNING_DAILY,
	FEATURE_MALWARE_SCANNING_DAILY_AND_ON_DEMAND,
	FEATURE_MANAGE,
	FEATURE_MEMBERSHIPS,
	FEATURE_MONETISE,
	FEATURE_NO_ADS,
	FEATURE_NO_BRANDING,
	FEATURE_OFFSITE_BACKUP_VAULTPRESS_DAILY,
	FEATURE_OFFSITE_BACKUP_VAULTPRESS_REALTIME,
	FEATURE_ONE_CLICK_THREAT_RESOLUTION,
	FEATURE_P2_13GB_STORAGE,
	FEATURE_P2_3GB_STORAGE,
	FEATURE_P2_ACTIVITY_OVERVIEW,
	FEATURE_P2_ADVANCED_SEARCH,
	FEATURE_P2_CUSTOMIZATION_OPTIONS,
	FEATURE_P2_MORE_FILE_TYPES,
	FEATURE_P2_PRIORITY_CHAT_EMAIL_SUPPORT,
	FEATURE_P2_SIMPLE_SEARCH,
	FEATURE_P2_UNLIMITED_POSTS_PAGES,
	FEATURE_P2_UNLIMITED_USERS,
	FEATURE_P2_VIDEO_SHARING,
	FEATURE_PLAN_SECURITY_DAILY,
	FEATURE_PREMIUM_CONTENT_BLOCK,
	FEATURE_PREMIUM_CUSTOMIZABE_THEMES,
	FEATURE_PREMIUM_SUPPORT,
	FEATURE_PRODUCT_BACKUP_DAILY_V2,
	FEATURE_PRODUCT_BACKUP_REALTIME_V2,
	FEATURE_PRODUCT_SCAN_DAILY_V2,
	FEATURE_PRODUCT_SCAN_REALTIME_V2,
	FEATURE_PRODUCT_SEARCH_V2,
	FEATURE_REPUBLICIZE,
	FEATURE_SCAN_V2,
	FEATURE_SEO_PREVIEW_TOOLS,
	FEATURE_SITE_STAGING_SITES,
	FEATURE_SFTP_DATABASE,
	FEATURE_SHIPPING_CARRIERS,
	FEATURE_SIMPLE_PAYMENTS,
	FEATURE_SITE_BACKUPS_AND_RESTORE,
	FEATURE_SITE_STATS,
	FEATURE_SOCIAL_MEDIA_TOOLS,
	FEATURE_SPAM_AKISMET_PLUS,
	FEATURE_STANDARD_SECURITY_TOOLS,
	FEATURE_TITAN_EMAIL,
	FEATURE_TRAFFIC_TOOLS,
	FEATURE_UNLIMITED_PRODUCTS_SERVICES,
	FEATURE_UPLOAD_PLUGINS,
	FEATURE_UPLOAD_THEMES,
	FEATURE_UPLOAD_THEMES_PLUGINS,
	FEATURE_VIDEO_UPLOADS,
	FEATURE_VIDEO_UPLOADS_JETPACK_PRO,
	FEATURE_WORDADS_INSTANT,
	FEATURE_WP_SUBDOMAIN,
	FEATURE_WP_SUBDOMAIN_SIGNUP,
	FEATURE_UNLIMITED_ADMINS,
	FEATURE_UNLIMITED_TRAFFIC,
	FEATURE_PAYMENT_BLOCKS,
	FEATURE_WOOCOMMERCE,
	GROUP_JETPACK,
	GROUP_WPCOM,
	JETPACK_LEGACY_PLANS,
	JETPACK_SECURITY_PLANS,
	PLAN_BLOGGER,
	PLAN_BLOGGER_2_YEARS,
	PLAN_BUSINESS,
	PLAN_BUSINESS_2_YEARS,
	PLAN_BUSINESS_3_YEARS,
	PLAN_100_YEARS,
	PLAN_BUSINESS_MONTHLY,
	PLAN_ECOMMERCE,
	PLAN_ECOMMERCE_2_YEARS,
	PLAN_ECOMMERCE_3_YEARS,
	PLAN_ECOMMERCE_MONTHLY,
	PLAN_ECOMMERCE_TRIAL_MONTHLY,
	PLAN_ENTERPRISE_GRID_WPCOM,
	PLAN_FREE,
	PLAN_JETPACK_BUSINESS,
	PLAN_JETPACK_BUSINESS_MONTHLY,
	PLAN_JETPACK_COMPLETE_BI_YEARLY,
	PLAN_JETPACK_COMPLETE,
	PLAN_JETPACK_COMPLETE_MONTHLY,
	PLAN_JETPACK_FREE,
	PLAN_JETPACK_GOLDEN_TOKEN,
	PLAN_JETPACK_GROWTH_MONTHLY,
	PLAN_JETPACK_GROWTH_YEARLY,
	PLAN_JETPACK_GROWTH_BI_YEARLY,
	PLAN_JETPACK_PERSONAL,
	PLAN_JETPACK_PERSONAL_MONTHLY,
	PLAN_JETPACK_PREMIUM,
	PLAN_JETPACK_PREMIUM_MONTHLY,
	PLAN_JETPACK_SECURITY_DAILY,
	PLAN_JETPACK_SECURITY_DAILY_MONTHLY,
	PLAN_JETPACK_SECURITY_REALTIME,
	PLAN_JETPACK_SECURITY_REALTIME_MONTHLY,
	PLAN_JETPACK_SECURITY_T1_MONTHLY,
	PLAN_JETPACK_SECURITY_T1_YEARLY,
	PLAN_JETPACK_SECURITY_T1_BI_YEARLY,
	PLAN_JETPACK_SECURITY_T2_MONTHLY,
	PLAN_JETPACK_SECURITY_T2_YEARLY,
	PLAN_JETPACK_STARTER_MONTHLY,
	PLAN_JETPACK_STARTER_YEARLY,
	PLAN_P2_FREE,
	PLAN_P2_PLUS,
	PLAN_PERSONAL,
	PLAN_PERSONAL_2_YEARS,
	PLAN_PERSONAL_3_YEARS,
	PLAN_PERSONAL_MONTHLY,
	PLAN_PREMIUM,
	PLAN_PREMIUM_2_YEARS,
	PLAN_PREMIUM_3_YEARS,
	PLAN_PREMIUM_MONTHLY,
	PLAN_WPCOM_FLEXIBLE,
	PLAN_WPCOM_STARTER,
	PLAN_WPCOM_PRO,
	PLAN_WPCOM_PRO_MONTHLY,
	PLAN_WPCOM_PRO_2_YEARS,
	PREMIUM_DESIGN_FOR_STORES,
	TERM_ANNUALLY,
	TERM_BIENNIALLY,
	TERM_TRIENNIALLY,
	TERM_MONTHLY,
	TYPE_ALL,
	TYPE_BLOGGER,
	TYPE_BUSINESS,
	TYPE_ECOMMERCE,
	TYPE_ENTERPRISE_GRID_WPCOM,
	TYPE_FREE,
	TYPE_P2_PLUS,
	TYPE_PERSONAL,
	TYPE_PREMIUM,
	TYPE_SECURITY_DAILY,
	TYPE_SECURITY_REALTIME,
	TYPE_SECURITY_T1,
	TYPE_SECURITY_T2,
	TYPE_JETPACK_GROWTH,
	TYPE_JETPACK_STARTER,
	TYPE_FLEXIBLE,
	TYPE_100_YEAR,
	TYPE_PRO,
	TYPE_STARTER,
	TYPE_GOLDEN_TOKEN,
	WPCOM_FEATURES_ATOMIC,
	WPCOM_FEATURES_SCAN,
	WPCOM_FEATURES_ANTISPAM,
	WPCOM_FEATURES_BACKUPS,
	FEATURE_MANAGED_HOSTING,
	FEATURE_UNLIMITED_EMAILS,
	FEATURE_UNLIMITED_SUBSCRIBERS,
	FEATURE_AD_FREE_EXPERIENCE,
	FEATURE_REAL_TIME_ANALYTICS,
	JETPACK_TAG_FOR_WOOCOMMERCE_STORES,
	JETPACK_TAG_FOR_NEWS_ORGANISATIONS,
	JETPACK_TAG_FOR_MEMBERSHIP_SITES,
	JETPACK_TAG_FOR_SMALL_SITES,
	JETPACK_TAG_FOR_BLOGS,
	PRODUCT_JETPACK_BACKUP_T0_MONTHLY,
	PRODUCT_JETPACK_BACKUP_T1_MONTHLY,
	PRODUCT_JETPACK_SCAN_MONTHLY,
	PRODUCT_JETPACK_ANTI_SPAM_MONTHLY,
	PRODUCT_JETPACK_BACKUP_T0_YEARLY,
	PRODUCT_JETPACK_BACKUP_T1_YEARLY,
	PRODUCT_JETPACK_SCAN,
	PRODUCT_JETPACK_ANTI_SPAM,
	PRODUCT_JETPACK_BACKUP_T2_YEARLY,
	PRODUCT_JETPACK_BACKUP_T2_MONTHLY,
	PRODUCT_JETPACK_VIDEOPRESS,
	PRODUCT_JETPACK_BOOST,
	PRODUCT_JETPACK_SOCIAL_ADVANCED,
	PRODUCT_JETPACK_SEARCH,
	PRODUCT_JETPACK_CRM,
	PRODUCT_JETPACK_CRM_MONTHLY,
	PRODUCT_JETPACK_SEARCH_MONTHLY,
	PRODUCT_JETPACK_SOCIAL_ADVANCED_MONTHLY,
	PRODUCT_JETPACK_SOCIAL_V1_MONTHLY,
	PRODUCT_JETPACK_SOCIAL_V1_YEARLY,
	PRODUCT_JETPACK_SOCIAL_V1_BI_YEARLY,
	PRODUCT_JETPACK_BOOST_MONTHLY,
	PRODUCT_JETPACK_VIDEOPRESS_MONTHLY,
	FEATURE_BEAUTIFUL_THEMES,
	FEATURE_PAGES,
	FEATURE_USERS,
	FEATURE_NEWSLETTERS_RSS,
	FEATURE_POST_EDITS_HISTORY,
	FEATURE_SECURITY_BRUTE_FORCE,
	FEATURE_SMART_REDIRECTS,
	FEATURE_ALWAYS_ONLINE,
	FEATURE_FAST_DNS,
	FEATURE_STYLE_CUSTOMIZATION,
	FEATURE_WORDADS,
	FEATURE_PLUGINS_THEMES,
	FEATURE_BANDWIDTH,
	FEATURE_BURST,
	FEATURE_WAF_V2,
	FEATURE_CDN,
	FEATURE_CPUS,
	FEATURE_DATACENTRE_FAILOVER,
	FEATURE_ISOLATED_INFRA,
	FEATURE_SECURITY_MALWARE,
	FEATURE_SECURITY_DDOS,
	FEATURE_DEV_TOOLS,
	FEATURE_DEV_TOOLS_SSH,
	FEATURE_DEV_TOOLS_GIT,
	FEATURE_WP_UPDATES,
	FEATURE_MULTI_SITE,
	FEATURE_SELL_SHIP,
	FEATURE_CUSTOM_STORE,
	FEATURE_INVENTORY,
	FEATURE_CHECKOUT,
	FEATURE_ACCEPT_PAYMENTS_V2,
	FEATURE_SALES_REPORTS,
	FEATURE_EXTENSIONS,
	FEATURE_STATS_JP,
	FEATURE_SPAM_JP,
	FEATURE_LTD_SOCIAL_MEDIA_JP,
	FEATURE_SHARES_SOCIAL_MEDIA_JP,
	FEATURE_CONTACT_FORM_JP,
	FEATURE_PAID_SUBSCRIBERS_JP,
	FEATURE_VIDEOPRESS_JP,
	FEATURE_UNLTD_SOCIAL_MEDIA_JP,
	FEATURE_SEO_JP,
	FEATURE_REALTIME_BACKUPS_JP,
	FEATURE_UPTIME_MONITOR_JP,
	FEATURE_ES_SEARCH_JP,
	FEATURE_PLUGIN_AUTOUPDATE_JP,
	FEATURE_PREMIUM_CONTENT_JP,
	FEATURE_ONE_CLICK_RESTORE_V2,
	FEATURE_SITE_ACTIVITY_LOG_JP,
	FEATURE_DONATIONS_AND_TIPS_JP,
	FEATURE_CLOUD_CRITICAL_CSS,
	FEATURE_GLOBAL_EDGE_CACHING,
	PLAN_WOOEXPRESS_MEDIUM_MONTHLY,
	PLAN_WOOEXPRESS_MEDIUM,
	PLAN_WOOEXPRESS_SMALL_MONTHLY,
	PLAN_WOOEXPRESS_SMALL,
	FEATURE_JETPACK_SOCIAL_ADVANCED,
	FEATURE_JETPACK_SOCIAL_ADVANCED_MONTHLY,
	FEATURE_JETPACK_BOOST_BI_YEARLY,
	FEATURE_JETPACK_BOOST,
	FEATURE_JETPACK_BOOST_MONTHLY,
	FEATURE_JETPACK_SOCIAL_BASIC_BI_YEARLY,
	FEATURE_JETPACK_SOCIAL_BASIC,
	FEATURE_JETPACK_SOCIAL_BASIC_MONTHLY,
	FEATURE_SELL_INTERNATIONALLY,
	FEATURE_AUTOMATIC_SALES_TAX,
	FEATURE_AUTOMATED_BACKUPS_SECURITY_SCAN,
	FEATURE_SELL_EGIFTS_AND_VOUCHERS,
	FEATURE_EMAIL_MARKETING,
	FEATURE_MARKETPLACE_SYNC_SOCIAL_MEDIA_INTEGRATION,
	FEATURE_INTEGRATED_SHIPMENT_TRACKING,
	FEATURE_BACK_IN_STOCK_NOTIFICATIONS,
	FEATURE_MARKETING_AUTOMATION,
	FEATURE_AUTOMATED_EMAIL_TRIGGERS,
	FEATURE_CART_ABANDONMENT_EMAILS,
	FEATURE_OFFER_BULK_DISCOUNTS,
	FEATURE_RECOMMEND_ADD_ONS,
	FEATURE_MIN_MAX_ORDER_QUANTITY,
	FEATURE_WOOCOMMERCE_STORE,
	FEATURE_WOOCOMMERCE_MOBILE_APP,
	FEATURE_WORDPRESS_CMS,
	FEATURE_WORDPRESS_MOBILE_APP,
	FEATURE_FREE_SSL_CERTIFICATE,
	FEATURE_GOOGLE_ANALYTICS_V3,
	FEATURE_LIST_UNLIMITED_PRODUCTS,
	FEATURE_GIFT_CARDS,
	FEATURE_PRODUCT_BUNDLES,
	FEATURE_LIST_PRODUCTS_BY_BRAND,
	FEATURE_PRODUCT_RECOMMENDATIONS,
	FEATURE_INTEGRATED_PAYMENTS,
	FEATURE_INTERNATIONAL_PAYMENTS,
	FEATURE_AUTOMATED_SALES_TAXES,
	FEATURE_ACCEPT_LOCAL_PAYMENTS,
	FEATURE_RECURRING_PAYMENTS,
	FEATURE_PROMOTE_ON_TIKTOK,
	FEATURE_SYNC_WITH_PINTEREST,
	FEATURE_CONNECT_WITH_FACEBOOK,
	FEATURE_ABANDONED_CART_RECOVERY,
	FEATURE_ADVERTISE_ON_GOOGLE,
	FEATURE_CUSTOM_ORDER_EMAILS,
	FEATURE_LIVE_SHIPPING_RATES,
	FEATURE_DISCOUNTED_SHIPPING,
	FEATURE_PRINT_SHIPPING_LABELS,
	PLAN_WOOEXPRESS_PLUS,
	TYPE_WOO_EXPRESS_PLUS,
	FEATURE_NEWSLETTER_IMPORT_SUBSCRIBERS_FREE,
	FEATURE_PAYMENT_TRANSACTION_FEES_10,
	FEATURE_PAYMENT_TRANSACTION_FEES_8,
	FEATURE_PAYMENT_TRANSACTION_FEES_4,
	FEATURE_PAYMENT_TRANSACTION_FEES_2,
	FEATURE_PAYMENT_TRANSACTION_FEES_0,
	TYPE_WOOEXPRESS_SMALL,
	TYPE_WOOEXPRESS_MEDIUM,
	FEATURE_PREMIUM_STORE_THEMES,
	FEATURE_STORE_DESIGN,
	FEATURE_UNLIMITED_PRODUCTS,
	FEATURE_DISPLAY_PRODUCTS_BRAND,
	FEATURE_PRODUCT_ADD_ONS,
	FEATURE_ASSEMBLED_KITS,
	FEATURE_STOCK_NOTIFS,
	FEATURE_DYNAMIC_UPSELLS,
	FEATURE_CUSTOM_MARKETING_AUTOMATION,
	FEATURE_BULK_DISCOUNTS,
	FEATURE_INVENTORY_MGMT,
	FEATURE_STREAMLINED_CHECKOUT,
	FEATURE_SELL_60_COUNTRIES,
	FEATURE_SHIPPING_INTEGRATIONS,
	FEATURE_THE_READER,
	PLAN_MIGRATION_TRIAL_MONTHLY,
	PLAN_HOSTING_TRIAL_MONTHLY,
	FEATURE_PAYMENT_BUTTONS_JP,
	FEATURE_PAYPAL_JP,
	FEATURE_PAYMENT_TRANSACTION_FEES_0_ALL,
	FEATURE_PAYMENT_TRANSACTION_FEES_2_REGULAR,
	FEATURE_PAYMENT_TRANSACTION_FEES_0_WOO,
	PRODUCT_JETPACK_STATS_MONTHLY,
	PRODUCT_JETPACK_STATS_YEARLY,
	TERM_CENTENNIALLY,
	FEATURE_STATS_PAID,
	FEATURE_STATS_COMMERCIAL,
	PRODUCT_JETPACK_SCAN_BI_YEARLY,
	PRODUCT_JETPACK_ANTI_SPAM_BI_YEARLY,
	PRODUCT_JETPACK_VIDEOPRESS_BI_YEARLY,
	PRODUCT_JETPACK_BOOST_BI_YEARLY,
	PRODUCT_JETPACK_SOCIAL_ADVANCED_BI_YEARLY,
	PRODUCT_JETPACK_SEARCH_BI_YEARLY,
	PRODUCT_JETPACK_STATS_BI_YEARLY,
	PRODUCT_JETPACK_BACKUP_T1_BI_YEARLY,
	PRODUCT_JETPACK_AI_BI_YEARLY,
	PRODUCT_JETPACK_AI_YEARLY,
	PRODUCT_JETPACK_AI_MONTHLY,
	FEATURE_JETPACK_SOCIAL_ADVANCED_BI_YEARLY,
	FEATURE_AI_ASSISTED_PRODUCT_DESCRIPTION,
	GROUP_P2,
	FEATURE_JETPACK_30_DAY_ARCHIVE_ACTIVITY_LOG,
	FEATURE_JETPACK_1_YEAR_ARCHIVE_ACTIVITY_LOG,
	FEATURE_COMMISSION_FEE_WOO_FEATURES,
	FEATURE_COMMISSION_FEE_STANDARD_FEATURES,
	FEATURE_SENSEI_SUPPORT,
	FEATURE_SENSEI_UNLIMITED,
	FEATURE_SENSEI_INTERACTIVE,
	FEATURE_SENSEI_QUIZZES,
	FEATURE_SENSEI_SELL_COURSES,
	FEATURE_SENSEI_STORAGE,
	FEATURE_SENSEI_HOSTING,
	FEATURE_SENSEI_JETPACK,
	WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED,
	FEATURE_PREMIUM_THEMES,
	WPCOM_FEATURES_PREMIUM_THEMES_LIMITED,
	FEATURE_REAL_TIME_SECURITY_SCANS,
	FEATURE_TIERED_STORAGE_PLANS_AVAILABLE,
	FEATURE_SEAMLESS_STAGING_PRODUCTION_SYNCING,
	FEATURE_SECURITY_VULNERABILITY_NOTIFICATIONS,
	FEATURE_WOOCOMMERCE_HOSTING,
	FEATURE_PRIORITY_24_7_SUPPORT,
	FEATURE_THEMES_PREMIUM_AND_STORE,
	FEATURE_UNLIMITED_ENTITIES,
	JETPACK_TAG_FOR_BLOGGERS,
	FEATURE_CONNECT_ANALYTICS,
	FEATURE_JETPACK_SOCIAL_V1_MONTHLY,
	FEATURE_BIG_SKY_WEBSITE_BUILDER,
	FEATURE_BIG_SKY_WEBSITE_BUILDER_CHECKOUT,
	FEATURE_UPLOAD_VIDEO,
	FEATURE_STATS_BASIC_20250206,
	FEATURE_STATS_ADVANCED_20250206,
	FEATURE_SUPPORT,
	FEATURE_SUPPORT_FROM_EXPERTS,
	FEATURE_AI_ASSISTANT,
	FEATURE_ADVANCED_FORM_FEATURES_JP,
	FEATURE_UPLOAD_PLUGINS_SUMMER_SPECIAL,
} from './constants';
import { isBigSkyOnboarding } from './is-big-sky-onboarding';
import { isGlobalStylesOnPersonalEnabled } from './is-global-styles-on-personal-enabled';
import {
	getPlanBusinessTitle,
	getPlanEcommerceTitle,
	getPlanPersonalTitle,
	getPlanPremiumTitle,
	getPlanBusinessTrialTitle,
	getPlanCommerceTrialTitle,
	getPlanBusinessTrialTagline,
} from './plans';
import type {
	BillingTerm,
	Plan,
	JetpackPlan,
	WPComPlan,
	IncompleteWPcomPlan,
	IncompleteJetpackPlan,
	Feature,
} from './types';
import type { TranslateResult } from 'i18n-calypso';

function isValueTruthy< T >( value: T ): value is Exclude< T, null | undefined | false | 0 | '' > {
	return !! value;
}

function compact( elements: ( string | false | undefined | null )[] ): string[] {
	return elements.filter( isValueTruthy );
}

const WPComGetBillingTimeframe = (): TranslateResult =>
	i18n.translate( 'per month, billed annually' );
const WPComGetBiennialBillingTimeframe = (): TranslateResult =>
	i18n.translate( '/month, billed every two years' );
const WPComGetTriennialBillingTimeframe = (): TranslateResult =>
	i18n.translate( '/month, billed every three years' );

const getBiAnnualTimeframe = (): BillingTerm => ( {
	term: TERM_BIENNIALLY,
	getBillingTimeFrame: () => translate( 'per 2 years' ),
} );

const getAnnualTimeframe = (): BillingTerm => ( {
	term: TERM_ANNUALLY,
	getBillingTimeFrame: () => translate( 'per year' ),
} );

const getMonthlyTimeframe = (): BillingTerm => ( {
	term: TERM_MONTHLY,
	getBillingTimeFrame: () => translate( 'per month, billed monthly' ),
} );
const getJetpackCommonPlanDetails = () => ( {
	getRecommendedFor: () => [
		{ tag: JETPACK_TAG_FOR_WOOCOMMERCE_STORES, label: translate( 'WooCommerce stores' ) },
		{ tag: JETPACK_TAG_FOR_NEWS_ORGANISATIONS, label: translate( 'News organizations' ) },
		{ tag: JETPACK_TAG_FOR_MEMBERSHIP_SITES, label: translate( 'Membership sites' ) },
	],
} );
const getDotcomPlanDetails = () => ( {
	// Features only available for annual plans
	getAnnualPlansOnlyFeatures: () => [
		FEATURE_FREE_DOMAIN,
		FEATURE_CUSTOM_DOMAIN,
		FEATURE_PRIORITY_24_7_SUPPORT,
		FEATURE_FAST_SUPPORT_FROM_EXPERTS,
	],
} );

/* eslint-disable wpcalypso/jsx-classname-namespace */
const plansDescriptionHeadingComponent = {
	components: {
		strong: <strong className="plans__features plan-features__targeted-description-heading" />,
	},
};
/* eslint-enable */

const isStatsFeatureTranslated = () => {
	const isEnglishLocale = i18n.getLocaleSlug()?.startsWith( 'en' );
	const hasStatsTranslation =
		i18n.hasTranslation( 'Full history, filters & peak times' ) ||
		i18n.hasTranslation( 'Last 7 days of basic stats' ) ||
		i18n.hasTranslation( 'Advanced insights, including UTM & device analytics' );

	return isEnglishLocale || hasStatsTranslation;
};

const getPlanFreeDetails = (): IncompleteWPcomPlan => ( {
	...getDotcomPlanDetails(),
	group: GROUP_WPCOM,
	type: TYPE_FREE,
	getTitle: () => i18n.translate( 'Free' ),
	getAudience: () => i18n.translate( 'Best for students' ),
	getBlogAudience: () => i18n.translate( 'Best for students' ),
	getPortfolioAudience: () => i18n.translate( 'Best for students' ),
	getStoreAudience: () => i18n.translate( 'Best for students' ),
	getPlanTagline: () => i18n.translate( 'Get started with all the basics.' ),
	getNewsletterTagLine: () =>
		i18n.translate( 'Start fresh or make the switch, bringing your first 100 readers with you.' ),
	getBlogOnboardingTagLine: () =>
		i18n.translate( 'Not a trial – blog free for as long as you like.' ),
	getDescription: () =>
		i18n.translate(
			'Get a free website and be on your way to publishing your ' +
				'first post in less than five minutes.'
		),
	getPlanCompareFeatures: () => [
		// pay attention to ordering, shared features should align on /plan page
		FEATURE_WP_SUBDOMAIN,
		FEATURE_JETPACK_ESSENTIAL,
		FEATURE_COMMUNITY_SUPPORT,
		FEATURE_FREE_THEMES,
		FEATURE_3GB_STORAGE,
	],
	getSignupFeatures: () => [
		FEATURE_COMMUNITY_SUPPORT,
		FEATURE_WP_SUBDOMAIN_SIGNUP,
		FEATURE_FREE_THEMES_SIGNUP,
	],
	getBlogSignupFeatures: () => [
		FEATURE_COMMUNITY_SUPPORT,
		FEATURE_WP_SUBDOMAIN_SIGNUP,
		FEATURE_FREE_THEMES_SIGNUP,
	],
	getPortfolioSignupFeatures: () => [
		FEATURE_COMMUNITY_SUPPORT,
		FEATURE_WP_SUBDOMAIN_SIGNUP,
		FEATURE_FREE_THEMES_SIGNUP,
	],
	get2023PricingGridSignupWpcomFeatures: () => {
		return [ FEATURE_UNLIMITED_ENTITIES ];
	},

	get2023PlanComparisonFeatureOverride: () => {
		return [
			FEATURE_AI_ASSISTANT,
			FEATURE_BEAUTIFUL_THEMES,
			FEATURE_PAGES,
			FEATURE_USERS,
			FEATURE_POST_EDITS_HISTORY,
			FEATURE_NEWSLETTERS_RSS,
			FEATURE_SECURITY_BRUTE_FORCE,
			FEATURE_SMART_REDIRECTS,
			FEATURE_ALWAYS_ONLINE,
			FEATURE_PAYMENT_TRANSACTION_FEES_10,
			FEATURE_GLOBAL_EDGE_CACHING,
			FEATURE_BURST,
			FEATURE_WAF_V2,
			FEATURE_CPUS,
			FEATURE_CDN,
			FEATURE_ES_SEARCH_JP,
			FEATURE_MULTI_SITE,
			FEATURE_WP_UPDATES,
			FEATURE_SECURITY_DDOS,
			FEATURE_SECURITY_MALWARE,
			FEATURE_DATACENTRE_FAILOVER,
			FEATURE_BANDWIDTH,
			FEATURE_FAST_DNS,
		];
	},
	get2023PricingGridSignupJetpackFeatures: () => {
		return [];
	},
	get2023PlanComparisonJetpackFeatureOverride: () => {
		return [
			FEATURE_PAID_SUBSCRIBERS_JP,
			FEATURE_DONATIONS_AND_TIPS_JP,
			FEATURE_PREMIUM_CONTENT_JP,
			FEATURE_PAYMENT_BUTTONS_JP,
			FEATURE_STATS_JP,
			FEATURE_SPAM_JP,
			FEATURE_CONTACT_FORM_JP,
			FEATURE_SITE_ACTIVITY_LOG_JP,
			FEATURE_UNLTD_SOCIAL_MEDIA_JP,
			FEATURE_ES_SEARCH_JP,
		];
	},
	getStorageFeature: () => FEATURE_1GB_STORAGE,
	getPlanComparisonFeatureLabels: () => {
		const baseFeatures = {
			[ FEATURE_SHARES_SOCIAL_MEDIA_JP ]: i18n.translate( '%d shares per month', { args: [ 30 ] } ),
			[ FEATURE_COMMISSION_FEE_STANDARD_FEATURES ]: formatNumber( 0.1, {
				numberFormatOptions: { style: 'percent' },
			} ),
			[ FEATURE_AI_ASSISTANT ]: i18n.translate( 'Limited to 20 requests' ),
		};

		return isStatsFeatureTranslated()
			? { ...baseFeatures, [ FEATURE_STATS_JP ]: i18n.translate( 'Last 7 days of basic stats' ) }
			: baseFeatures;
	},
	getNewsletterSignupFeatures: () => [
		FEATURE_NEWSLETTER_IMPORT_SUBSCRIBERS_FREE,
		FEATURE_PREMIUM_CONTENT_JP,
		FEATURE_NEWSLETTERS_RSS,
		FEATURE_UNLIMITED_EMAILS,
		FEATURE_STATS_JP,
		FEATURE_BANDWIDTH,
		FEATURE_LTD_SOCIAL_MEDIA_JP,
		FEATURE_PAYMENT_TRANSACTION_FEES_10,
	],
	getBlogOnboardingSignupFeatures: () => [
		FEATURE_CUSTOM_DOMAIN,
		FEATURE_BEAUTIFUL_THEMES,
		FEATURE_PAGES,
		FEATURE_USERS,
		FEATURE_POST_EDITS_HISTORY,
		FEATURE_SECURITY_BRUTE_FORCE,
		FEATURE_ALWAYS_ONLINE,
		FEATURE_THE_READER,
	],
	getBlogOnboardingSignupJetpackFeatures: () => [
		FEATURE_NEWSLETTER_IMPORT_SUBSCRIBERS_FREE,
		FEATURE_UNLIMITED_EMAILS,
		FEATURE_NEWSLETTERS_RSS,
		FEATURE_STATS_JP,
		FEATURE_LTD_SOCIAL_MEDIA_JP,
		FEATURE_SPAM_JP,
	],
	getIncludedFeatures: () => [],
	getInferiorFeatures: () => [],
} );

const getPlanBloggerDetails = (): IncompleteWPcomPlan => ( {
	...getDotcomPlanDetails(),
	group: GROUP_WPCOM,
	type: TYPE_BLOGGER,
	getTitle: () => i18n.translate( 'Blogger' ),
	// @TODO not updating copy for now, we need to update it after the first round of design {{{
	getAudience: () => i18n.translate( 'Best for bloggers' ),
	getBlogAudience: () => i18n.translate( 'Best for bloggers' ),
	getPortfolioAudience: () => i18n.translate( 'Best for bloggers' ),
	getStoreAudience: () => i18n.translate( 'Best for bloggers' ),
	getDescription: () =>
		i18n.translate(
			'{{strong}}Best for bloggers:{{/strong}} Brand your blog with a custom .blog domain name, and remove all WordPress.com advertising. Receive additional storage space and customer support via email.',
			plansDescriptionHeadingComponent
		),
	getShortDescription: () =>
		i18n.translate(
			'Brand your blog with a custom .blog domain name, and remove all WordPress.com advertising. Receive additional storage space and customer support via email.'
		),
	// }}}
	getPlanCompareFeatures: () => [
		// pay attention to ordering, shared features should align on /plan page
		FEATURE_BLOG_DOMAIN,
		FEATURE_JETPACK_ESSENTIAL,
		FEATURE_FAST_SUPPORT_FROM_EXPERTS,
		FEATURE_FREE_THEMES,
		FEATURE_6GB_STORAGE,
		FEATURE_NO_ADS,
		FEATURE_MEMBERSHIPS,
		FEATURE_PREMIUM_CONTENT_BLOCK,
	],
	getSignupFeatures: () => [
		FEATURE_FAST_SUPPORT_FROM_EXPERTS,
		FEATURE_BLOG_DOMAIN,
		FEATURE_ALL_FREE_FEATURES,
	],
	getBlogSignupFeatures: () => [
		FEATURE_FREE_BLOG_DOMAIN,
		FEATURE_FAST_SUPPORT_FROM_EXPERTS,
		FEATURE_ALL_FREE_FEATURES,
	],
	getPortfolioSignupFeatures: () => [
		FEATURE_FREE_BLOG_DOMAIN,
		FEATURE_FAST_SUPPORT_FROM_EXPERTS,
		FEATURE_ALL_FREE_FEATURES,
	],
	// Features not displayed but used for checking plan abilities
	getIncludedFeatures: () => [ FEATURE_AUDIO_UPLOADS ],
	getInferiorFeatures: () => [],
	getCancellationFeatures: () => [
		FEATURE_FAST_SUPPORT_FROM_EXPERTS,
		FEATURE_AUDIO_UPLOADS,
		FEATURE_NO_ADS,
		FEATURE_MEMBERSHIPS,
	],
} );

const getPlanPersonalDetails = (): IncompleteWPcomPlan => ( {
	...getDotcomPlanDetails(),
	group: GROUP_WPCOM,
	type: TYPE_PERSONAL,
	getTitle: getPlanPersonalTitle,
	getAudience: () => i18n.translate( 'Best for personal use' ),
	getBlogAudience: () => i18n.translate( 'Best for personal use' ),
	getPortfolioAudience: () => i18n.translate( 'Best for personal use' ),
	getStoreAudience: () => i18n.translate( 'Best for personal use' ),
	getPlanTagline: () => i18n.translate( 'Make your mark with a custom domain.' ),
	getNewsletterTagLine: () =>
		i18n.translate( 'Monetize your writing, go ad-free, and expand your media content.' ),
	getBlogOnboardingTagLine: () =>
		i18n.translate(
			'Take the next step with gated content, paid subscribers, and an ad-free site.'
		),
	getDescription: () =>
		i18n.translate(
			'{{strong}}Best for personal use:{{/strong}} Boost your' +
				' website with a custom domain name, and remove all WordPress.com advertising. ' +
				'Unlock unlimited, expert customer support via email.',
			plansDescriptionHeadingComponent
		),
	getShortDescription: () =>
		i18n.translate(
			'Boost your website with a custom domain name, and remove all WordPress.com advertising. ' +
				'Unlock unlimited, expert customer support via email.'
		),
	getPlanCompareFeatures: () =>
		compact(
			[
				// pay attention to ordering, shared features should align on /plan page
				FEATURE_CUSTOM_DOMAIN,
				FEATURE_HOSTING,
				FEATURE_JETPACK_ESSENTIAL,
				FEATURE_FAST_SUPPORT_FROM_EXPERTS,
				FEATURE_FREE_THEMES,
				isGlobalStylesOnPersonalEnabled() ? FEATURE_STYLE_CUSTOMIZATION : null,
				FEATURE_6GB_STORAGE,
				FEATURE_NO_ADS,
				FEATURE_MEMBERSHIPS,
				FEATURE_PREMIUM_CONTENT_BLOCK,
				FEATURE_PAYMENT_TRANSACTION_FEES_8,
			].filter( ( feature ) => feature !== null )
		),
	getSignupFeatures: () => {
		const baseFeatures = [
			FEATURE_FREE_DOMAIN,
			FEATURE_FAST_SUPPORT_FROM_EXPERTS,
			FEATURE_FREE_THEMES,
		];

		return isGlobalStylesOnPersonalEnabled()
			? [ ...baseFeatures, FEATURE_STYLE_CUSTOMIZATION ]
			: baseFeatures;
	},
	getBlogSignupFeatures: () => [
		FEATURE_FREE_DOMAIN,
		FEATURE_FAST_SUPPORT_FROM_EXPERTS,
		FEATURE_ALL_FREE_FEATURES,
	],
	getPortfolioSignupFeatures: () => [
		FEATURE_FREE_DOMAIN,
		FEATURE_FAST_SUPPORT_FROM_EXPERTS,
		FEATURE_ALL_FREE_FEATURES,
	],
	getSignupCompareAvailableFeatures: () => {
		const baseFeatures = [
			FEATURE_CUSTOM_DOMAIN,
			FEATURE_HOSTING,
			FEATURE_NO_ADS,
			FEATURE_COLLECT_PAYMENTS_V2,
			FEATURE_FAST_SUPPORT_FROM_EXPERTS,
		];

		return isGlobalStylesOnPersonalEnabled()
			? [ ...baseFeatures, FEATURE_STYLE_CUSTOMIZATION ]
			: baseFeatures;
	},
	get2023PricingGridSignupWpcomFeatures: ( props?: { isSummerSpecial?: boolean } ) => {
		const baseFeatures = [
			...( isBigSkyOnboarding() ? [ FEATURE_BIG_SKY_WEBSITE_BUILDER ] : [] ),
			FEATURE_UNLIMITED_ENTITIES,
			FEATURE_CUSTOM_DOMAIN,
			FEATURE_AD_FREE_EXPERIENCE,
			WPCOM_FEATURES_PREMIUM_THEMES_LIMITED,
			FEATURE_SUPPORT_FROM_EXPERTS,
			FEATURE_STATS_BASIC_20250206,
		];

		let features = baseFeatures;

		if ( props?.isSummerSpecial ) {
			features = [ FEATURE_UPLOAD_PLUGINS_SUMMER_SPECIAL, ...features ];
		}

		if ( isGlobalStylesOnPersonalEnabled() ) {
			features = [ ...features, FEATURE_STYLE_CUSTOMIZATION ];
		}

		return features;
	},
	get2023PricingGridSignupJetpackFeatures: () => {
		return [];
	},
	get2023PlanComparisonFeatureOverride: ( props?: { isSummerSpecial?: boolean } ) => {
		const baseFeatures = [
			FEATURE_CUSTOM_DOMAIN,
			FEATURE_AD_FREE_EXPERIENCE,
			FEATURE_FAST_DNS,
			FEATURE_PAYMENT_TRANSACTION_FEES_8,
			FEATURE_PREMIUM_THEMES,
			FEATURE_SUPPORT,
		];

		let features = baseFeatures;

		if ( isGlobalStylesOnPersonalEnabled() ) {
			features = [ ...features, FEATURE_STYLE_CUSTOMIZATION ];
		}

		if ( props?.isSummerSpecial ) {
			features = [ ...features, FEATURE_PLUGINS_THEMES ];
		}

		return features;
	},
	get2023PlanComparisonJetpackFeatureOverride: () => {
		return [ FEATURE_ADVANCED_FORM_FEATURES_JP ];
	},
	getStorageFeature: () => FEATURE_6GB_STORAGE,
	getPlanComparisonFeatureLabels: () => {
		const baseFeatures = {
			[ FEATURE_PREMIUM_THEMES ]: i18n.translate( 'Dozens of premium themes' ),
			[ FEATURE_SHARES_SOCIAL_MEDIA_JP ]: i18n.translate( '%d shares per month', { args: [ 30 ] } ),
			[ FEATURE_COMMISSION_FEE_STANDARD_FEATURES ]: formatNumber( 0.08, {
				numberFormatOptions: { style: 'percent' },
			} ),
			[ FEATURE_SUPPORT ]: i18n.translate( 'Support from our expert\u00A0team' ),
		};

		return isStatsFeatureTranslated()
			? {
					...baseFeatures,
					[ FEATURE_STATS_JP ]: i18n.translate( 'Full history, filters & peak times' ),
			  }
			: baseFeatures;
	},

	getNewsletterSignupFeatures: () => [
		FEATURE_CUSTOM_DOMAIN,
		FEATURE_UNLIMITED_SUBSCRIBERS,
		FEATURE_AD_FREE_EXPERIENCE,
		FEATURE_PAYMENT_TRANSACTION_FEES_8,
	],
	getNewsletterHighlightedFeatures: () => [
		FEATURE_CUSTOM_DOMAIN,
		FEATURE_UNLIMITED_EMAILS,
		FEATURE_AD_FREE_EXPERIENCE,
	],
	getBlogOnboardingSignupFeatures: () => [
		FEATURE_CUSTOM_DOMAIN,
		FEATURE_AD_FREE_EXPERIENCE,
		FEATURE_FAST_DNS,
		FEATURE_PAYMENT_TRANSACTION_FEES_8,
	],
	getBlogOnboardingHighlightedFeatures: () => [ FEATURE_CUSTOM_DOMAIN ],
	getBlogOnboardingSignupJetpackFeatures: () => [
		FEATURE_UNLIMITED_SUBSCRIBERS,
		FEATURE_PREMIUM_CONTENT_JP,
		FEATURE_PAID_SUBSCRIBERS_JP,
	],
	getCheckoutFeatures: () => {
		const baseFeatures = [
			...( isBigSkyOnboarding() ? [ FEATURE_BIG_SKY_WEBSITE_BUILDER_CHECKOUT ] : [] ),
			FEATURE_CUSTOM_DOMAIN,
			...( isEnabled( 'summer-special-2025' ) ? [ FEATURE_PLUGINS_THEMES ] : [] ),
			FEATURE_AD_FREE_EXPERIENCE,
			FEATURE_FAST_DNS,
			FEATURE_PAID_SUBSCRIBERS_JP,
		];

		return isEnabled( 'stats/paid-wpcom-v3' )
			? [ ...baseFeatures, FEATURE_STATS_PAID ]
			: baseFeatures;
	},
	// Features not displayed but used for checking plan abilities
	getIncludedFeatures: () => [ FEATURE_AUDIO_UPLOADS ],
	getInferiorFeatures: () => [],
	getCancellationFeatures: () => [
		FEATURE_FAST_SUPPORT_FROM_EXPERTS,
		FEATURE_AD_FREE_EXPERIENCE,
		FEATURE_AUDIO_UPLOADS,
	],
} );

const getPlanEcommerceDetails = (): IncompleteWPcomPlan => ( {
	...getDotcomPlanDetails(),
	group: GROUP_WPCOM,
	type: TYPE_ECOMMERCE,
	getTitle: getPlanEcommerceTitle,
	getAudience: () => i18n.translate( 'Best for online stores' ),
	getBlogAudience: () => i18n.translate( 'Best for online stores' ),
	getPortfolioAudience: () => i18n.translate( 'Best for online stores' ),
	getStoreAudience: () => i18n.translate( 'Best for online stores' ),
	getPlanTagline: () =>
		i18n.translate( 'Grow your online store with commerce-optimized extensions.' ),
	getDescription: () => {
		return i18n.translate(
			'{{strong}}Best for online stores:{{/strong}} Sell products or services with this powerful, ' +
				'all-in-one online store experience. This plan includes premium integrations and is extendable, ' +
				'so it’ll grow with you as your business grows.',
			plansDescriptionHeadingComponent
		);
	},
	getShortDescription: () =>
		i18n.translate(
			'Sell products or services with this powerful, ' +
				'all-in-one online store experience. This plan includes premium integrations and is extendable, ' +
				'so it’ll grow with you as your business grows.'
		),
	getTagline: function () {
		return i18n.translate(
			'Learn more about everything included with %(planName)s and take advantage of its powerful marketplace features.',
			{ args: { planName: this.getTitle() } }
		);
	},
	getPlanCompareFeatures: ( _, { isLoggedInMonthlyPricing } = {} ) =>
		compact( [
			// pay attention to ordering, shared features should align on /plan page
			FEATURE_CUSTOM_DOMAIN,
			FEATURE_HOSTING,
			FEATURE_JETPACK_ADVANCED,
			isLoggedInMonthlyPricing && FEATURE_FAST_SUPPORT_FROM_EXPERTS,
			isEnabled( 'themes/premium' ) ? WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED : null,
			FEATURE_50GB_STORAGE,
			FEATURE_NO_ADS,
			FEATURE_MEMBERSHIPS,
			FEATURE_PREMIUM_CONTENT_BLOCK,
			FEATURE_ADVANCED_DESIGN_CUSTOMIZATION,
			isLoggedInMonthlyPricing && FEATURE_PRIORITY_24_7_SUPPORT,
			FEATURE_SIMPLE_PAYMENTS,
			FEATURE_GOOGLE_ANALYTICS,
			FEATURE_REPUBLICIZE,
			FEATURE_WORDADS_INSTANT,
			FEATURE_VIDEO_UPLOADS,
			FEATURE_ADVANCED_SEO,
			FEATURE_UPLOAD_PLUGINS,
			FEATURE_UPLOAD_THEMES,
			FEATURE_SFTP_DATABASE,
			FEATURE_NO_BRANDING,
			FEATURE_ACCEPT_PAYMENTS,
			FEATURE_SHIPPING_CARRIERS,
			FEATURE_UNLIMITED_PRODUCTS_SERVICES,
			FEATURE_ECOMMERCE_MARKETING,
			FEATURE_PREMIUM_CUSTOMIZABE_THEMES,
			FEATURE_PAYMENT_TRANSACTION_FEES_0,
		] ),
	getPromotedFeatures: () => [
		FEATURE_200GB_STORAGE,
		FEATURE_CUSTOM_DOMAIN,
		FEATURE_NO_ADS,
		FEATURE_ADVANCED_DESIGN_CUSTOMIZATION,
	],
	getSignupFeatures: () => [
		FEATURE_ACCEPT_PAYMENTS,
		FEATURE_SHIPPING_CARRIERS,
		FEATURE_ALL_BUSINESS_FEATURES,
	],
	getBlogSignupFeatures: () => [
		FEATURE_ACCEPT_PAYMENTS,
		FEATURE_SHIPPING_CARRIERS,
		FEATURE_ALL_BUSINESS_FEATURES,
	],
	getPortfolioSignupFeatures: () => [
		FEATURE_ACCEPT_PAYMENTS,
		FEATURE_SHIPPING_CARRIERS,
		FEATURE_ALL_BUSINESS_FEATURES,
	],
	getSignupCompareAvailableFeatures: () =>
		[
			FEATURE_CUSTOM_DOMAIN,
			FEATURE_HOSTING,
			FEATURE_NO_ADS,
			FEATURE_COLLECT_PAYMENTS_V2,
			FEATURE_FAST_SUPPORT_FROM_EXPERTS,
			FEATURE_PRIORITY_24_7_SUPPORT,
			FEATURE_EARN_AD,
			isEnabled( 'themes/premium' ) ? WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED : null,
			FEATURE_GOOGLE_ANALYTICS,
			FEATURE_INSTALL_PLUGINS,
			FEATURE_ADVANCED_SEO_EXPANDED_ABBR,
			FEATURE_SITE_BACKUPS_AND_RESTORE,
			FEATURE_SFTP_DATABASE,
			FEATURE_ACCEPT_PAYMENTS,
			FEATURE_SHIPPING_CARRIERS,
			PREMIUM_DESIGN_FOR_STORES,
		].filter( isValueTruthy ),
	get2023PricingGridSignupWpcomFeatures: ( props?: { isSummerSpecial?: boolean } ) => {
		return [
			FEATURE_UNLIMITED_ENTITIES,
			FEATURE_CUSTOM_DOMAIN,
			FEATURE_AD_FREE_EXPERIENCE,
			FEATURE_THEMES_PREMIUM_AND_STORE,
			FEATURE_PRIORITY_24_7_SUPPORT,
			FEATURE_STYLE_CUSTOMIZATION,
			FEATURE_CONNECT_ANALYTICS,
			FEATURE_UPLOAD_VIDEO,
			FEATURE_STATS_ADVANCED_20250206,
			props?.isSummerSpecial ? FEATURE_UPLOAD_PLUGINS_SUMMER_SPECIAL : FEATURE_UPLOAD_PLUGINS,
			FEATURE_DEV_TOOLS,
			FEATURE_WOOCOMMERCE_HOSTING,
		];
	},
	get2023PlanComparisonFeatureOverride: () => {
		return [
			FEATURE_CUSTOM_DOMAIN,
			FEATURE_WOOCOMMERCE_HOSTING,
			FEATURE_PREMIUM_STORE_THEMES,
			FEATURE_STORE_DESIGN,
			FEATURE_UNLIMITED_PRODUCTS,
			FEATURE_DISPLAY_PRODUCTS_BRAND,
			FEATURE_PRODUCT_ADD_ONS,
			FEATURE_ASSEMBLED_KITS,
			FEATURE_MIN_MAX_ORDER_QUANTITY,
			FEATURE_STOCK_NOTIFS,
			FEATURE_DYNAMIC_UPSELLS,
			FEATURE_CUSTOM_MARKETING_AUTOMATION,
			FEATURE_BULK_DISCOUNTS,
			FEATURE_INVENTORY_MGMT,
			FEATURE_STREAMLINED_CHECKOUT,
			FEATURE_SELL_60_COUNTRIES,
			FEATURE_SHIPPING_INTEGRATIONS,
			FEATURE_PAYMENT_TRANSACTION_FEES_0_ALL,
			FEATURE_SUPPORT,
		];
	},
	getCheckoutFeatures: () => [
		FEATURE_CUSTOM_DOMAIN,
		FEATURE_PRIORITY_24_7_SUPPORT,
		FEATURE_PLUGINS_THEMES,
		FEATURE_ACCEPT_PAYMENTS,
		FEATURE_SHIPPING_CARRIERS,
		FEATURE_UNLIMITED_PRODUCTS_SERVICES,
		FEATURE_INVENTORY,
		FEATURE_CUSTOM_MARKETING_AUTOMATION,
	],
	get2023PricingGridSignupJetpackFeatures: () => [],
	getStorageFeature: ( showLegacyStorageFeature, isCurrentPlan ) => {
		if ( showLegacyStorageFeature && isCurrentPlan ) {
			return FEATURE_200GB_STORAGE;
		}
		return isEnabled( 'plans/updated-storage-labels' )
			? FEATURE_50GB_STORAGE
			: FEATURE_200GB_STORAGE;
	},
	getPlanComparisonFeatureLabels: () => {
		const baseFeatures = {
			[ FEATURE_PREMIUM_THEMES ]: i18n.translate( 'All premium themes' ),
			[ FEATURE_SHARES_SOCIAL_MEDIA_JP ]: i18n.translate( 'Unlimited shares' ),
			[ FEATURE_COMMISSION_FEE_STANDARD_FEATURES ]: formatNumber( 0, {
				numberFormatOptions: { style: 'percent' },
			} ),
			[ FEATURE_COMMISSION_FEE_WOO_FEATURES ]: formatNumber( 0, {
				numberFormatOptions: { style: 'percent' },
			} ),
			[ FEATURE_SUPPORT ]: i18n.translate( 'Priority 24/7 support from our expert\u00A0team' ),
		};

		return isStatsFeatureTranslated()
			? {
					...baseFeatures,
					[ FEATURE_STATS_JP ]: i18n.translate(
						'Advanced insights, including UTM & device analytics'
					),
			  }
			: baseFeatures;
	},
	getHostingSignupFeatures: ( term ) => () =>
		compact( [
			term !== TERM_MONTHLY && FEATURE_CUSTOM_DOMAIN,
			FEATURE_SELL_SHIP,
			FEATURE_CUSTOM_STORE,
			FEATURE_INVENTORY,
			FEATURE_CHECKOUT,
			FEATURE_ACCEPT_PAYMENTS_V2,
			FEATURE_SALES_REPORTS,
			FEATURE_SHIPPING_CARRIERS,
			FEATURE_EXTENSIONS,
			FEATURE_BANDWIDTH,
			FEATURE_GLOBAL_EDGE_CACHING,
			FEATURE_BURST,
			FEATURE_WAF_V2,
			FEATURE_CDN,
			FEATURE_CPUS,
			FEATURE_DATACENTRE_FAILOVER,
			FEATURE_SECURITY_MALWARE,
			FEATURE_SECURITY_DDOS,
		] ),
	// Features not displayed but used for checking plan abilities
	getIncludedFeatures: () => [
		FEATURE_AUDIO_UPLOADS,
		FEATURE_GOOGLE_MY_BUSINESS,
		WPCOM_FEATURES_INSTALL_PURCHASED_PLUGINS,
		FEATURE_UPLOAD_THEMES_PLUGINS,
		FEATURE_EMAIL_FORWARDING_EXTENDED_LIMIT,
		FEATURE_SEO_PREVIEW_TOOLS,
		WPCOM_FEATURES_ATOMIC,
		WPCOM_FEATURES_SCAN,
		WPCOM_FEATURES_ANTISPAM,
		WPCOM_FEATURES_BACKUPS,
	],
	getInferiorFeatures: () => [],
	getCancellationFeatures: () => [
		FEATURE_ACCEPT_PAYMENTS_V2,
		FEATURE_SHIPPING_CARRIERS,
		FEATURE_ECOMMERCE_MARKETING,
		FEATURE_SELL_SHIP,
		FEATURE_PLUGINS_THEMES,
		FEATURE_ADVANCED_SEO_TOOLS,
	],
} );

const getWooExpressMediumPlanCompareFeatures = (): string[] => [
	FEATURE_WOOCOMMERCE_STORE,
	FEATURE_WOOCOMMERCE_MOBILE_APP,
	FEATURE_WORDPRESS_CMS,
	FEATURE_WORDPRESS_MOBILE_APP,
	FEATURE_CUSTOM_DOMAIN,
	FEATURE_FREE_SSL_CERTIFICATE,
	FEATURE_AUTOMATED_BACKUPS_SECURITY_SCAN,
	FEATURE_AD_FREE_EXPERIENCE,
	FEATURE_UNLIMITED_ADMINS,
	FEATURE_PRIORITY_24_7_SUPPORT,
	FEATURE_FAST_SUPPORT_FROM_EXPERTS,
	WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED,
	FEATURE_SALES_REPORTS,
	FEATURE_GOOGLE_ANALYTICS_V3,
	FEATURE_LIST_UNLIMITED_PRODUCTS,
	FEATURE_GIFT_CARDS,
	FEATURE_MIN_MAX_ORDER_QUANTITY,
	FEATURE_PRODUCT_BUNDLES,
	FEATURE_LIST_PRODUCTS_BY_BRAND,
	FEATURE_PRODUCT_RECOMMENDATIONS,
	FEATURE_INTEGRATED_PAYMENTS,
	FEATURE_INTERNATIONAL_PAYMENTS,
	FEATURE_AUTOMATED_SALES_TAXES,
	FEATURE_ACCEPT_LOCAL_PAYMENTS,
	FEATURE_RECURRING_PAYMENTS,
	FEATURE_MIN_MAX_ORDER_QUANTITY,
	FEATURE_PROMOTE_ON_TIKTOK,
	FEATURE_SYNC_WITH_PINTEREST,
	FEATURE_CONNECT_WITH_FACEBOOK,
	FEATURE_BACK_IN_STOCK_NOTIFICATIONS,
	FEATURE_MARKETING_AUTOMATION,
	FEATURE_ABANDONED_CART_RECOVERY,
	FEATURE_ADVANCED_SEO_TOOLS,
	FEATURE_ADVERTISE_ON_GOOGLE,
	FEATURE_CUSTOM_ORDER_EMAILS,
	FEATURE_INTEGRATED_SHIPMENT_TRACKING,
	FEATURE_LIVE_SHIPPING_RATES,
	FEATURE_DISCOUNTED_SHIPPING,
	FEATURE_PRINT_SHIPPING_LABELS,
	FEATURE_AI_ASSISTED_PRODUCT_DESCRIPTION,
];
const getWooExpressSmallPlanCompareFeatures = (): string[] => [
	FEATURE_WOOCOMMERCE_STORE,
	FEATURE_WOOCOMMERCE_MOBILE_APP,
	FEATURE_WORDPRESS_CMS,
	FEATURE_WORDPRESS_MOBILE_APP,
	FEATURE_CUSTOM_DOMAIN,
	FEATURE_FREE_SSL_CERTIFICATE,
	FEATURE_AUTOMATED_BACKUPS_SECURITY_SCAN,
	FEATURE_AD_FREE_EXPERIENCE,
	FEATURE_UNLIMITED_ADMINS,
	FEATURE_PRIORITY_24_7_SUPPORT,
	FEATURE_FAST_SUPPORT_FROM_EXPERTS,
	WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED,
	FEATURE_SALES_REPORTS,
	FEATURE_GOOGLE_ANALYTICS_V3,
	FEATURE_LIST_UNLIMITED_PRODUCTS,
	FEATURE_GIFT_CARDS,
	FEATURE_LIST_PRODUCTS_BY_BRAND,
	FEATURE_INTEGRATED_PAYMENTS,
	FEATURE_INTERNATIONAL_PAYMENTS,
	FEATURE_AUTOMATED_SALES_TAXES,
	FEATURE_ACCEPT_LOCAL_PAYMENTS,
	FEATURE_RECURRING_PAYMENTS,
	FEATURE_PROMOTE_ON_TIKTOK,
	FEATURE_SYNC_WITH_PINTEREST,
	FEATURE_CONNECT_WITH_FACEBOOK,
	FEATURE_ADVANCED_SEO_TOOLS,
	FEATURE_ADVERTISE_ON_GOOGLE,
	FEATURE_CUSTOM_ORDER_EMAILS,
	FEATURE_INTEGRATED_SHIPMENT_TRACKING,
	FEATURE_LIVE_SHIPPING_RATES,
	FEATURE_PRINT_SHIPPING_LABELS,
	FEATURE_AI_ASSISTED_PRODUCT_DESCRIPTION,
];

const getWooExpressPlanCompareFeatures = (): string[] => [
	...getWooExpressSmallPlanCompareFeatures(),
	...getWooExpressMediumPlanCompareFeatures(),
];

const getPlanWooExpressMediumDetails = (): IncompleteWPcomPlan => ( {
	...getPlanEcommerceDetails(),
	getTitle: () => i18n.translate( 'Performance' ),
	getPlanTagline: () => i18n.translate( 'Accelerate your growth with advanced features.' ),
	get2023PricingGridSignupWpcomFeatures: () => [
		FEATURE_200GB_STORAGE,
		FEATURE_BACK_IN_STOCK_NOTIFICATIONS,
		FEATURE_MARKETING_AUTOMATION,
		FEATURE_AUTOMATED_EMAIL_TRIGGERS,
		FEATURE_CART_ABANDONMENT_EMAILS,
		FEATURE_OFFER_BULK_DISCOUNTS,
		FEATURE_RECOMMEND_ADD_ONS,
		FEATURE_MIN_MAX_ORDER_QUANTITY,
	],
	getPlanCompareFeatures: () => getWooExpressPlanCompareFeatures(),
	get2023PlanComparisonFeatureOverride: () => getWooExpressMediumPlanCompareFeatures(),
	getStorageFeature: () => FEATURE_200GB_STORAGE,
	getTagline: () =>
		i18n.translate(
			'Learn more about everything included with Woo Express Performance and take advantage of its powerful marketplace features.'
		),
} );

const getPlanWooExpressSmallDetails = (): IncompleteWPcomPlan => ( {
	...getPlanEcommerceDetails(),
	get2023PricingGridSignupWpcomFeatures: () => [
		FEATURE_CUSTOM_DOMAIN,
		FEATURE_PRIORITY_24_7_SUPPORT,
		FEATURE_UNLIMITED_ADMINS,
		FEATURE_50GB_STORAGE,
		FEATURE_UNLIMITED_PRODUCTS_SERVICES,
		WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED,
		FEATURE_SELL_INTERNATIONALLY,
		FEATURE_AUTOMATIC_SALES_TAX,
		FEATURE_AUTOMATED_BACKUPS_SECURITY_SCAN,
		FEATURE_INTEGRATED_SHIPMENT_TRACKING,
		FEATURE_REAL_TIME_ANALYTICS,
		FEATURE_SELL_EGIFTS_AND_VOUCHERS,
		FEATURE_EMAIL_MARKETING,
		FEATURE_MARKETPLACE_SYNC_SOCIAL_MEDIA_INTEGRATION,
		FEATURE_ADVANCED_SEO_TOOLS,
		FEATURE_AI_ASSISTED_PRODUCT_DESCRIPTION,
	],
	getPlanCompareFeatures: () => getWooExpressPlanCompareFeatures(),
	get2023PlanComparisonFeatureOverride: () => getWooExpressSmallPlanCompareFeatures(),
	getStorageFeature: () => FEATURE_50GB_STORAGE,
	getTitle: () => i18n.translate( 'Essential' ),
	getPlanTagline: () =>
		i18n.translate( 'Everything you need to set up your store and start selling your products.' ),
	getTagline: () =>
		i18n.translate(
			'Learn more about everything included with Woo Express Essential and take advantage of its powerful marketplace features.'
		),
} );

const getPlanPremiumDetails = (): IncompleteWPcomPlan => ( {
	...getDotcomPlanDetails(),
	group: GROUP_WPCOM,
	type: TYPE_PREMIUM,
	getTitle: getPlanPremiumTitle,
	getAudience: () => i18n.translate( 'Best for freelancers' ),
	getBlogAudience: () => i18n.translate( 'Best for freelancers' ),
	getPortfolioAudience: () => i18n.translate( 'Best for freelancers' ),
	getStoreAudience: () => i18n.translate( 'Best for freelancers' ),
	getPlanTagline: () =>
		i18n.translate( 'Step up site customization with premium design features.' ),
	getNewsletterTagLine: () =>
		i18n.translate( 'Make it even more memorable with premium designs and style customization.' ),
	getBlogOnboardingTagLine: () =>
		i18n.translate(
			'Make it even more memorable with premium designs, 4K video, and style customization.'
		),
	getDescription: () =>
		i18n.translate(
			'{{strong}}Best for freelancers:{{/strong}} Build a unique website with advanced design tools, CSS editing, lots of space for audio and video,' +
				' Google Analytics support,' +
				' and the ability to monetize your site with ads.',
			plansDescriptionHeadingComponent
		),
	getShortDescription: () =>
		i18n.translate(
			'Build a unique website with advanced design tools, CSS editing, lots of space for audio and video,' +
				' Google Analytics support,' +
				' and the ability to monetize your site with ads.'
		),
	getPlanCompareFeatures: ( _, { isLoggedInMonthlyPricing } = {} ) =>
		compact( [
			// pay attention to ordering, shared features should align on /plan page
			FEATURE_CUSTOM_DOMAIN,
			FEATURE_HOSTING,
			FEATURE_JETPACK_ESSENTIAL,
			isLoggedInMonthlyPricing && FEATURE_FAST_SUPPORT_FROM_EXPERTS,
			isEnabled( 'themes/premium' ) ? WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED : null,
			FEATURE_13GB_STORAGE,
			FEATURE_NO_ADS,
			FEATURE_MEMBERSHIPS,
			FEATURE_PREMIUM_CONTENT_BLOCK,
			FEATURE_ADVANCED_DESIGN_CUSTOMIZATION,
			isLoggedInMonthlyPricing && FEATURE_PRIORITY_24_7_SUPPORT,
			FEATURE_SIMPLE_PAYMENTS,
			FEATURE_GOOGLE_ANALYTICS,
			FEATURE_REPUBLICIZE,
			FEATURE_WORDADS_INSTANT,
			FEATURE_VIDEO_UPLOADS,
			FEATURE_PAYMENT_TRANSACTION_FEES_4,
		] ),
	getPromotedFeatures: () => [
		FEATURE_CUSTOM_DOMAIN,
		FEATURE_NO_ADS,
		FEATURE_ADVANCED_DESIGN_CUSTOMIZATION,
		FEATURE_13GB_STORAGE,
	],
	getSignupFeatures: () => [
		FEATURE_PRIORITY_24_7_SUPPORT,
		FEATURE_ADVANCED_DESIGN_CUSTOMIZATION,
		FEATURE_ALL_PERSONAL_FEATURES,
	],
	getTagline: function () {
		return i18n.translate(
			'Take your Newsletter further, faster. Get everything included in %(planName)s, plus premium design themes, baked-in video uploads, ad monetization, deep visitor insights from Google Analytics, and 24/7 expert support.',
			{ args: { planName: this.getTitle() } }
		);
	},
	getNewsletterSignupFeatures: () => [
		FEATURE_FAST_SUPPORT_FROM_EXPERTS,
		FEATURE_STYLE_CUSTOMIZATION,
		WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED,
		FEATURE_UNLTD_SOCIAL_MEDIA_JP,
		FEATURE_UPLOAD_VIDEO,
		isEnabled( 'stats/paid-wpcom-v3' ) ? FEATURE_STATS_COMMERCIAL : FEATURE_STATS_PAID,
		FEATURE_PAYMENT_TRANSACTION_FEES_4,
	],
	getNewsletterHighlightedFeatures: () => [
		FEATURE_CUSTOM_DOMAIN,
		FEATURE_UNLIMITED_EMAILS,
		FEATURE_AD_FREE_EXPERIENCE,
		FEATURE_FAST_SUPPORT_FROM_EXPERTS,
		FEATURE_REAL_TIME_ANALYTICS,
		WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED,
	],

	getBlogOnboardingSignupFeatures: () => [
		FEATURE_CUSTOM_DOMAIN,
		WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED,
		FEATURE_STYLE_CUSTOMIZATION,
		FEATURE_FAST_SUPPORT_FROM_EXPERTS,
		FEATURE_WORDADS,
		FEATURE_PAYMENT_TRANSACTION_FEES_4,
	],
	getBlogOnboardingHighlightedFeatures: () => [ FEATURE_CUSTOM_DOMAIN ],
	getBlogOnboardingSignupJetpackFeatures: () => [
		FEATURE_VIDEOPRESS_JP,
		FEATURE_UNLTD_SOCIAL_MEDIA_JP,
		FEATURE_SITE_ACTIVITY_LOG_JP,
		isEnabled( 'stats/paid-wpcom-v3' ) ? FEATURE_STATS_COMMERCIAL : FEATURE_STATS_PAID,
	],
	getBlogSignupFeatures: () =>
		[
			FEATURE_MONETISE,
			isEnabled( 'themes/premium' ) ? WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED : null,
			FEATURE_ALL_PERSONAL_FEATURES,
		].filter( isValueTruthy ),
	getPortfolioSignupFeatures: () =>
		[
			FEATURE_ADVANCED_DESIGN_CUSTOMIZATION,
			isEnabled( 'themes/premium' ) ? WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED : null,
			FEATURE_ALL_PERSONAL_FEATURES,
		].filter( isValueTruthy ),
	getSignupCompareAvailableFeatures: () =>
		[
			FEATURE_CUSTOM_DOMAIN,
			FEATURE_HOSTING,
			FEATURE_NO_ADS,
			FEATURE_COLLECT_PAYMENTS_V2,
			FEATURE_FAST_SUPPORT_FROM_EXPERTS,
			FEATURE_PRIORITY_24_7_SUPPORT,
			FEATURE_EARN_AD,
			isEnabled( 'themes/premium' ) ? WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED : null,
			FEATURE_GOOGLE_ANALYTICS,
		].filter( isValueTruthy ),
	get2023PricingGridSignupWpcomFeatures: ( props?: { isSummerSpecial?: boolean } ) => {
		const baseFeatures = [
			...( isBigSkyOnboarding() ? [ FEATURE_BIG_SKY_WEBSITE_BUILDER ] : [] ),
			FEATURE_UNLIMITED_ENTITIES,
			FEATURE_CUSTOM_DOMAIN,
			FEATURE_AD_FREE_EXPERIENCE,
			WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED,
			FEATURE_FAST_SUPPORT_FROM_EXPERTS,
			FEATURE_STYLE_CUSTOMIZATION,
			FEATURE_CONNECT_ANALYTICS,
			FEATURE_UPLOAD_VIDEO,
			FEATURE_STATS_ADVANCED_20250206,
		];

		let features = baseFeatures;

		if ( props?.isSummerSpecial ) {
			features = [ FEATURE_UPLOAD_PLUGINS_SUMMER_SPECIAL, ...features ];
		}

		return features;
	},
	get2023PlanComparisonFeatureOverride: ( props?: { isSummerSpecial?: boolean } ) => {
		const baseFeatures = [
			FEATURE_CUSTOM_DOMAIN,
			FEATURE_FAST_SUPPORT_FROM_EXPERTS,
			WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED,
			FEATURE_WORDADS,
			FEATURE_CONNECT_ANALYTICS,
			FEATURE_STYLE_CUSTOMIZATION,
			FEATURE_PAYMENT_TRANSACTION_FEES_4,
			FEATURE_SUPPORT,
		];

		let features = baseFeatures;

		if ( props?.isSummerSpecial ) {
			features = [ ...features, FEATURE_PLUGINS_THEMES ];
		}

		return features;
	},
	getCheckoutFeatures: () => [
		...( isBigSkyOnboarding() ? [ FEATURE_BIG_SKY_WEBSITE_BUILDER_CHECKOUT ] : [] ),
		FEATURE_CUSTOM_DOMAIN,
		...( isEnabled( 'summer-special-2025' ) ? [ FEATURE_PLUGINS_THEMES ] : [] ),
		FEATURE_FAST_SUPPORT_FROM_EXPERTS,
		WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED,
		FEATURE_WORDADS,
		FEATURE_STYLE_CUSTOMIZATION,
		isEnabled( 'stats/paid-wpcom-v3' ) ? FEATURE_STATS_COMMERCIAL : FEATURE_STATS_PAID,
		FEATURE_UPLOAD_VIDEO,
		FEATURE_UNLTD_SOCIAL_MEDIA_JP,
		FEATURE_SITE_ACTIVITY_LOG_JP,
	],
	get2023PricingGridSignupJetpackFeatures: () => {
		return [];
	},
	getStorageFeature: () => FEATURE_13GB_STORAGE,
	getPlanComparisonFeatureLabels: () => {
		const baseFeatures = {
			[ FEATURE_PREMIUM_THEMES ]: i18n.translate( 'All premium themes' ),
			[ FEATURE_SHARES_SOCIAL_MEDIA_JP ]: i18n.translate( 'Unlimited shares' ),
			[ FEATURE_COMMISSION_FEE_STANDARD_FEATURES ]: formatNumber( 0.04, {
				numberFormatOptions: { style: 'percent' },
			} ),
			[ FEATURE_SUPPORT ]: i18n.translate( 'Fast support from our expert\u00A0team' ),
		};

		return isStatsFeatureTranslated()
			? {
					...baseFeatures,
					[ FEATURE_STATS_JP ]: i18n.translate(
						'Advanced insights, including UTM & device analytics'
					),
			  }
			: baseFeatures;
	},
	get2023PlanComparisonJetpackFeatureOverride: () => {
		return [
			FEATURE_PAYPAL_JP,
			FEATURE_UPLOAD_VIDEO,
			isEnabled( 'stats/paid-wpcom-v3' ) ? FEATURE_STATS_COMMERCIAL : FEATURE_STATS_PAID,
			FEATURE_ADVANCED_FORM_FEATURES_JP,
		];
	},
	// Features not displayed but used for checking plan abilities
	getIncludedFeatures: () => [
		FEATURE_AUDIO_UPLOADS,
		WPCOM_FEATURES_SCAN,
		WPCOM_FEATURES_ANTISPAM,
		WPCOM_FEATURES_BACKUPS,
	],
	getInferiorFeatures: () => [],
	getCancellationFeatures: () => [
		FEATURE_FAST_SUPPORT_FROM_EXPERTS,
		WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED,
		FEATURE_STYLE_CUSTOMIZATION,
		FEATURE_WORDADS_INSTANT,
		FEATURE_AD_FREE_EXPERIENCE,
	],
} );

const getPlanBusinessDetails = (): IncompleteWPcomPlan => ( {
	...getDotcomPlanDetails(),
	group: GROUP_WPCOM,
	type: TYPE_BUSINESS,
	getTitle: getPlanBusinessTitle,
	getAudience: () => i18n.translate( 'Best for small businesses' ),
	getBlogAudience: () => i18n.translate( 'Best for small businesses' ),
	getPortfolioAudience: () => i18n.translate( 'Best for small businesses' ),
	getStoreAudience: () => i18n.translate( 'The plan for small businesses' ),
	getPlanTagline: () =>
		i18n.translate( 'Unlock next-level WordPress with custom plugins and themes.' ),
	getBlogOnboardingTagLine: () =>
		i18n.translate( 'Expand your blog with plugins and powerful tools to help you scale.' ),
	getDescription: () =>
		i18n.translate(
			'{{strong}}Best for small businesses:{{/strong}} Power your' +
				' business website with custom plugins and themes,' +
				' %(nmOfGB)s GB storage, and the ability to remove WordPress.com branding.',
			{
				...plansDescriptionHeadingComponent,
				args: {
					nmOfGB: isEnabled( 'plans/updated-storage-labels' ) ? '50' : '200',
				},
			}
		),
	getShortDescription: () =>
		i18n.translate(
			'Power your business website with custom plugins and themes,' +
				' %(nmOfGB)s GB storage, and the ability to remove WordPress.com branding.',
			{ args: { nmOfGB: isEnabled( 'plans/updated-storage-labels' ) ? '50' : '200' } }
		),
	getTagline: function () {
		return i18n.translate(
			'Learn more about everything included with %(planName)s and take advantage of its powerful marketplace features.',
			{ args: { planName: this.getTitle() } }
		);
	},
	getPlanCompareFeatures: ( _, { isLoggedInMonthlyPricing } = {} ) =>
		compact( [
			// pay attention to ordering, shared features should align on /plan page
			FEATURE_CUSTOM_DOMAIN,
			FEATURE_HOSTING,
			FEATURE_JETPACK_ADVANCED,
			isLoggedInMonthlyPricing && FEATURE_FAST_SUPPORT_FROM_EXPERTS,
			isEnabled( 'themes/premium' ) ? WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED : null,
			FEATURE_50GB_STORAGE,
			FEATURE_NO_ADS,
			FEATURE_MEMBERSHIPS,
			FEATURE_PREMIUM_CONTENT_BLOCK,
			FEATURE_ADVANCED_DESIGN_CUSTOMIZATION,
			isLoggedInMonthlyPricing && FEATURE_PRIORITY_24_7_SUPPORT,
			FEATURE_SIMPLE_PAYMENTS,
			FEATURE_GOOGLE_ANALYTICS,
			FEATURE_REPUBLICIZE,
			FEATURE_WORDADS_INSTANT,
			FEATURE_VIDEO_UPLOADS,
			FEATURE_ADVANCED_SEO,
			FEATURE_UPLOAD_PLUGINS,
			FEATURE_UPLOAD_THEMES,
			FEATURE_SFTP_DATABASE,
			FEATURE_NO_BRANDING,
			FEATURE_PAYMENT_TRANSACTION_FEES_2,
		] ),
	getPromotedFeatures: () => [
		FEATURE_200GB_STORAGE,
		FEATURE_CUSTOM_DOMAIN,
		FEATURE_NO_ADS,
		FEATURE_ADVANCED_DESIGN_CUSTOMIZATION,
		FEATURE_VIDEO_UPLOADS,
	],
	getSignupFeatures: () => [
		FEATURE_UPLOAD_THEMES_PLUGINS,
		FEATURE_ADVANCED_SEO_TOOLS,
		FEATURE_ALL_PREMIUM_FEATURES,
	],
	getBlogSignupFeatures: () => [
		FEATURE_UPLOAD_THEMES_PLUGINS,
		FEATURE_ADVANCED_SEO_TOOLS,
		FEATURE_ALL_PREMIUM_FEATURES,
	],
	getPortfolioSignupFeatures: () => [
		FEATURE_UPLOAD_THEMES_PLUGINS,
		FEATURE_200GB_STORAGE,
		FEATURE_ALL_PREMIUM_FEATURES,
	],
	getSignupCompareAvailableFeatures: () =>
		[
			FEATURE_CUSTOM_DOMAIN,
			FEATURE_HOSTING,
			FEATURE_NO_ADS,
			FEATURE_COLLECT_PAYMENTS_V2,
			FEATURE_FAST_SUPPORT_FROM_EXPERTS,
			FEATURE_PRIORITY_24_7_SUPPORT,
			FEATURE_EARN_AD,
			isEnabled( 'themes/premium' ) ? WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED : null,
			FEATURE_GOOGLE_ANALYTICS,
			FEATURE_INSTALL_PLUGINS,
			FEATURE_ADVANCED_SEO_EXPANDED_ABBR,
			FEATURE_SITE_BACKUPS_AND_RESTORE,
			FEATURE_SFTP_DATABASE,
		].filter( isValueTruthy ),
	get2023PricingGridSignupWpcomFeatures: ( props?: { isSummerSpecial?: boolean } ) => {
		return [
			...( isBigSkyOnboarding() ? [ FEATURE_BIG_SKY_WEBSITE_BUILDER ] : [] ),
			FEATURE_UNLIMITED_ENTITIES,
			FEATURE_CUSTOM_DOMAIN,
			FEATURE_AD_FREE_EXPERIENCE,
			WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED,
			FEATURE_PRIORITY_24_7_SUPPORT,
			FEATURE_STYLE_CUSTOMIZATION,
			FEATURE_CONNECT_ANALYTICS,
			FEATURE_UPLOAD_VIDEO,
			FEATURE_STATS_ADVANCED_20250206,
			props?.isSummerSpecial ? FEATURE_UPLOAD_PLUGINS_SUMMER_SPECIAL : FEATURE_UPLOAD_PLUGINS,
			FEATURE_DEV_TOOLS,
		];
	},
	get2023PlanComparisonFeatureOverride: () => {
		return [
			FEATURE_CUSTOM_DOMAIN,
			FEATURE_PRIORITY_24_7_SUPPORT,
			FEATURE_PLUGINS_THEMES,
			FEATURE_BANDWIDTH,
			FEATURE_UNLIMITED_TRAFFIC,
			FEATURE_GLOBAL_EDGE_CACHING,
			FEATURE_CDN,
			FEATURE_DATACENTRE_FAILOVER,
			FEATURE_ISOLATED_INFRA,
			FEATURE_SECURITY_MALWARE,
			FEATURE_TIERED_STORAGE_PLANS_AVAILABLE,
			FEATURE_REAL_TIME_SECURITY_SCANS,
			FEATURE_SPAM_JP,
			FEATURE_SECURITY_DDOS,
			FEATURE_DEV_TOOLS_GIT,
			FEATURE_DEV_TOOLS_SSH,
			FEATURE_SITE_STAGING_SITES,
			FEATURE_SEAMLESS_STAGING_PRODUCTION_SYNCING,
			FEATURE_WP_UPDATES,
			FEATURE_MULTI_SITE,
			FEATURE_SECURITY_VULNERABILITY_NOTIFICATIONS,
			FEATURE_PAYMENT_TRANSACTION_FEES_0_WOO,
			FEATURE_PAYMENT_TRANSACTION_FEES_2_REGULAR,
			FEATURE_COMMISSION_FEE_WOO_FEATURES,
			FEATURE_COMMISSION_FEE_STANDARD_FEATURES,
		];
	},
	getCheckoutFeatures: () => [
		...( isBigSkyOnboarding() ? [ FEATURE_BIG_SKY_WEBSITE_BUILDER_CHECKOUT ] : [] ),
		FEATURE_CUSTOM_DOMAIN,
		FEATURE_PLUGINS_THEMES,
		FEATURE_BANDWIDTH,
		FEATURE_CDN,
		FEATURE_ADVANCED_SEO_TOOLS,
		FEATURE_PRIORITY_24_7_SUPPORT,
		FEATURE_DEV_TOOLS,
		FEATURE_REALTIME_BACKUPS_JP,
		FEATURE_SITE_ACTIVITY_LOG_JP,
		FEATURE_SECURITY_DDOS,
		FEATURE_SITE_STAGING_SITES,
	],
	get2023PricingGridSignupJetpackFeatures: () => {
		return [];
	},
	get2023PlanComparisonJetpackFeatureOverride: () => {
		return [
			FEATURE_REALTIME_BACKUPS_JP,
			FEATURE_ONE_CLICK_RESTORE_V2,
			FEATURE_UPTIME_MONITOR_JP,
			FEATURE_PLUGIN_AUTOUPDATE_JP,
			FEATURE_SEO_JP,
			FEATURE_ADVANCED_FORM_FEATURES_JP,
		];
	},
	getPlanComparisonFeatureLabels: () => {
		const featureLabels: Record< Feature, TranslateResult > = {
			[ FEATURE_PREMIUM_THEMES ]: i18n.translate( 'All premium themes' ),
			[ FEATURE_PREMIUM_STORE_THEMES ]: i18n.translate( 'Available with plugins' ),
			[ FEATURE_UNLIMITED_PRODUCTS ]: i18n.translate( 'Available with plugins' ),
			[ FEATURE_DISPLAY_PRODUCTS_BRAND ]: i18n.translate( 'Available with plugins' ),
			[ FEATURE_PRODUCT_ADD_ONS ]: i18n.translate( 'Available with paid plugins' ),
			[ FEATURE_ASSEMBLED_KITS ]: i18n.translate( 'Available with paid plugins' ),
			[ FEATURE_MIN_MAX_ORDER_QUANTITY ]: i18n.translate( 'Available with paid plugins' ),
			[ FEATURE_STOCK_NOTIFS ]: i18n.translate( 'Available with paid plugins' ),
			[ FEATURE_DYNAMIC_UPSELLS ]: i18n.translate( 'Available with paid plugins' ),
			[ FEATURE_CUSTOM_MARKETING_AUTOMATION ]: i18n.translate( 'Available with paid plugins' ),
			[ FEATURE_BULK_DISCOUNTS ]: i18n.translate( 'Available with paid plugins' ),
			[ FEATURE_INVENTORY_MGMT ]: i18n.translate( 'Available with plugins' ),
			[ FEATURE_STREAMLINED_CHECKOUT ]: i18n.translate( 'Available with plugins' ),
			[ FEATURE_SELL_60_COUNTRIES ]: i18n.translate( 'Available with plugins' ),
			[ FEATURE_SHIPPING_INTEGRATIONS ]: i18n.translate( 'Available with paid plugins' ),
			[ FEATURE_SHARES_SOCIAL_MEDIA_JP ]: i18n.translate( 'Unlimited shares' ),
			[ FEATURE_STORE_DESIGN ]: i18n.translate( 'Available with plugins' ),
			[ FEATURE_COMMISSION_FEE_STANDARD_FEATURES ]: formatNumber( 0.02, {
				numberFormatOptions: { style: 'percent' },
			} ),
			[ FEATURE_COMMISSION_FEE_WOO_FEATURES ]: formatNumber( 0, {
				numberFormatOptions: { style: 'percent' },
			} ),
			[ FEATURE_SUPPORT ]: i18n.translate( 'Priority 24/7 support from our expert team' ),
		};

		return isStatsFeatureTranslated()
			? {
					...featureLabels,
					[ FEATURE_STATS_JP ]: i18n.translate(
						'Advanced insights, including UTM & device analytics'
					),
			  }
			: featureLabels;
	},

	getStorageFeature: ( showLegacyStorageFeature, isCurrentPlan ) => {
		if ( showLegacyStorageFeature ) {
			/* If the user is currently has a legacy plan with 200GB storage space, the capacity will decrease to
			 * 50GB if they change their billing terms.
			 */
			return isCurrentPlan ? FEATURE_200GB_STORAGE : FEATURE_50GB_STORAGE;
		}

		return isEnabled( 'plans/updated-storage-labels' )
			? FEATURE_50GB_STORAGE
			: FEATURE_200GB_STORAGE;
	},
	getHostingSignupFeatures: ( term ) => () =>
		compact( [
			term !== TERM_MONTHLY && FEATURE_CUSTOM_DOMAIN,
			FEATURE_PLUGINS_THEMES,
			FEATURE_BANDWIDTH,
			FEATURE_GLOBAL_EDGE_CACHING,
			FEATURE_BURST,
			FEATURE_WAF_V2,
			FEATURE_CDN,
			FEATURE_CPUS,
			FEATURE_DATACENTRE_FAILOVER,
			FEATURE_ISOLATED_INFRA,
			FEATURE_SECURITY_MALWARE,
			FEATURE_SECURITY_DDOS,
			FEATURE_DEV_TOOLS,
			FEATURE_SITE_STAGING_SITES,
			FEATURE_WP_UPDATES,
			FEATURE_MULTI_SITE,
		] ),
	getBlogOnboardingSignupFeatures: () => [
		FEATURE_CUSTOM_DOMAIN,
		FEATURE_PLUGINS_THEMES,
		FEATURE_SECURITY_MALWARE,
		FEATURE_WP_UPDATES,
		FEATURE_PAYMENT_TRANSACTION_FEES_2,
	],
	getBlogOnboardingSignupJetpackFeatures: () => [
		FEATURE_SEO_JP,
		FEATURE_PLUGIN_AUTOUPDATE_JP,
		FEATURE_REALTIME_BACKUPS_JP,
		FEATURE_ONE_CLICK_RESTORE_V2,
		FEATURE_ES_SEARCH_JP,
	],
	// Features not displayed but used for checking plan abilities
	getIncludedFeatures: () => [
		FEATURE_AUDIO_UPLOADS,
		FEATURE_GOOGLE_MY_BUSINESS,
		WPCOM_FEATURES_INSTALL_PURCHASED_PLUGINS,
		FEATURE_EMAIL_FORWARDING_EXTENDED_LIMIT,
		FEATURE_SEO_PREVIEW_TOOLS,
		WPCOM_FEATURES_ATOMIC,
		WPCOM_FEATURES_SCAN,
		WPCOM_FEATURES_ANTISPAM,
		WPCOM_FEATURES_BACKUPS,
	],
	getInferiorFeatures: () => [],
	getSenseiFeatures: ( term ) => () =>
		compact( [
			term !== TERM_MONTHLY && FEATURE_CUSTOM_DOMAIN,
			term !== TERM_MONTHLY && FEATURE_SENSEI_SUPPORT,
			FEATURE_SENSEI_UNLIMITED,
			FEATURE_SENSEI_INTERACTIVE,
			FEATURE_SENSEI_QUIZZES,
			FEATURE_SENSEI_SELL_COURSES,
			FEATURE_SENSEI_STORAGE,
			FEATURE_SENSEI_HOSTING,
			FEATURE_SENSEI_JETPACK,
		] ),
	getSenseiHighlightedFeatures: () => [ FEATURE_CUSTOM_DOMAIN, FEATURE_SENSEI_SUPPORT ],
	getCancellationFeatures: () => [
		FEATURE_PLUGINS_THEMES,
		FEATURE_ADVANCED_SEO_TOOLS,
		FEATURE_DEV_TOOLS,
		FEATURE_SEAMLESS_STAGING_PRODUCTION_SYNCING,
		FEATURE_SITE_BACKUPS_AND_RESTORE,
		FEATURE_AD_FREE_EXPERIENCE,
		FEATURE_FAST_SUPPORT_FROM_EXPERTS,
	],
} );

const getPlanProDetails = (): IncompleteWPcomPlan => ( {
	...getDotcomPlanDetails(),
	group: GROUP_WPCOM,
	type: TYPE_PRO,
	getTitle: () => i18n.translate( 'WordPress Pro' ),
	getTagline: () =>
		i18n.translate(
			'This plan gives you access to our most powerful features at an affordable price for an unmatched value you won’t get anywhere else. No longer available to new users.'
		),
	getDescription: () =>
		i18n.translate(
			'You’ve got our best deal on hosting! ' +
				'Your Pro plan includes access to all the most popular features WordPress.com has to offer, including premium themes and access to over 50,000 plugins. ' +
				'As an existing customer, you can keep your site on this plan as long as your subscription remains active.'
		),
	getSubTitle: () => i18n.translate( 'Unlimited features. Unbeatable value.' ),
	getPlanCompareFeatures: () => [
		FEATURE_UNLIMITED_TRAFFIC,
		FEATURE_MANAGED_HOSTING,
		FEATURE_FREE_THEMES,
		FEATURE_CUSTOM_DOMAIN,
		WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED,
		FEATURE_INSTALL_PLUGINS,
		WPCOM_FEATURES_INSTALL_PURCHASED_PLUGINS,
		FEATURE_PREMIUM_SUPPORT,
		FEATURE_WOOCOMMERCE,
		FEATURE_50GB_STORAGE,
		FEATURE_NO_ADS,
		FEATURE_ADVANCED_SEO,
		FEATURE_UNLIMITED_ADMINS,
		FEATURE_VIDEO_UPLOADS,
		FEATURE_PAYMENT_BLOCKS,
		FEATURE_SOCIAL_MEDIA_TOOLS,
		FEATURE_TITAN_EMAIL,
		FEATURE_MONETISE,
		FEATURE_SFTP_DATABASE,
		FEATURE_SITE_BACKUPS_AND_RESTORE,
		FEATURE_JETPACK_ESSENTIAL,
		FEATURE_SIMPLE_PAYMENTS,
		FEATURE_WORDADS_INSTANT,
		FEATURE_GOOGLE_ANALYTICS,
	],
	getIncludedFeatures: () => [
		FEATURE_ADVANCED_SEO_EXPANDED_ABBR,
		FEATURE_AUDIO_UPLOADS,
		FEATURE_COLLECT_PAYMENTS_V2,
		FEATURE_EARN_AD,
		FEATURE_EMAIL_FORWARDING_EXTENDED_LIMIT,
		FEATURE_GOOGLE_ANALYTICS,
		FEATURE_GOOGLE_MY_BUSINESS,
		FEATURE_HOSTING,
		FEATURE_ADVANCED_DESIGN_CUSTOMIZATION,
		FEATURE_MEMBERSHIPS,
		FEATURE_NO_BRANDING,
		FEATURE_REPUBLICIZE,
		FEATURE_PREMIUM_CONTENT_BLOCK,
		FEATURE_SEO_PREVIEW_TOOLS,
		FEATURE_SFTP_DATABASE,
		FEATURE_SITE_BACKUPS_AND_RESTORE,
		FEATURE_UPLOAD_PLUGINS,
		FEATURE_UPLOAD_THEMES,
		FEATURE_UPLOAD_THEMES_PLUGINS,
		WPCOM_FEATURES_ATOMIC,
		WPCOM_FEATURES_SCAN,
		WPCOM_FEATURES_ANTISPAM,
		WPCOM_FEATURES_BACKUPS,
	],
	getPlanCancellationDescription: () =>
		i18n.translate(
			'Heads up — you are currently on a legacy plan that is no longer available for new subscribers. ' +
				'Your Pro plan includes access to all the most popular features WordPress.com has to offer, ' +
				'including premium themes and access to over 50,000 plugins. As an existing Pro plan subscriber, ' +
				'you can keep your site on this legacy plan as long as your subscription remains active. ' +
				'If canceled, the WordPress.com Pro plan can no longer be added to your account.'
		),
} );

// The following is not a real plan, we are adding it here so that
// Woo Express Plus gets its own column in the plans grid.
const getPlanWooExpressPlusDetails = (): IncompleteWPcomPlan => ( {
	...getDotcomPlanDetails(),
	group: GROUP_WPCOM,
	type: TYPE_WOO_EXPRESS_PLUS,
	getTitle: () => i18n.translate( 'Plus' ),
	getPlanTagline: () =>
		i18n.translate( 'For fast-growing businesses that need access to the most powerful tools.' ),
	getDescription: () => '',
	get2023PricingGridSignupWpcomFeatures: () => [],
	get2023PricingGridSignupJetpackFeatures: () => [],
} );

// The following is not a real plan, we are adding it here so that
// VIP (a.k.a Enterprise) gets its own column in the plans grid.
// Check pdgrnI-1Qp-p2 for more details.
const get2023EnterprisGrideDetails = (): IncompleteWPcomPlan => ( {
	...getDotcomPlanDetails(),
	group: GROUP_WPCOM,
	type: TYPE_ENTERPRISE_GRID_WPCOM,
	getTitle: () => i18n.translate( 'Enterprise' ),
	getAudience: () => i18n.translate( 'Best for enterprises' ),
	getPlanTagline: () =>
		i18n.translate( 'Level up to bespoke Enterprise-grade performance and security.' ),
	getDescription: () => '',
	get2023PricingGridSignupWpcomFeatures: () => [],
	get2023PricingGridSignupJetpackFeatures: () => [],
} );

const getJetpackPersonalDetails = (): IncompleteJetpackPlan => ( {
	group: GROUP_JETPACK,
	type: TYPE_PERSONAL,
	getTitle: () => i18n.translate( 'Personal' ),
	availableFor: ( plan ) => [ PLAN_JETPACK_FREE ].includes( plan ),
	getDescription: () =>
		i18n.translate(
			'{{strong}}Best for personal use:{{/strong}} Security essentials for your WordPress site, including ' +
				'automated backups and priority support.',
			plansDescriptionHeadingComponent
		),
	getTagline: () =>
		i18n.translate(
			'Your data is being securely backed up and you have access to priority support.'
		),
	getPlanCardFeatures: () => [ FEATURE_BACKUP_DAILY_V2, FEATURE_ANTISPAM_V2 ],
	getBillingTimeFrame: () => i18n.translate( 'per year' ),
	getIncludedFeatures: () => [
		FEATURE_OFFSITE_BACKUP_VAULTPRESS_DAILY,
		FEATURE_BACKUP_ARCHIVE_30,
		FEATURE_BACKUP_STORAGE_SPACE_UNLIMITED,
		FEATURE_AUTOMATED_RESTORES,
		FEATURE_SPAM_AKISMET_PLUS,
		FEATURE_EASY_SITE_MIGRATION,
		FEATURE_PREMIUM_SUPPORT,
		FEATURE_JETPACK_BACKUP_DAILY,
		FEATURE_JETPACK_BACKUP_DAILY_MONTHLY,
		FEATURE_JETPACK_ANTI_SPAM_BI_YEARLY,
		FEATURE_JETPACK_ANTI_SPAM,
		FEATURE_JETPACK_ANTI_SPAM_MONTHLY,
		FEATURE_SEO_PREVIEW_TOOLS,
		FEATURE_ADVANCED_SEO,
		FEATURE_OFFSITE_BACKUP_VAULTPRESS_DAILY,
		FEATURE_SPAM_AKISMET_PLUS,
		FEATURE_ACTIVITY_LOG,
		FEATURE_PREMIUM_SUPPORT,
		FEATURE_ALL_FREE_FEATURES_JETPACK,
		WPCOM_FEATURES_ANTISPAM,
		WPCOM_FEATURES_BACKUPS,
	],
} );

const getJetpackPremiumDetails = (): IncompleteJetpackPlan => ( {
	group: GROUP_JETPACK,
	type: TYPE_PREMIUM,
	availableFor: ( plan ) =>
		[ PLAN_JETPACK_FREE, PLAN_JETPACK_PERSONAL, PLAN_JETPACK_PERSONAL_MONTHLY ].includes( plan ),
	getTitle: () => i18n.translate( 'Premium' ),
	getDescription: () =>
		i18n.translate(
			'{{strong}}Best for small businesses:{{/strong}} Comprehensive, automated scanning for security vulnerabilities, ' +
				'fast video hosting, and marketing automation.',
			plansDescriptionHeadingComponent
		),
	getTagline: () =>
		i18n.translate(
			'Your site is being secured and you have access to marketing tools and priority support.'
		),
	getPlanCardFeatures: () => [ FEATURE_BACKUP_DAILY_V2, FEATURE_SCAN_V2, FEATURE_ANTISPAM_V2 ],
	getIncludedFeatures: () =>
		compact( [
			// pay attention to ordering, shared features should align on /plan page
			FEATURE_OFFSITE_BACKUP_VAULTPRESS_DAILY,
			FEATURE_BACKUP_ARCHIVE_30,
			FEATURE_BACKUP_STORAGE_SPACE_UNLIMITED,
			FEATURE_AUTOMATED_RESTORES,
			FEATURE_SPAM_AKISMET_PLUS,
			FEATURE_EASY_SITE_MIGRATION,
			FEATURE_PREMIUM_SUPPORT,
			FEATURE_REPUBLICIZE,
			FEATURE_SIMPLE_PAYMENTS,
			FEATURE_WORDADS_INSTANT,
			FEATURE_VIDEO_UPLOADS_JETPACK_PRO,
			FEATURE_MALWARE_SCANNING_DAILY,
			FEATURE_ADVANCED_SEO,
			FEATURE_GOOGLE_ANALYTICS,
			FEATURE_JETPACK_BACKUP_DAILY,
			FEATURE_JETPACK_BACKUP_DAILY_MONTHLY,
			FEATURE_JETPACK_SCAN_DAILY,
			FEATURE_JETPACK_SCAN_DAILY_MONTHLY,
			FEATURE_JETPACK_ANTI_SPAM_BI_YEARLY,
			FEATURE_JETPACK_ANTI_SPAM,
			FEATURE_JETPACK_ANTI_SPAM_MONTHLY,
			FEATURE_JETPACK_VIDEOPRESS_BI_YEARLY,
			FEATURE_JETPACK_VIDEOPRESS,
			FEATURE_JETPACK_VIDEOPRESS_MONTHLY,
			FEATURE_SEO_PREVIEW_TOOLS,
			FEATURE_OFFSITE_BACKUP_VAULTPRESS_DAILY,
			FEATURE_SPAM_AKISMET_PLUS,
			FEATURE_MALWARE_SCANNING_DAILY,
			FEATURE_AUTOMATIC_SECURITY_FIXES,
			FEATURE_VIDEO_UPLOADS_JETPACK_PRO,
			FEATURE_WORDADS_INSTANT,
			FEATURE_ADVANCED_SEO,
			FEATURE_ALL_FREE_FEATURES_JETPACK,
			WPCOM_FEATURES_SCAN,
			WPCOM_FEATURES_ANTISPAM,
			WPCOM_FEATURES_BACKUPS,
		] ),
} );

const getJetpackBusinessDetails = (): IncompleteJetpackPlan => ( {
	group: GROUP_JETPACK,
	type: TYPE_BUSINESS,
	getTitle: () => i18n.translate( 'Professional' ),
	availableFor: ( plan ) =>
		[
			PLAN_JETPACK_FREE,
			PLAN_JETPACK_PREMIUM,
			PLAN_JETPACK_PREMIUM_MONTHLY,
			PLAN_JETPACK_PERSONAL,
			PLAN_JETPACK_PERSONAL_MONTHLY,
		].includes( plan ),
	getDescription: () =>
		isEnabled( 'themes/premium' )
			? i18n.translate(
					'{{strong}}Best for organizations:{{/strong}} The most powerful WordPress sites.',
					plansDescriptionHeadingComponent
			  )
			: i18n.translate(
					'{{strong}}Best for organizations:{{/strong}} The most powerful WordPress sites: real-time backups ' +
						'and premium themes.',
					plansDescriptionHeadingComponent
			  ),
	getTagline: () => i18n.translate( 'You have the full suite of security and performance tools.' ),
	getPlanCardFeatures: () => [
		FEATURE_BACKUP_REALTIME_V2,
		FEATURE_PRODUCT_SCAN_REALTIME_V2,
		FEATURE_ANTISPAM_V2,
	],
	getIncludedFeatures: () =>
		compact( [
			// pay attention to ordering, shared features should align on /plan page
			FEATURE_OFFSITE_BACKUP_VAULTPRESS_REALTIME,
			FEATURE_BACKUP_ARCHIVE_UNLIMITED,
			FEATURE_BACKUP_STORAGE_SPACE_UNLIMITED,
			FEATURE_AUTOMATED_RESTORES,
			FEATURE_SPAM_AKISMET_PLUS,
			FEATURE_EASY_SITE_MIGRATION,
			FEATURE_PREMIUM_SUPPORT,
			FEATURE_REPUBLICIZE,
			FEATURE_SIMPLE_PAYMENTS,
			FEATURE_WORDADS_INSTANT,
			FEATURE_VIDEO_UPLOADS_JETPACK_PRO,
			FEATURE_MALWARE_SCANNING_DAILY_AND_ON_DEMAND,
			FEATURE_ONE_CLICK_THREAT_RESOLUTION,
			FEATURE_ADVANCED_SEO,
			FEATURE_GOOGLE_ANALYTICS,
			FEATURE_JETPACK_BACKUP_REALTIME,
			FEATURE_JETPACK_BACKUP_REALTIME_MONTHLY,
			FEATURE_JETPACK_SCAN_DAILY,
			FEATURE_JETPACK_SCAN_DAILY_MONTHLY,
			FEATURE_JETPACK_ANTI_SPAM_BI_YEARLY,
			FEATURE_JETPACK_ANTI_SPAM,
			FEATURE_JETPACK_ANTI_SPAM_MONTHLY,
			FEATURE_JETPACK_VIDEOPRESS_BI_YEARLY,
			FEATURE_JETPACK_VIDEOPRESS,
			FEATURE_JETPACK_VIDEOPRESS_MONTHLY,
			FEATURE_SEO_PREVIEW_TOOLS,
			FEATURE_OFFSITE_BACKUP_VAULTPRESS_REALTIME,
			isEnabled( 'themes/premium' ) ? WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED : null,
			FEATURE_ALL_PREMIUM_FEATURES_JETPACK,
			WPCOM_FEATURES_SCAN,
			WPCOM_FEATURES_ANTISPAM,
			WPCOM_FEATURES_BACKUPS,
			isEnabled( 'stats/paid-wpcom-v3' ) ? FEATURE_STATS_COMMERCIAL : FEATURE_STATS_PAID,
		] ),
	getInferiorFeatures: () => [ FEATURE_JETPACK_BACKUP_DAILY, FEATURE_JETPACK_BACKUP_DAILY_MONTHLY ],
} );

const getPlanJetpackSecurityDailyDetails = (): IncompleteJetpackPlan => ( {
	group: GROUP_JETPACK,
	type: TYPE_SECURITY_DAILY,
	getTitle: () => translate( 'Security {{em}}Daily{{/em}}', { components: { em: <em /> } } ),
	availableFor: ( plan ) => [ PLAN_JETPACK_FREE, ...JETPACK_LEGACY_PLANS ].includes( plan ),
	getDescription: () =>
		translate(
			'All of the essential Jetpack Security features in one package including VaultPress Backup, Scan, Akismet Anti-spam and more.'
		),
	getTagline: () => translate( 'Best for sites with occasional updates' ),
	getPlanCardFeatures: () => [
		FEATURE_PRODUCT_BACKUP_DAILY_V2,
		FEATURE_PRODUCT_SCAN_DAILY_V2,
		FEATURE_ANTISPAM_V2,
		FEATURE_WAF,
	],
	getIncludedFeatures: () => [
		FEATURE_JETPACK_BACKUP_DAILY,
		FEATURE_JETPACK_BACKUP_DAILY_MONTHLY,
		FEATURE_JETPACK_SCAN_DAILY,
		FEATURE_JETPACK_SCAN_DAILY_MONTHLY,
		FEATURE_JETPACK_ANTI_SPAM_BI_YEARLY,
		FEATURE_JETPACK_ANTI_SPAM,
		FEATURE_JETPACK_ANTI_SPAM_MONTHLY,
		FEATURE_BACKUP_ARCHIVE_30,
		FEATURE_REPUBLICIZE,
		FEATURE_ADVANCED_SEO,
		FEATURE_SEO_PREVIEW_TOOLS,
		FEATURE_SIMPLE_PAYMENTS,
		FEATURE_WORDADS_INSTANT,
		FEATURE_GOOGLE_ANALYTICS,
		FEATURE_PREMIUM_SUPPORT,
		WPCOM_FEATURES_SCAN,
		WPCOM_FEATURES_ANTISPAM,
		WPCOM_FEATURES_BACKUPS,
	],
} );

const getPlanJetpackSecurityRealtimeDetails = (): IncompleteJetpackPlan => ( {
	group: GROUP_JETPACK,
	type: TYPE_SECURITY_REALTIME,
	getTitle: () =>
		translate( 'Security {{em}}Real-time{{/em}}', {
			components: { em: <em style={ { whiteSpace: 'nowrap' } } /> },
		} ),
	availableFor: ( plan ) =>
		[
			PLAN_JETPACK_FREE,
			PLAN_JETPACK_SECURITY_DAILY,
			PLAN_JETPACK_SECURITY_DAILY_MONTHLY,
			...JETPACK_LEGACY_PLANS,
		].includes( plan ),
	getDescription: () =>
		translate(
			'Get next-level protection with real-time backups, real-time scan and all essential security tools.'
		),
	getTagline: () => translate( 'Best for sites with frequent updates' ),
	getPlanCardFeatures: () => [
		FEATURE_PLAN_SECURITY_DAILY,
		FEATURE_PRODUCT_BACKUP_REALTIME_V2,
		FEATURE_PRODUCT_SCAN_REALTIME_V2,
		FEATURE_ACTIVITY_LOG_1_YEAR_V2,
	],
	getIncludedFeatures: () => [
		FEATURE_JETPACK_BACKUP_REALTIME,
		FEATURE_JETPACK_BACKUP_REALTIME_MONTHLY,
		FEATURE_JETPACK_SCAN_DAILY,
		FEATURE_JETPACK_SCAN_DAILY_MONTHLY,
		FEATURE_JETPACK_ANTI_SPAM_BI_YEARLY,
		FEATURE_JETPACK_ANTI_SPAM,
		FEATURE_JETPACK_ANTI_SPAM_MONTHLY,
		FEATURE_BACKUP_ARCHIVE_UNLIMITED,
		FEATURE_VIDEO_UPLOADS_JETPACK_PRO,
		FEATURE_REPUBLICIZE,
		FEATURE_ADVANCED_SEO,
		FEATURE_SEO_PREVIEW_TOOLS,
		FEATURE_SIMPLE_PAYMENTS,
		FEATURE_WORDADS_INSTANT,
		FEATURE_GOOGLE_ANALYTICS,
		FEATURE_PREMIUM_SUPPORT,
		WPCOM_FEATURES_SCAN,
		WPCOM_FEATURES_ANTISPAM,
		WPCOM_FEATURES_BACKUPS,
	],
	getInferiorFeatures: () => [
		FEATURE_JETPACK_BACKUP_DAILY,
		FEATURE_JETPACK_BACKUP_DAILY_MONTHLY,
		FEATURE_BACKUP_ARCHIVE_30,
	],
} );

const getPlanJetpackSecurityT1Details = (): IncompleteJetpackPlan => ( {
	...getJetpackCommonPlanDetails(),
	group: GROUP_JETPACK,
	type: TYPE_SECURITY_T1,
	getTitle: () => translate( 'Security', { context: 'Jetpack product name' } ),
	availableFor: ( plan ) => [ PLAN_JETPACK_FREE, ...JETPACK_LEGACY_PLANS ].includes( plan ),
	getDescription: () =>
		translate(
			'Easy-to-use, comprehensive WordPress site security including backups, malware scanning, and spam protection.'
		),
	getFeaturedDescription: () =>
		translate(
			'Comprehensive site security made simple.{{br}}{{/br}}{{br}}{{/br}}This bundle includes:{{ul}}{{li}}VaultPress Backup (10GB){{/li}}{{li}}Scan{{/li}}{{li}}Akismet Anti-spam (10k API calls/mo){{/li}}{{/ul}}',
			{
				components: {
					br: <br />,
					ul: <ul />,
					li: <li />,
				},
				comment:
					'{{ul}}{{ul/}} represents an unordered list, and {{li}}{/li} represents a list item',
			}
		),
	getLightboxDescription: () =>
		translate(
			'Easy-to-use, comprehensive WordPress site security including backups, malware scanning, and spam protection.{{br/}}Includes VaultPress Backup, Jetpack Scan, and Akismet Anti-spam.',
			{
				components: {
					br: <br />,
				},
				comment: '{{br/}} represents a line break',
			}
		),
	getPlanCardFeatures: () => [
		FEATURE_JETPACK_PRODUCT_BACKUP,
		FEATURE_JETPACK_REAL_TIME_MALWARE_SCANNING,
		FEATURE_ANTISPAM_V2,
		FEATURE_WAF,
		FEATURE_JETPACK_30_DAY_ARCHIVE_ACTIVITY_LOG,
	],
	getIncludedFeatures: () => [
		FEATURE_JETPACK_BACKUP_T1_BI_YEARLY,
		FEATURE_JETPACK_BACKUP_T1_YEARLY,
		FEATURE_JETPACK_BACKUP_T1_MONTHLY,
		FEATURE_JETPACK_SCAN_DAILY,
		FEATURE_JETPACK_SCAN_DAILY_MONTHLY,
		FEATURE_JETPACK_ANTI_SPAM_BI_YEARLY,
		FEATURE_JETPACK_ANTI_SPAM,
		FEATURE_JETPACK_ANTI_SPAM_MONTHLY,
		FEATURE_BACKUP_ARCHIVE_UNLIMITED,
		FEATURE_VIDEO_UPLOADS_JETPACK_PRO,
		FEATURE_REPUBLICIZE,
		FEATURE_ADVANCED_SEO,
		FEATURE_SEO_PREVIEW_TOOLS,
		FEATURE_SIMPLE_PAYMENTS,
		FEATURE_WORDADS_INSTANT,
		FEATURE_GOOGLE_ANALYTICS,
		FEATURE_PREMIUM_SUPPORT,
		WPCOM_FEATURES_SCAN,
		WPCOM_FEATURES_ANTISPAM,
		WPCOM_FEATURES_BACKUPS,
	],
	getBenefits: () => [
		translate( 'Protect your revenue stream and content' ),
		translate( 'Learn about issues before your customers are impacted' ),
		translate( 'Restore your site in one click from desktop or mobile' ),
		translate( 'Fix your site without a developer' ),
		translate( 'Protect Woo order and customer data' ),
		translate( 'Save time manually reviewing spam' ),
		translate( 'Best-in-class support from WordPress experts' ),
	],
	getInferiorFeatures: () => [ FEATURE_JETPACK_BACKUP_DAILY, FEATURE_JETPACK_BACKUP_DAILY_MONTHLY ],
} );

const getPlanJetpackSecurityT2Details = (): IncompleteJetpackPlan => ( {
	...getPlanJetpackSecurityT1Details(),
	type: TYPE_SECURITY_T2,
	getPlanCardFeatures: () => [
		FEATURE_PLAN_SECURITY_DAILY,
		FEATURE_PRODUCT_BACKUP_REALTIME_V2,
		FEATURE_PRODUCT_SCAN_REALTIME_V2,
		FEATURE_WAF,
		FEATURE_JETPACK_1_YEAR_ARCHIVE_ACTIVITY_LOG,
	],
	getIncludedFeatures: () => [
		FEATURE_JETPACK_BACKUP_T2_YEARLY,
		FEATURE_JETPACK_BACKUP_T2_MONTHLY,
		FEATURE_JETPACK_SCAN_DAILY,
		FEATURE_JETPACK_SCAN_DAILY_MONTHLY,
		FEATURE_JETPACK_ANTI_SPAM_BI_YEARLY,
		FEATURE_JETPACK_ANTI_SPAM,
		FEATURE_JETPACK_ANTI_SPAM_MONTHLY,
		FEATURE_BACKUP_ARCHIVE_UNLIMITED,
		FEATURE_VIDEO_UPLOADS_JETPACK_PRO,
		FEATURE_REPUBLICIZE,
		FEATURE_ADVANCED_SEO,
		FEATURE_SEO_PREVIEW_TOOLS,
		FEATURE_SIMPLE_PAYMENTS,
		FEATURE_WORDADS_INSTANT,
		FEATURE_GOOGLE_ANALYTICS,
		FEATURE_PREMIUM_SUPPORT,
		WPCOM_FEATURES_SCAN,
		WPCOM_FEATURES_ANTISPAM,
		WPCOM_FEATURES_BACKUPS,
	],
	getInferiorFeatures: () => [
		FEATURE_JETPACK_BACKUP_DAILY,
		FEATURE_JETPACK_BACKUP_DAILY_MONTHLY,
		FEATURE_BACKUP_ARCHIVE_30,
	],
} );

const getPlanJetpackCompleteDetails = (): IncompleteJetpackPlan => ( {
	...getJetpackCommonPlanDetails(),
	group: GROUP_JETPACK,
	type: TYPE_ALL,
	getTitle: () =>
		translate( 'Complete', {
			context: 'Jetpack plan name',
		} ),
	availableFor: ( plan ) =>
		[ PLAN_JETPACK_FREE, ...JETPACK_SECURITY_PLANS, ...JETPACK_LEGACY_PLANS ].includes( plan ),
	getDescription: () =>
		translate(
			'Get the full power of Jetpack with all Security, Performance, Growth, and Design tools.'
		),
	getFeaturedDescription: () =>
		translate(
			'Get the full Jetpack suite with real-time security tools, improved site performance, and tools to grow your business.'
		),
	getLightboxDescription: () =>
		translate(
			'Full Jetpack suite with real-time security, instant site search, ad-free video, all CRM extensions, and extra storage for backups and video.'
		),
	getTagline: () => translate( 'For best-in-class WordPress sites' ),
	getPlanCardFeatures: () => [
		FEATURE_JETPACK_ALL_BACKUP_SECURITY_FEATURES,
		FEATURE_JETPACK_1TB_BACKUP_STORAGE,
		FEATURE_JETPACK_PRODUCT_VIDEOPRESS,
		FEATURE_PRODUCT_SEARCH_V2,
		FEATURE_CRM_V2,
		FEATURE_JETPACK_1_YEAR_ARCHIVE_ACTIVITY_LOG,
	],
	getIncludedFeatures: () =>
		compact( [
			FEATURE_JETPACK_BACKUP_T2_YEARLY,
			FEATURE_JETPACK_BACKUP_T2_MONTHLY,
			FEATURE_JETPACK_SCAN_DAILY,
			FEATURE_JETPACK_SCAN_DAILY_MONTHLY,
			FEATURE_JETPACK_ANTI_SPAM_BI_YEARLY,
			FEATURE_JETPACK_ANTI_SPAM,
			FEATURE_JETPACK_ANTI_SPAM_MONTHLY,
			FEATURE_JETPACK_SEARCH_BI_YEARLY,
			FEATURE_JETPACK_SEARCH,
			FEATURE_JETPACK_SEARCH_MONTHLY,
			FEATURE_JETPACK_CRM,
			FEATURE_JETPACK_CRM_MONTHLY,
			FEATURE_BACKUP_ARCHIVE_UNLIMITED,
			FEATURE_VIDEO_UPLOADS_JETPACK_PRO,
			FEATURE_JETPACK_BOOST_BI_YEARLY,
			FEATURE_JETPACK_BOOST,
			FEATURE_JETPACK_BOOST_MONTHLY,
			FEATURE_JETPACK_SOCIAL_ADVANCED_BI_YEARLY,
			FEATURE_JETPACK_SOCIAL_ADVANCED,
			FEATURE_JETPACK_SOCIAL_ADVANCED_MONTHLY,
			FEATURE_JETPACK_VIDEOPRESS_BI_YEARLY,
			FEATURE_JETPACK_VIDEOPRESS,
			FEATURE_JETPACK_VIDEOPRESS_MONTHLY,
			FEATURE_CLOUD_CRITICAL_CSS,
			FEATURE_REPUBLICIZE,
			FEATURE_ADVANCED_SEO,
			FEATURE_SEO_PREVIEW_TOOLS,
			FEATURE_SIMPLE_PAYMENTS,
			FEATURE_WORDADS_INSTANT,
			FEATURE_GOOGLE_ANALYTICS,
			isEnabled( 'themes/premium' ) ? WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED : null,
			FEATURE_PREMIUM_SUPPORT,
			WPCOM_FEATURES_SCAN,
			WPCOM_FEATURES_ANTISPAM,
			WPCOM_FEATURES_BACKUPS,
			isEnabled( 'stats/paid-wpcom-v3' ) ? FEATURE_STATS_COMMERCIAL : FEATURE_STATS_PAID,
		] ),
	getInferiorFeatures: () => [
		FEATURE_JETPACK_BACKUP_DAILY,
		FEATURE_JETPACK_BACKUP_DAILY_MONTHLY,
		FEATURE_BACKUP_ARCHIVE_30,
		FEATURE_JETPACK_SOCIAL_BASIC_BI_YEARLY,
		FEATURE_JETPACK_SOCIAL_BASIC,
		FEATURE_JETPACK_SOCIAL_BASIC_MONTHLY,
	],
	getBenefits: () => [
		translate( 'Protect your revenue stream and content' ),
		translate( 'Learn about issues before your customers are impacted' ),
		translate( 'Restore your site in one click from desktop or mobile' ),
		translate( 'Fix your site without a developer' ),
		translate( 'Protect Woo order and customer data' ),
		translate( 'Save time manually reviewing spam' ),
		translate( 'Grow your business with video, social, and CRM tools' ),
		translate( 'Best-in-class support from WordPress experts' ),
	],
} );

const getPlanJetpackStarterDetails = (): IncompleteJetpackPlan => ( {
	...getJetpackCommonPlanDetails(),
	group: GROUP_JETPACK,
	type: TYPE_JETPACK_STARTER,
	getTitle: () => translate( 'Starter', { context: 'Jetpack product name' } ),
	availableFor: ( plan ) => [ PLAN_JETPACK_FREE, ...JETPACK_LEGACY_PLANS ].includes( plan ),
	getTagline: () =>
		translate( 'Essential security tools: real-time backups and comment spam protection.' ),
	getDescription: () =>
		translate( 'Essential security tools: real-time backups and comment spam protection.' ),
	getFeaturedDescription: () =>
		translate(
			'This bundle includes:{{ul}}{{li}}VaultPress Backup (1GB){{/li}}{{li}}Akismet Anti-spam (1k API calls/mo){{/li}}{{/ul}}',
			{
				components: {
					ul: <ul />,
					li: <li />,
				},
				comment:
					'{{ul}}{{ul/}} represents an unordered list, and {{li}}{/li} represents a list item',
			}
		),
	getRecommendedFor: () => [
		{ tag: JETPACK_TAG_FOR_SMALL_SITES, label: translate( 'Small sites' ) },
		{ tag: JETPACK_TAG_FOR_BLOGS, label: translate( 'Blogs' ) },
	],
	getLightboxDescription: () =>
		translate( 'Essential security tools: real-time backups and comment spam protection.' ),
	getPlanCardFeatures: () => [ FEATURE_JETPACK_PRODUCT_BACKUP, FEATURE_ANTISPAM_V2 ],
	getIncludedFeatures: () => [
		FEATURE_JETPACK_BACKUP_T0_YEARLY,
		FEATURE_JETPACK_BACKUP_T0_MONTHLY,
		FEATURE_JETPACK_ANTI_SPAM_BI_YEARLY,
		FEATURE_JETPACK_ANTI_SPAM,
		FEATURE_JETPACK_ANTI_SPAM_MONTHLY,
		FEATURE_BACKUP_ARCHIVE_UNLIMITED,
		FEATURE_VIDEO_UPLOADS_JETPACK_PRO,
		FEATURE_REPUBLICIZE,
		FEATURE_ADVANCED_SEO,
		FEATURE_SEO_PREVIEW_TOOLS,
		FEATURE_SIMPLE_PAYMENTS,
		FEATURE_WORDADS_INSTANT,
		FEATURE_GOOGLE_ANALYTICS,
		FEATURE_PREMIUM_SUPPORT,
		WPCOM_FEATURES_ANTISPAM,
		WPCOM_FEATURES_BACKUPS,
	],
	getBenefits: () => [
		translate( 'Protect your revenue stream and content' ),
		translate( 'Restore your site in one click from desktop or mobile' ),
		translate( 'Fix your site without a developer' ),
		translate( 'Protect Woo order and customer data' ),
		translate( 'Save time manually reviewing spam' ),
		translate( 'Best-in-class support from WordPress experts' ),
	],
	getInferiorFeatures: () => [ FEATURE_JETPACK_BACKUP_DAILY, FEATURE_JETPACK_BACKUP_DAILY_MONTHLY ],
} );

const getPlanJetpackGrowthDetails = (): IncompleteJetpackPlan => ( {
	...getJetpackCommonPlanDetails(),
	group: GROUP_JETPACK,
	type: TYPE_JETPACK_GROWTH,
	getTitle: () => translate( 'Growth', { context: 'Jetpack product name' } ),
	getDescription: () => translate( 'Grow your audience effortlessly.' ),
	availableFor: ( plan ) => [ PLAN_JETPACK_FREE, ...JETPACK_LEGACY_PLANS ].includes( plan ),
	getRecommendedFor: () => [
		{ tag: JETPACK_TAG_FOR_WOOCOMMERCE_STORES, label: translate( 'WooCommerce Stores' ) },
		{ tag: JETPACK_TAG_FOR_MEMBERSHIP_SITES, label: translate( 'Membership sites' ) },
		{ tag: JETPACK_TAG_FOR_BLOGGERS, label: translate( 'Bloggers' ) },
	],
	getFeaturedDescription: () =>
		translate(
			'Grow your audience effortlessly.{{br}}{{/br}}{{br}}{{/br}}This bundle includes:{{ul}}{{li}}Stats (10k site views - upgradeable){{/li}}{{li}}Social{{/li}}{{li}}Newsletter and monetization tools{{/li}}{{/ul}}',
			{
				components: {
					br: <br />,
					ul: <ul />,
					li: <li />,
				},
				comment:
					'{{ul}}{{ul/}} represents an unordered list, and {{li}}{/li} represents a list item',
			}
		),
	getTagline: () =>
		translate(
			'Essential tools to help you grow your audience, track visitor engagement, and turn leads into loyal customers and advocates.'
		),
	getLightboxDescription: () =>
		translate(
			'Essential tools to help you grow your audience, track visitor engagement, and turn leads into loyal customers and advocates.'
		),
	getPlanCardFeatures: () => [
		FEATURE_JETPACK_SOCIAL_V1_MONTHLY,
		isEnabled( 'stats/paid-wpcom-v3' ) ? FEATURE_STATS_COMMERCIAL : FEATURE_STATS_PAID,
	],
	getIncludedFeatures: () => [
		FEATURE_EARN_AD,
		FEATURE_UNLIMITED_SUBSCRIBERS,
		FEATURE_SIMPLE_PAYMENTS,
	],
	getWhatIsIncluded: () => [
		translate( '40+ Jetpack blocks' ),
		translate( 'Display ads with WordAds' ),
		translate( 'Pay with PayPal' ),
		translate( 'Paid content gating' ),
		translate( 'Paywall access' ),
		translate( 'Newsletter' ),
		translate( 'Priority support' ),
	],
	getBenefits: () => [
		translate( 'Quickly create content that stands out' ),
		translate( 'Grow your subscribers with simple subscribe forms' ),
		translate( 'Create content for paid subscribers' ),
		translate( 'Sell access to premium content' ),
		translate( 'Easily accept tips and donations' ),
	],
} );

const getPlanJetpackGoldenTokenDetails = (): IncompleteJetpackPlan => ( {
	group: GROUP_JETPACK,
	type: TYPE_GOLDEN_TOKEN,
	getTitle: () =>
		translate( 'Golden Token', {
			context: 'The name of a Jetpack plan awarded to amazing WordPress sites',
		} ),
	availableFor: ( plan ) => [ PLAN_JETPACK_FREE, ...JETPACK_LEGACY_PLANS ].includes( plan ),
	getDescription: () =>
		translate( 'The Golden Token provides a lifetime license for Backup and Scan.' ),
	getTagline: () => translate( 'A lifetime of Jetpack powers for your website' ),
	getPlanCardFeatures: () => [
		FEATURE_PRODUCT_BACKUP_REALTIME_V2,
		FEATURE_PRODUCT_SCAN_REALTIME_V2,
		FEATURE_ACTIVITY_LOG_1_YEAR_V2,
	],
	getIncludedFeatures: () => [
		FEATURE_JETPACK_BACKUP_REALTIME,
		FEATURE_JETPACK_BACKUP_REALTIME_MONTHLY,
		FEATURE_JETPACK_SCAN_DAILY,
		FEATURE_JETPACK_SCAN_DAILY_MONTHLY,
		FEATURE_BACKUP_ARCHIVE_UNLIMITED,
		FEATURE_VIDEO_UPLOADS_JETPACK_PRO,
		FEATURE_REPUBLICIZE,
		FEATURE_ADVANCED_SEO,
		FEATURE_SEO_PREVIEW_TOOLS,
		FEATURE_SIMPLE_PAYMENTS,
		FEATURE_WORDADS_INSTANT,
		FEATURE_GOOGLE_ANALYTICS,
		FEATURE_PREMIUM_SUPPORT,
		WPCOM_FEATURES_SCAN,
		WPCOM_FEATURES_BACKUPS,
	],
	getInferiorFeatures: () => [
		FEATURE_JETPACK_BACKUP_DAILY,
		FEATURE_JETPACK_BACKUP_DAILY_MONTHLY,
		FEATURE_BACKUP_ARCHIVE_30,
	],
} );

// DO NOT import. Use `getPlan` instead.
export const PLANS_LIST: Record< string, Plan | JetpackPlan | WPComPlan > = {
	[ PLAN_FREE ]: {
		...getPlanFreeDetails(),
		term: TERM_ANNUALLY,
		getBillingTimeFrame: () => i18n.translate( 'No expiration date' ),
		getProductId: () => 1,
		getStoreSlug: () => PLAN_FREE,
		getPathSlug: () => 'beginner',
	},

	[ PLAN_BLOGGER ]: {
		...getPlanBloggerDetails(),
		term: TERM_ANNUALLY,
		getBillingTimeFrame: WPComGetBillingTimeframe,
		availableFor: ( plan ) => [ PLAN_FREE ].includes( plan ),
		getProductId: () => 1010,
		getStoreSlug: () => PLAN_BLOGGER,
		getPathSlug: () => 'blogger',
	},

	[ PLAN_BLOGGER_2_YEARS ]: {
		...getPlanBloggerDetails(),
		term: TERM_BIENNIALLY,
		getBillingTimeFrame: WPComGetBiennialBillingTimeframe,
		availableFor: ( plan ) => [ PLAN_FREE, PLAN_BLOGGER ].includes( plan ),
		getProductId: () => 1030,
		getStoreSlug: () => PLAN_BLOGGER_2_YEARS,
		getPathSlug: () => 'blogger-2-years',
	},

	[ PLAN_PERSONAL_MONTHLY ]: {
		...getPlanPersonalDetails(),
		...getMonthlyTimeframe(),
		availableFor: ( plan ) => [ PLAN_FREE, PLAN_BLOGGER, PLAN_BLOGGER_2_YEARS ].includes( plan ),
		getProductId: () => 1019,
		getStoreSlug: () => PLAN_PERSONAL_MONTHLY,
		getPathSlug: () => 'personal-monthly',
	},

	[ PLAN_PERSONAL ]: {
		...getPlanPersonalDetails(),
		term: TERM_ANNUALLY,
		getBillingTimeFrame: WPComGetBillingTimeframe,
		availableFor: ( plan ) =>
			[ PLAN_FREE, PLAN_BLOGGER, PLAN_BLOGGER_2_YEARS, PLAN_PERSONAL_MONTHLY ].includes( plan ),
		getProductId: () => 1009,
		getStoreSlug: () => PLAN_PERSONAL,
		getPathSlug: () => 'personal',
	},

	[ PLAN_PERSONAL_2_YEARS ]: {
		...getPlanPersonalDetails(),
		term: TERM_BIENNIALLY,
		getBillingTimeFrame: WPComGetBiennialBillingTimeframe,
		availableFor: ( plan ) =>
			[
				PLAN_FREE,
				PLAN_BLOGGER,
				PLAN_BLOGGER_2_YEARS,
				PLAN_PERSONAL_MONTHLY,
				PLAN_PERSONAL,
			].includes( plan ),
		getProductId: () => 1029,
		getStoreSlug: () => PLAN_PERSONAL_2_YEARS,
		getPathSlug: () => 'personal-2-years',
	},

	[ PLAN_PERSONAL_3_YEARS ]: {
		...getPlanPersonalDetails(),
		term: TERM_TRIENNIALLY,
		getBillingTimeFrame: WPComGetTriennialBillingTimeframe,
		availableFor: ( plan ) =>
			[
				PLAN_FREE,
				PLAN_BLOGGER,
				PLAN_BLOGGER_2_YEARS,
				PLAN_PERSONAL_MONTHLY,
				PLAN_PERSONAL,
				PLAN_PERSONAL_2_YEARS,
			].includes( plan ),
		getProductId: () => 1049,
		getStoreSlug: () => PLAN_PERSONAL_3_YEARS,
		getPathSlug: () => 'personal-3-years',
	},

	[ PLAN_PREMIUM_MONTHLY ]: {
		...getPlanPremiumDetails(),
		...getMonthlyTimeframe(),
		availableFor: ( plan ) =>
			[
				PLAN_FREE,
				PLAN_BLOGGER,
				PLAN_BLOGGER_2_YEARS,
				PLAN_PERSONAL_MONTHLY,
				PLAN_PERSONAL,
				PLAN_PERSONAL_2_YEARS,
			].includes( plan ),
		getProductId: () => 1013,
		getStoreSlug: () => PLAN_PREMIUM_MONTHLY,
		getPathSlug: () => 'premium-monthly',
	},

	[ PLAN_PREMIUM ]: {
		...getPlanPremiumDetails(),
		term: TERM_ANNUALLY,
		getBillingTimeFrame: WPComGetBillingTimeframe,
		availableFor: ( plan ) =>
			[
				PLAN_FREE,
				PLAN_BLOGGER,
				PLAN_BLOGGER_2_YEARS,
				PLAN_PERSONAL_MONTHLY,
				PLAN_PERSONAL,
				PLAN_PERSONAL_2_YEARS,
				PLAN_PREMIUM_MONTHLY,
				PLAN_WPCOM_STARTER,
			].includes( plan ),
		getProductId: () => 1003,
		getStoreSlug: () => PLAN_PREMIUM,
		getPathSlug: () => 'premium',
	},

	[ PLAN_PREMIUM_2_YEARS ]: {
		...getPlanPremiumDetails(),
		term: TERM_BIENNIALLY,
		getBillingTimeFrame: WPComGetBiennialBillingTimeframe,
		availableFor: ( plan ) =>
			[
				PLAN_FREE,
				PLAN_BLOGGER,
				PLAN_BLOGGER_2_YEARS,
				PLAN_PERSONAL_MONTHLY,
				PLAN_PERSONAL,
				PLAN_PERSONAL_2_YEARS,
				PLAN_PREMIUM_MONTHLY,
				PLAN_PREMIUM,
			].includes( plan ),
		getProductId: () => 1023,
		getStoreSlug: () => PLAN_PREMIUM_2_YEARS,
		getPathSlug: () => 'premium-2-years',
	},

	[ PLAN_PREMIUM_3_YEARS ]: {
		...getPlanPremiumDetails(),
		term: TERM_TRIENNIALLY,
		getBillingTimeFrame: WPComGetTriennialBillingTimeframe,
		availableFor: ( plan ) =>
			[
				PLAN_FREE,
				PLAN_BLOGGER,
				PLAN_BLOGGER_2_YEARS,
				PLAN_PERSONAL_MONTHLY,
				PLAN_PERSONAL,
				PLAN_PERSONAL_2_YEARS,
				PLAN_PERSONAL_3_YEARS,
				PLAN_PREMIUM_MONTHLY,
				PLAN_PREMIUM,
				PLAN_PREMIUM_2_YEARS,
			].includes( plan ),
		getProductId: () => 1043,
		getStoreSlug: () => PLAN_PREMIUM_3_YEARS,
		getPathSlug: () => 'premium-3-years',
	},

	[ PLAN_BUSINESS_MONTHLY ]: {
		...getPlanBusinessDetails(),
		...getMonthlyTimeframe(),
		availableFor: ( plan ) =>
			isEnabled( 'upgrades/wpcom-monthly-plans' ) &&
			[
				PLAN_FREE,
				PLAN_BLOGGER,
				PLAN_BLOGGER_2_YEARS,
				PLAN_PERSONAL_MONTHLY,
				PLAN_PERSONAL,
				PLAN_PERSONAL_2_YEARS,
				PLAN_PREMIUM_MONTHLY,
				PLAN_PREMIUM,
				PLAN_PREMIUM_2_YEARS,
				PLAN_WPCOM_PRO_MONTHLY,
				PLAN_MIGRATION_TRIAL_MONTHLY,
				PLAN_HOSTING_TRIAL_MONTHLY,
			].includes( plan ),
		getProductId: () => 1018,
		getStoreSlug: () => PLAN_BUSINESS_MONTHLY,
		getPathSlug: () => 'business-monthly',
	},

	[ PLAN_BUSINESS ]: {
		...getPlanBusinessDetails(),
		term: TERM_ANNUALLY,
		getBillingTimeFrame: WPComGetBillingTimeframe,
		availableFor: ( plan ) =>
			[
				PLAN_FREE,
				PLAN_WPCOM_STARTER,
				PLAN_WPCOM_PRO,
				PLAN_BLOGGER,
				PLAN_BLOGGER_2_YEARS,
				PLAN_PERSONAL_MONTHLY,
				PLAN_PERSONAL,
				PLAN_PERSONAL_2_YEARS,
				PLAN_PREMIUM_MONTHLY,
				PLAN_PREMIUM,
				PLAN_PREMIUM_2_YEARS,
				PLAN_BUSINESS_MONTHLY,
				PLAN_WPCOM_PRO_MONTHLY,
				PLAN_MIGRATION_TRIAL_MONTHLY,
				PLAN_HOSTING_TRIAL_MONTHLY,
			].includes( plan ),
		getProductId: () => 1008,
		getStoreSlug: () => PLAN_BUSINESS,
		getPathSlug: () => 'business',
	},

	[ PLAN_BUSINESS_2_YEARS ]: {
		...getPlanBusinessDetails(),
		term: TERM_BIENNIALLY,
		getBillingTimeFrame: WPComGetBiennialBillingTimeframe,
		availableFor: ( plan ) =>
			[
				PLAN_FREE,
				PLAN_WPCOM_STARTER,
				PLAN_BLOGGER,
				PLAN_BLOGGER_2_YEARS,
				PLAN_PERSONAL_MONTHLY,
				PLAN_PERSONAL,
				PLAN_PERSONAL_2_YEARS,
				PLAN_PREMIUM_MONTHLY,
				PLAN_PREMIUM,
				PLAN_PREMIUM_2_YEARS,
				PLAN_BUSINESS,
				PLAN_BUSINESS_MONTHLY,
				PLAN_WPCOM_PRO_MONTHLY,
				PLAN_WPCOM_PRO,
				PLAN_WPCOM_PRO_2_YEARS,
				PLAN_MIGRATION_TRIAL_MONTHLY,
				PLAN_HOSTING_TRIAL_MONTHLY,
			].includes( plan ),
		getProductId: () => 1028,
		getStoreSlug: () => PLAN_BUSINESS_2_YEARS,
		getPathSlug: () => 'business-2-years',
	},

	[ PLAN_BUSINESS_3_YEARS ]: {
		...getPlanBusinessDetails(),
		term: TERM_TRIENNIALLY,
		getBillingTimeFrame: WPComGetTriennialBillingTimeframe,
		availableFor: ( plan ) =>
			[
				PLAN_FREE,
				PLAN_WPCOM_STARTER,
				PLAN_BLOGGER,
				PLAN_BLOGGER_2_YEARS,
				PLAN_PERSONAL_MONTHLY,
				PLAN_PERSONAL,
				PLAN_PERSONAL_2_YEARS,
				PLAN_PERSONAL_3_YEARS,
				PLAN_PREMIUM_MONTHLY,
				PLAN_PREMIUM,
				PLAN_PREMIUM_2_YEARS,
				PLAN_PREMIUM_3_YEARS,
				PLAN_BUSINESS,
				PLAN_BUSINESS_MONTHLY,
				PLAN_BUSINESS_2_YEARS,
				PLAN_WPCOM_PRO_MONTHLY,
				PLAN_WPCOM_PRO,
				PLAN_WPCOM_PRO_2_YEARS,
				PLAN_MIGRATION_TRIAL_MONTHLY,
				PLAN_HOSTING_TRIAL_MONTHLY,
			].includes( plan ),
		getProductId: () => 1048,
		getStoreSlug: () => PLAN_BUSINESS_3_YEARS,
		getPathSlug: () => 'business-3-years',
	},

	[ PLAN_100_YEARS ]: {
		...getPlanBusinessDetails(),
		term: TERM_CENTENNIALLY,
		group: GROUP_WPCOM,
		type: TYPE_100_YEAR,
		// Todo: ¯\_(ツ)_/¯ on the copy.
		getTitle: () => i18n.translate( '100-Year Plan' ),
		getAudience: () => i18n.translate( 'Best for long-term thinkers' ),
		getBlogAudience: () => i18n.translate( 'Best for long-term thinkers' ),
		getPortfolioAudience: () => i18n.translate( 'Best for long-term thinkers' ),
		getStoreAudience: () => i18n.translate( 'Best for long-term thinkers' ),
		getPlanTagline: () => i18n.translate( 'A plan to leave a lasting mark on the web.' ),
		getDescription: () => i18n.translate( 'A plan to leave a lasting mark on the web.' ),
		getShortDescription: () => i18n.translate( 'A plan to leave a lasting mark on the web.' ),
		getTagline: () => i18n.translate( 'A plan to leave a lasting mark on the web.' ),
		getBlogOnboardingTagLine: () => i18n.translate( 'A plan to leave a lasting mark on the web.' ),
		getBillingTimeFrame: WPComGetBillingTimeframe,
		availableFor: ( plan ) =>
			[
				PLAN_FREE,
				PLAN_WPCOM_STARTER,
				PLAN_WPCOM_PRO,
				PLAN_BLOGGER,
				PLAN_BLOGGER_2_YEARS,
				PLAN_PERSONAL_MONTHLY,
				PLAN_PERSONAL,
				PLAN_PERSONAL_2_YEARS,
				PLAN_PREMIUM_MONTHLY,
				PLAN_PREMIUM,
				PLAN_PREMIUM_2_YEARS,
				PLAN_BUSINESS_MONTHLY,
				PLAN_WPCOM_PRO_MONTHLY,
				PLAN_MIGRATION_TRIAL_MONTHLY,
				PLAN_HOSTING_TRIAL_MONTHLY,
			].includes( plan ),
		getProductId: () => 1061,
		getStoreSlug: () => PLAN_100_YEARS,
		getPathSlug: () => 'wp_bundle_hundred_year',
	},

	[ PLAN_ECOMMERCE_MONTHLY ]: {
		...getPlanEcommerceDetails(),
		...getMonthlyTimeframe(),
		availableFor: ( plan ) =>
			[
				PLAN_FREE,
				PLAN_BLOGGER,
				PLAN_BLOGGER_2_YEARS,
				PLAN_PERSONAL_MONTHLY,
				PLAN_PERSONAL,
				PLAN_PERSONAL_2_YEARS,
				PLAN_PREMIUM_MONTHLY,
				PLAN_PREMIUM,
				PLAN_PREMIUM_2_YEARS,
				PLAN_BUSINESS_MONTHLY,
				PLAN_BUSINESS,
				PLAN_BUSINESS_2_YEARS,
				PLAN_WPCOM_PRO_MONTHLY,
				PLAN_ECOMMERCE_TRIAL_MONTHLY,
				PLAN_MIGRATION_TRIAL_MONTHLY,
				PLAN_HOSTING_TRIAL_MONTHLY,
			].includes( plan ),
		getProductId: () => 1021,
		getStoreSlug: () => PLAN_ECOMMERCE_MONTHLY,
		getPathSlug: () => 'ecommerce-monthly',
	},

	[ PLAN_ECOMMERCE ]: {
		...getPlanEcommerceDetails(),
		term: TERM_ANNUALLY,
		getBillingTimeFrame: WPComGetBillingTimeframe,
		availableFor: ( plan ) =>
			[
				PLAN_FREE,
				PLAN_WPCOM_STARTER,
				PLAN_WPCOM_PRO,
				PLAN_BLOGGER,
				PLAN_BLOGGER_2_YEARS,
				PLAN_PERSONAL_MONTHLY,
				PLAN_PERSONAL,
				PLAN_PERSONAL_2_YEARS,
				PLAN_PREMIUM_MONTHLY,
				PLAN_PREMIUM,
				PLAN_PREMIUM_2_YEARS,
				PLAN_BUSINESS_MONTHLY,
				PLAN_BUSINESS,
				PLAN_BUSINESS_2_YEARS,
				PLAN_ECOMMERCE_MONTHLY,
				PLAN_WPCOM_PRO_MONTHLY,
				PLAN_ECOMMERCE_TRIAL_MONTHLY,
				PLAN_MIGRATION_TRIAL_MONTHLY,
				PLAN_HOSTING_TRIAL_MONTHLY,
			].includes( plan ),
		getProductId: () => 1011,
		getStoreSlug: () => PLAN_ECOMMERCE,
		getPathSlug: () => 'ecommerce',
	},

	[ PLAN_ECOMMERCE_2_YEARS ]: {
		...getPlanEcommerceDetails(),
		term: TERM_BIENNIALLY,
		getBillingTimeFrame: WPComGetBiennialBillingTimeframe,
		availableFor: ( plan ) =>
			[
				PLAN_FREE,
				PLAN_WPCOM_STARTER,
				PLAN_WPCOM_PRO_MONTHLY,
				PLAN_WPCOM_PRO,
				PLAN_WPCOM_PRO_2_YEARS,
				PLAN_BLOGGER,
				PLAN_BLOGGER_2_YEARS,
				PLAN_PERSONAL_MONTHLY,
				PLAN_PERSONAL,
				PLAN_PERSONAL_2_YEARS,
				PLAN_PREMIUM_MONTHLY,
				PLAN_PREMIUM,
				PLAN_PREMIUM_2_YEARS,
				PLAN_BUSINESS_MONTHLY,
				PLAN_BUSINESS,
				PLAN_BUSINESS_2_YEARS,
				PLAN_ECOMMERCE_MONTHLY,
				PLAN_ECOMMERCE,
				PLAN_ECOMMERCE_TRIAL_MONTHLY,
				PLAN_MIGRATION_TRIAL_MONTHLY,
				PLAN_HOSTING_TRIAL_MONTHLY,
			].includes( plan ),
		getProductId: () => 1031,
		getStoreSlug: () => PLAN_ECOMMERCE_2_YEARS,
		getPathSlug: () => 'ecommerce-2-years',
	},

	[ PLAN_WOOEXPRESS_MEDIUM_MONTHLY ]: {
		...getPlanWooExpressMediumDetails(),
		...getMonthlyTimeframe(),
		type: TYPE_WOOEXPRESS_MEDIUM,
		getBillingTimeFrame: () => translate( 'per month' ),
		availableFor: ( plan ) =>
			[ PLAN_FREE, PLAN_ECOMMERCE_TRIAL_MONTHLY, PLAN_WOOEXPRESS_SMALL_MONTHLY ].includes( plan ),
		getProductId: () => 1053,
		getStoreSlug: () => PLAN_WOOEXPRESS_MEDIUM_MONTHLY,
		getPathSlug: () => 'wooexpress-medium-monthly',
	},

	[ PLAN_WOOEXPRESS_MEDIUM ]: {
		...getPlanWooExpressMediumDetails(),
		term: TERM_ANNUALLY,
		getBillingTimeFrame: WPComGetBillingTimeframe,
		type: TYPE_WOOEXPRESS_MEDIUM,
		availableFor: ( plan ) =>
			[
				PLAN_FREE,
				PLAN_WOOEXPRESS_MEDIUM_MONTHLY,
				PLAN_ECOMMERCE_TRIAL_MONTHLY,
				PLAN_WOOEXPRESS_SMALL,
				PLAN_WOOEXPRESS_SMALL_MONTHLY,
			].includes( plan ),
		getProductId: () => 1055,
		getStoreSlug: () => PLAN_WOOEXPRESS_MEDIUM,
		getPathSlug: () => 'wooexpress-medium-yearly',
	},

	[ PLAN_WOOEXPRESS_SMALL_MONTHLY ]: {
		...getPlanWooExpressSmallDetails(),
		...getMonthlyTimeframe(),
		type: TYPE_WOOEXPRESS_SMALL,
		getBillingTimeFrame: () => translate( 'per month' ),
		availableFor: ( plan ) => [ PLAN_FREE, PLAN_ECOMMERCE_TRIAL_MONTHLY ].includes( plan ),
		getProductId: () => 1054,
		getStoreSlug: () => PLAN_WOOEXPRESS_SMALL_MONTHLY,
		getPathSlug: () => 'wooexpress-small-monthly',
	},

	[ PLAN_WOOEXPRESS_SMALL ]: {
		...getPlanWooExpressSmallDetails(),
		type: TYPE_WOOEXPRESS_SMALL,
		term: TERM_ANNUALLY,
		getBillingTimeFrame: WPComGetBillingTimeframe,
		availableFor: ( plan ) =>
			[ PLAN_FREE, PLAN_WOOEXPRESS_SMALL_MONTHLY, PLAN_ECOMMERCE_TRIAL_MONTHLY ].includes( plan ),
		getProductId: () => 1056,
		getStoreSlug: () => PLAN_WOOEXPRESS_SMALL,
		getPathSlug: () => 'wooexpress-small-yearly',
	},

	// Not a real plan. This is used to show the Plus offering in the Woo Express plans grid
	[ PLAN_WOOEXPRESS_PLUS ]: {
		...getPlanWooExpressPlusDetails(),
		term: TERM_ANNUALLY,
		getBillingTimeFrame: () => '',
		getProductId: () => 0,
		getStoreSlug: () => PLAN_WOOEXPRESS_PLUS,
	},

	// Not a real plan. This is used to show the Enterprise (VIP) offering in
	// the main plans grid as part of pdgrnI-1Qp-p2.
	[ PLAN_ENTERPRISE_GRID_WPCOM ]: {
		...get2023EnterprisGrideDetails(),
		term: TERM_ANNUALLY,
		getBillingTimeFrame: () => '',
		getProductId: () => 0,
		getStoreSlug: () => PLAN_ENTERPRISE_GRID_WPCOM,
		getPathSlug: () => 'enterprise',
	},
	[ PLAN_ECOMMERCE_3_YEARS ]: {
		...getPlanEcommerceDetails(),
		term: TERM_TRIENNIALLY,
		getBillingTimeFrame: WPComGetTriennialBillingTimeframe,
		availableFor: ( plan ) =>
			[
				PLAN_FREE,
				PLAN_WPCOM_STARTER,
				PLAN_WPCOM_PRO_MONTHLY,
				PLAN_WPCOM_PRO,
				PLAN_WPCOM_PRO_2_YEARS,
				PLAN_BLOGGER,
				PLAN_BLOGGER_2_YEARS,
				PLAN_PERSONAL_MONTHLY,
				PLAN_PERSONAL,
				PLAN_PERSONAL_2_YEARS,
				PLAN_PERSONAL_3_YEARS,
				PLAN_PREMIUM_MONTHLY,
				PLAN_PREMIUM,
				PLAN_PREMIUM_2_YEARS,
				PLAN_PREMIUM_3_YEARS,
				PLAN_BUSINESS_MONTHLY,
				PLAN_BUSINESS,
				PLAN_BUSINESS_2_YEARS,
				PLAN_BUSINESS_3_YEARS,
				PLAN_ECOMMERCE_MONTHLY,
				PLAN_ECOMMERCE,
				PLAN_ECOMMERCE_2_YEARS,
				PLAN_ECOMMERCE_TRIAL_MONTHLY,
				PLAN_MIGRATION_TRIAL_MONTHLY,
				PLAN_HOSTING_TRIAL_MONTHLY,
			].includes( plan ),
		getProductId: () => 1051,
		getStoreSlug: () => PLAN_ECOMMERCE_3_YEARS,
		getPathSlug: () => 'ecommerce-3-years',
	},

	[ PLAN_JETPACK_FREE ]: {
		term: TERM_ANNUALLY,
		group: GROUP_JETPACK,
		type: TYPE_FREE,
		getTitle: () => i18n.translate( 'Free' ),
		getProductId: () => 2002,
		getStoreSlug: () => PLAN_JETPACK_FREE,
		getTagline: ( siteFeatures = [] ) => {
			const hasSiteJetpackBackup = siteFeatures.some( ( feature ) =>
				[
					FEATURE_JETPACK_BACKUP_DAILY,
					FEATURE_JETPACK_BACKUP_DAILY_MONTHLY,
					FEATURE_JETPACK_BACKUP_REALTIME,
					FEATURE_JETPACK_BACKUP_REALTIME_MONTHLY,
					FEATURE_JETPACK_BACKUP_T1_BI_YEARLY,
					FEATURE_JETPACK_BACKUP_T1_YEARLY,
					FEATURE_JETPACK_BACKUP_T1_MONTHLY,
					FEATURE_JETPACK_BACKUP_T2_YEARLY,
					FEATURE_JETPACK_BACKUP_T2_MONTHLY,
				].includes( feature )
			);
			const hasSiteJetpackScan = siteFeatures.some( ( feature ) =>
				[ FEATURE_JETPACK_SCAN_DAILY, FEATURE_JETPACK_SCAN_DAILY_MONTHLY ].includes( feature )
			);
			if ( hasSiteJetpackBackup && hasSiteJetpackScan ) {
				return i18n.translate(
					'Upgrade your site to access additional features, including spam protection and priority support.'
				);
			} else if ( hasSiteJetpackBackup ) {
				return i18n.translate(
					'Upgrade your site to access additional features, including spam protection, security scanning, and priority support.'
				);
			} else if ( hasSiteJetpackScan ) {
				return i18n.translate(
					'Upgrade your site to access additional features, including spam protection, backups, and priority support.'
				);
			}
			return i18n.translate(
				'Upgrade your site for additional features, including spam protection, backups, security scanning, and priority support.'
			);
		},
		getDescription: () =>
			i18n.translate(
				'The features most needed by WordPress sites' +
					' — perfectly packaged and optimized for everyone.'
			),
		getBillingTimeFrame: () => i18n.translate( 'for life' ),
		getIncludedFeatures: () => [
			FEATURE_STANDARD_SECURITY_TOOLS,
			FEATURE_SITE_STATS,
			FEATURE_TRAFFIC_TOOLS,
			FEATURE_MANAGE,
			FEATURE_ADVANCED_SEO,
			FEATURE_SEO_PREVIEW_TOOLS,
			FEATURE_FREE_WORDPRESS_THEMES,
			FEATURE_SITE_STATS,
			FEATURE_STANDARD_SECURITY_TOOLS,
			FEATURE_TRAFFIC_TOOLS,
			FEATURE_BLANK,
		],
	},

	[ PLAN_JETPACK_PREMIUM ]: {
		...getJetpackPremiumDetails(),
		...getAnnualTimeframe(),
		getProductId: () => 2000,
		getStoreSlug: () => PLAN_JETPACK_PREMIUM,
		getPathSlug: () => 'premium',
	},

	[ PLAN_JETPACK_PREMIUM_MONTHLY ]: {
		...getJetpackPremiumDetails(),
		...getMonthlyTimeframe(),
		getProductId: () => 2003,
		getStoreSlug: () => PLAN_JETPACK_PREMIUM_MONTHLY,
		getPathSlug: () => 'premium-monthly',
	},

	[ PLAN_JETPACK_PERSONAL ]: {
		...getJetpackPersonalDetails(),
		...getAnnualTimeframe(),
		getProductId: () => 2005,
		getStoreSlug: () => PLAN_JETPACK_PERSONAL,
		getPathSlug: () => 'jetpack-personal',
	},

	[ PLAN_JETPACK_PERSONAL_MONTHLY ]: {
		...getJetpackPersonalDetails(),
		...getMonthlyTimeframe(),
		getProductId: () => 2006,
		getStoreSlug: () => PLAN_JETPACK_PERSONAL_MONTHLY,
		getPathSlug: () => 'jetpack-personal-monthly',
	},

	[ PLAN_JETPACK_BUSINESS ]: {
		...getJetpackBusinessDetails(),
		...getAnnualTimeframe(),
		getProductId: () => 2001,
		getStoreSlug: () => PLAN_JETPACK_BUSINESS,
		getPathSlug: () => 'professional',
	},

	[ PLAN_JETPACK_BUSINESS_MONTHLY ]: {
		...getJetpackBusinessDetails(),
		...getMonthlyTimeframe(),
		getProductId: () => 2004,
		getStoreSlug: () => PLAN_JETPACK_BUSINESS_MONTHLY,
		getPathSlug: () => 'professional-monthly',
	},

	[ PLAN_JETPACK_SECURITY_DAILY ]: {
		...getPlanJetpackSecurityDailyDetails(),
		...getAnnualTimeframe(),
		getMonthlySlug: () => PLAN_JETPACK_SECURITY_DAILY_MONTHLY,
		getStoreSlug: () => PLAN_JETPACK_SECURITY_DAILY,
		getPathSlug: () => 'security-daily',
		getProductId: () => 2010,
	},

	[ PLAN_JETPACK_SECURITY_DAILY_MONTHLY ]: {
		...getPlanJetpackSecurityDailyDetails(),
		...getMonthlyTimeframe(),
		getAnnualSlug: () => PLAN_JETPACK_SECURITY_DAILY,
		getStoreSlug: () => PLAN_JETPACK_SECURITY_DAILY_MONTHLY,
		getPathSlug: () => 'security-daily-monthly',
		getProductId: () => 2011,
	},

	[ PLAN_JETPACK_SECURITY_REALTIME ]: {
		...getPlanJetpackSecurityRealtimeDetails(),
		...getAnnualTimeframe(),
		getMonthlySlug: () => PLAN_JETPACK_SECURITY_REALTIME_MONTHLY,
		getStoreSlug: () => PLAN_JETPACK_SECURITY_REALTIME,
		getPathSlug: () => 'security-realtime',
		getProductId: () => 2012,
	},

	[ PLAN_JETPACK_SECURITY_REALTIME_MONTHLY ]: {
		...getPlanJetpackSecurityRealtimeDetails(),
		...getMonthlyTimeframe(),
		getAnnualSlug: () => PLAN_JETPACK_SECURITY_REALTIME,
		getStoreSlug: () => PLAN_JETPACK_SECURITY_REALTIME_MONTHLY,
		getPathSlug: () => 'security-realtime-monthly',
		getProductId: () => 2013,
	},

	[ PLAN_JETPACK_COMPLETE_BI_YEARLY ]: {
		...getPlanJetpackCompleteDetails(),
		...getBiAnnualTimeframe(),
		getStoreSlug: () => PLAN_JETPACK_COMPLETE_BI_YEARLY,
		getPathSlug: () => 'complete-bi-yearly',
		getProductId: () => 2035,
		getProductsIncluded: () => [
			PRODUCT_JETPACK_BACKUP_T2_YEARLY,
			PRODUCT_JETPACK_SCAN_BI_YEARLY,
			PRODUCT_JETPACK_ANTI_SPAM_BI_YEARLY,
			PRODUCT_JETPACK_VIDEOPRESS_BI_YEARLY,
			PRODUCT_JETPACK_BOOST_BI_YEARLY,
			PRODUCT_JETPACK_SOCIAL_ADVANCED_BI_YEARLY,
			PRODUCT_JETPACK_SEARCH_BI_YEARLY,
			PRODUCT_JETPACK_STATS_BI_YEARLY,
			PRODUCT_JETPACK_AI_BI_YEARLY,
			PRODUCT_JETPACK_CRM,
		],
		getWhatIsIncluded: () => [
			translate( 'VaultPress Backup: Real-time backups as you edit' ),
			translate( '1TB (1,000GB) of cloud storage' ),
			translate( '1-year activity log archive' ),
			translate( 'Unlimited one-click restores from the last 1 year' ),
			translate( 'Scan: Real-time malware scanning and one-click fixes' ),
			translate( 'Akismet: Comment and form spam protection (60k API calls/mo)' ),
			translate( 'VideoPress: 1TB of ad-free video hosting' ),
			translate( 'Boost: Automatic CSS generation' ),
			translate( 'Site Search: Up to 100k records and 100k requests/mo.' ),
			translate(
				'Social: Get unlimited shares and share as a post by attaching images or videos.'
			),
			translate( 'CRM: Entrepreneur with 30 extensions' ),
		],
	},

	[ PLAN_JETPACK_COMPLETE ]: {
		...getPlanJetpackCompleteDetails(),
		...getAnnualTimeframe(),
		getStoreSlug: () => PLAN_JETPACK_COMPLETE,
		getPathSlug: () => 'complete',
		getProductId: () => 2014,
		getMonthlySlug: () => PLAN_JETPACK_COMPLETE_MONTHLY,
		getProductsIncluded: () => [
			PRODUCT_JETPACK_BACKUP_T2_YEARLY,
			PRODUCT_JETPACK_SCAN,
			PRODUCT_JETPACK_ANTI_SPAM,
			PRODUCT_JETPACK_VIDEOPRESS,
			PRODUCT_JETPACK_BOOST,
			PRODUCT_JETPACK_SOCIAL_ADVANCED,
			PRODUCT_JETPACK_SEARCH,
			PRODUCT_JETPACK_STATS_YEARLY,
			PRODUCT_JETPACK_AI_YEARLY,
			PRODUCT_JETPACK_CRM,
		],
		getWhatIsIncluded: () => [
			translate( 'VaultPress Backup: Real-time backups as you edit' ),
			translate( '1TB (1,000GB) of cloud storage' ),
			translate( '1-year activity log archive' ),
			translate( 'Unlimited one-click restores from the last 1 year' ),
			translate( 'Scan: Real-time malware scanning and one-click fixes' ),
			translate( 'Akismet: Comment and form spam protection (60k API calls/mo)' ),
			translate( 'VideoPress: 1TB of ad-free video hosting' ),
			translate( 'Boost: Automatic CSS generation' ),
			translate( 'Site Search: Up to 100k records and 100k requests/mo.' ),
			translate(
				'Social: Get unlimited shares and share as a post by attaching images or videos.'
			),
			translate( 'CRM: Entrepreneur with 30 extensions' ),
		],
	},

	[ PLAN_JETPACK_COMPLETE_MONTHLY ]: {
		...getPlanJetpackCompleteDetails(),
		...getMonthlyTimeframe(),
		getStoreSlug: () => PLAN_JETPACK_COMPLETE_MONTHLY,
		getPathSlug: () => 'complete-monthly',
		getProductId: () => 2015,
		getAnnualSlug: () => PLAN_JETPACK_COMPLETE,
		getProductsIncluded: () => [
			PRODUCT_JETPACK_BACKUP_T2_MONTHLY,
			PRODUCT_JETPACK_SCAN_MONTHLY,
			PRODUCT_JETPACK_ANTI_SPAM_MONTHLY,
			PRODUCT_JETPACK_VIDEOPRESS_MONTHLY,
			PRODUCT_JETPACK_BOOST_MONTHLY,
			PRODUCT_JETPACK_SOCIAL_ADVANCED_MONTHLY,
			PRODUCT_JETPACK_SEARCH_MONTHLY,
			PRODUCT_JETPACK_STATS_MONTHLY,
			PRODUCT_JETPACK_AI_MONTHLY,
			PRODUCT_JETPACK_CRM_MONTHLY,
		],
		getWhatIsIncluded: () => [
			translate( 'VaultPress Backup: Real-time backups as you edit' ),
			translate( '1TB (1,000GB) of cloud storage' ),
			translate( '1-year activity log archive' ),
			translate( 'Unlimited one-click restores from the last 1-year' ),
			translate( 'Scan: Real-time malware scanning and one-click fixes' ),
			translate( 'Akismet: Comment and form spam protection (60k API calls/mo)' ),
			translate( 'VideoPress: 1TB of ad-free video hosting' ),
			translate( 'Boost: Automatic CSS generation' ),
			translate( 'Site Search: Up to 100k records and 100k requests/mo.' ),
			translate(
				'Social: Get unlimited shares and share as a post by attaching images or videos.'
			),
			translate( 'CRM: Entrepreneur with 30 extensions' ),
		],
	},

	[ PLAN_JETPACK_SECURITY_T1_BI_YEARLY ]: {
		...getPlanJetpackSecurityT1Details(),
		...getBiAnnualTimeframe(),
		getStoreSlug: () => PLAN_JETPACK_SECURITY_T1_BI_YEARLY,
		getPathSlug: () => 'security-20gb-bi-yearly',
		getProductId: () => 2034,
		getProductsIncluded: () => [
			PRODUCT_JETPACK_BACKUP_T1_BI_YEARLY,
			PRODUCT_JETPACK_SCAN_BI_YEARLY,
			PRODUCT_JETPACK_ANTI_SPAM_BI_YEARLY,
		],
		getWhatIsIncluded: () => [
			translate( 'VaultPress Backup: Real-time backups as you edit' ),
			translate( '10GB of cloud storage' ),
			translate( '30-day activity log archive' ),
			translate( 'Unlimited one-click restores from the last 30 days' ),
			translate( 'Scan: Real-time malware scanning and one-click fixes' ),
			translate( 'Akismet: Comment and form spam protection (10k API calls/mo)' ),
		],
	},

	[ PLAN_JETPACK_SECURITY_T1_YEARLY ]: {
		...getPlanJetpackSecurityT1Details(),
		...getAnnualTimeframe(),
		getStoreSlug: () => PLAN_JETPACK_SECURITY_T1_YEARLY,
		getPathSlug: () => 'security-20gb-yearly',
		getProductId: () => 2016,
		getProductsIncluded: () => [
			PRODUCT_JETPACK_BACKUP_T1_YEARLY,
			PRODUCT_JETPACK_SCAN,
			PRODUCT_JETPACK_ANTI_SPAM,
		],
		getWhatIsIncluded: () => [
			translate( 'VaultPress Backup: Real-time backups as you edit' ),
			translate( '10GB of cloud storage' ),
			translate( '30-day activity log archive' ),
			translate( 'Unlimited one-click restores from the last 30 days' ),
			translate( 'Scan: Real-time malware scanning and one-click fixes' ),
			translate( 'Akismet: Comment and form spam protection (10k API calls/mo)' ),
		],
	},

	[ PLAN_JETPACK_SECURITY_T1_MONTHLY ]: {
		...getPlanJetpackSecurityT1Details(),
		...getMonthlyTimeframe(),
		getStoreSlug: () => PLAN_JETPACK_SECURITY_T1_MONTHLY,
		getPathSlug: () => 'security-20gb-monthly',
		getProductId: () => 2017,
		getProductsIncluded: () => [
			PRODUCT_JETPACK_BACKUP_T1_MONTHLY,
			PRODUCT_JETPACK_SCAN_MONTHLY,
			PRODUCT_JETPACK_ANTI_SPAM_MONTHLY,
		],
		getWhatIsIncluded: () => [
			translate( 'VaultPress Backup: Real-time backups as you edit' ),
			translate( '10GB of cloud storage' ),
			translate( '30-day activity log archive' ),
			translate( 'Unlimited one-click restores from the last 30 days' ),
			translate( 'Scan: Real-time malware scanning and one-click fixes' ),
			translate( 'Akismet: Comment and form spam protection (10k API calls/mo)' ),
		],
	},

	[ PLAN_JETPACK_SECURITY_T2_YEARLY ]: {
		...getPlanJetpackSecurityT2Details(),
		...getAnnualTimeframe(),
		getStoreSlug: () => PLAN_JETPACK_SECURITY_T2_YEARLY,
		getPathSlug: () => 'security-1tb-yearly',
		getProductId: () => 2019,
		getProductsIncluded: () => [
			PRODUCT_JETPACK_BACKUP_T2_YEARLY,
			PRODUCT_JETPACK_SCAN,
			PRODUCT_JETPACK_ANTI_SPAM,
		],
		getWhatIsIncluded: () => [
			translate( 'VaultPress Backup: Real-time backups as you edit' ),
			translate( '{{strong}}1TB (1,000GB){{/strong}} of cloud storage', {
				components: {
					strong: <strong />,
				},
			} ),
			translate( '{{strong}}1-year{{/strong}} activity log archive', {
				components: {
					strong: <strong />,
				},
			} ),
			translate( 'Unlimited one-click restores from the last {{strong}}1 year{{/strong}}', {
				components: {
					strong: <strong />,
				},
			} ),
			translate( 'Scan: Real-time malware scanning and one-click fixes' ),
			translate( 'Akismet: Comment and form spam protection (10k API calls/mo)' ),
		],
	},

	[ PLAN_JETPACK_SECURITY_T2_MONTHLY ]: {
		...getPlanJetpackSecurityT2Details(),
		...getMonthlyTimeframe(),
		getStoreSlug: () => PLAN_JETPACK_SECURITY_T2_MONTHLY,
		getPathSlug: () => 'security-1tb-monthly',
		getProductId: () => 2020,
		getProductsIncluded: () => [
			PRODUCT_JETPACK_BACKUP_T2_MONTHLY,
			PRODUCT_JETPACK_SCAN_MONTHLY,
			PRODUCT_JETPACK_ANTI_SPAM_MONTHLY,
		],
		getWhatIsIncluded: () => [
			translate( 'VaultPress Backup: Real-time backups as you edit' ),
			translate( '{{strong}}1TB (1,000GB){{/strong}} of cloud storage', {
				components: {
					strong: <strong />,
				},
			} ),
			translate( '{{strong}}1-year{{/strong}} activity log archive', {
				components: {
					strong: <strong />,
				},
			} ),
			translate( 'Unlimited one-click restores from the last {{strong}}1 year{{/strong}}', {
				components: {
					strong: <strong />,
				},
			} ),
			translate( 'Scan: Real-time malware scanning and one-click fixes' ),
			translate( 'Akismet: Comment and form spam protection (10k API calls/mo)' ),
		],
	},

	[ PLAN_JETPACK_STARTER_YEARLY ]: {
		...getPlanJetpackStarterDetails(),
		...getAnnualTimeframe(),
		getStoreSlug: () => PLAN_JETPACK_STARTER_YEARLY,
		getPathSlug: () => 'starter-yearly',
		getProductId: () => 2030,
		getProductsIncluded: () => [ PRODUCT_JETPACK_BACKUP_T0_YEARLY, PRODUCT_JETPACK_ANTI_SPAM ],
		getWhatIsIncluded: () => [
			translate( 'VaultPress Backup: Real-time backups as you edit' ),
			translate( '1GB of cloud storage' ),
			translate( '30-day activity log archive' ),
			translate( 'Unlimited one-click restores from the last 30 days' ),
			translate( 'Akismet: Comment and form spam protection (1k API calls/mo)' ),
		],
	},

	[ PLAN_JETPACK_STARTER_MONTHLY ]: {
		...getPlanJetpackStarterDetails(),
		...getMonthlyTimeframe(),
		getStoreSlug: () => PLAN_JETPACK_STARTER_MONTHLY,
		getPathSlug: () => 'starter-monthly',
		getProductId: () => 2031,
		getProductsIncluded: () => [
			PRODUCT_JETPACK_BACKUP_T0_MONTHLY,
			PRODUCT_JETPACK_ANTI_SPAM_MONTHLY,
		],
		getWhatIsIncluded: () => [
			translate( 'VaultPress Backup: Real-time backups as you edit' ),
			translate( '1GB of cloud storage' ),
			translate( '30-day activity log archive' ),
			translate( 'Unlimited one-click restores from the last 30 days' ),
			translate( 'Akismet: Comment and form spam protection (1k API calls/mo)' ),
		],
	},

	[ PLAN_JETPACK_GROWTH_MONTHLY ]: {
		...getPlanJetpackGrowthDetails(),
		...getMonthlyTimeframe(),
		getProductId: () => 2021,
		getStoreSlug: () => PLAN_JETPACK_GROWTH_MONTHLY,
		getPathSlug: () => 'growth-monthly',
		getProductsIncluded: () => [ PRODUCT_JETPACK_STATS_MONTHLY, PRODUCT_JETPACK_SOCIAL_V1_MONTHLY ],
	},

	[ PLAN_JETPACK_GROWTH_YEARLY ]: {
		...getPlanJetpackGrowthDetails(),
		...getAnnualTimeframe(),
		getProductId: () => 2022,
		getStoreSlug: () => PLAN_JETPACK_GROWTH_YEARLY,
		getPathSlug: () => 'growth-yearly',
		getMonthlySlug: () => PLAN_JETPACK_GROWTH_MONTHLY,
		getProductsIncluded: () => [ PRODUCT_JETPACK_STATS_YEARLY, PRODUCT_JETPACK_SOCIAL_V1_YEARLY ],
	},

	[ PLAN_JETPACK_GROWTH_BI_YEARLY ]: {
		...getPlanJetpackGrowthDetails(),
		...getBiAnnualTimeframe(),
		getProductId: () => 2023,
		getStoreSlug: () => PLAN_JETPACK_GROWTH_BI_YEARLY,
		getPathSlug: () => 'growth-bi-yearly',
		getProductsIncluded: () => [
			PRODUCT_JETPACK_STATS_BI_YEARLY,
			PRODUCT_JETPACK_SOCIAL_V1_BI_YEARLY,
		],
	},

	[ PLAN_JETPACK_GOLDEN_TOKEN ]: {
		...getPlanJetpackGoldenTokenDetails(),
		...getAnnualTimeframe(),
		getMonthlySlug: () => PLAN_JETPACK_GOLDEN_TOKEN,
		getStoreSlug: () => PLAN_JETPACK_GOLDEN_TOKEN,
		getPathSlug: () => 'golden-token',
		getProductId: () => 2900,
	},

	[ PLAN_P2_PLUS ]: {
		...getDotcomPlanDetails(),
		group: GROUP_P2,
		type: TYPE_P2_PLUS,
		getTitle: () => i18n.translate( 'P2+' ),
		getDescription: () => '',
		getPlanTagline: () =>
			i18n.translate(
				'{{strong}}Best for professionals:{{/strong}} Enhance your P2 with more space for audio and video, advanced search, an activity overview panel, and priority customer support.',
				plansDescriptionHeadingComponent
			),
		getShortDescription: () => i18n.translate( 'Some short description' ),
		get2023PricingGridSignupWpcomFeatures: () => [
			FEATURE_P2_13GB_STORAGE,
			FEATURE_P2_ADVANCED_SEARCH,
			FEATURE_P2_VIDEO_SHARING,
			FEATURE_P2_MORE_FILE_TYPES,
			FEATURE_P2_PRIORITY_CHAT_EMAIL_SUPPORT,
			FEATURE_P2_ACTIVITY_OVERVIEW,
		],
		getStorageFeature: () => FEATURE_P2_13GB_STORAGE,
		getPlanCompareFeatures: () => [
			// pay attention to ordering, shared features should align on /plan page
			FEATURE_P2_13GB_STORAGE,
			FEATURE_P2_ADVANCED_SEARCH,
			FEATURE_P2_VIDEO_SHARING,
			FEATURE_P2_MORE_FILE_TYPES,
			FEATURE_P2_PRIORITY_CHAT_EMAIL_SUPPORT,
			FEATURE_P2_ACTIVITY_OVERVIEW,
		],

		// TODO: update this once we put P2+ in the signup.
		getSignupFeatures: () => [ FEATURE_FAST_SUPPORT_FROM_EXPERTS ],

		// TODO: no idea about this, copied from the WP.com Premium plan.
		// Features not displayed but used for checking plan abilities
		getIncludedFeatures: () => [
			FEATURE_AUDIO_UPLOADS,
			FEATURE_JETPACK_SEARCH_BI_YEARLY,
			FEATURE_JETPACK_SEARCH,
			FEATURE_JETPACK_SEARCH_MONTHLY,
		],
		getInferiorFeatures: () => [],

		// TODO: Calypso requires this prop but we probably don't need it. Refactor Calypso?
		getAudience: () => i18n.translate( 'Best for bloggers' ),

		...getMonthlyTimeframe(),
		availableFor: ( plan ) => [ PLAN_FREE ].includes( plan ), //TODO: only for P2 sites.
		getProductId: () => 1040,
		getStoreSlug: () => PLAN_P2_PLUS,
		getPathSlug: () => 'p2-plus',
		getBillingTimeFrame: () => translate( 'per user per month' ),
	},
};

PLANS_LIST[ PLAN_P2_FREE ] = {
	...PLANS_LIST[ PLAN_FREE ],
	group: GROUP_P2,
	getPlanTagline: () =>
		i18n.translate(
			'{{strong}}Best for small groups:{{/strong}} All the features needed to share, discuss, review, and collaborate with your team in one spot, without interruptions.',
			plansDescriptionHeadingComponent
		),
	getTitle: () => i18n.translate( 'P2 Free' ),
	get2023PricingGridSignupWpcomFeatures: () => [
		FEATURE_P2_3GB_STORAGE,
		FEATURE_P2_UNLIMITED_USERS,
		FEATURE_P2_UNLIMITED_POSTS_PAGES,
		FEATURE_P2_SIMPLE_SEARCH,
		FEATURE_P2_CUSTOMIZATION_OPTIONS,
	],
	getStorageFeature: () => FEATURE_P2_3GB_STORAGE,
	getPlanCompareFeatures: () => [
		// pay attention to ordering, shared features should align on /plan page
		FEATURE_P2_3GB_STORAGE,
		FEATURE_P2_UNLIMITED_USERS,
		FEATURE_P2_UNLIMITED_POSTS_PAGES,
		FEATURE_P2_SIMPLE_SEARCH,
		FEATURE_P2_CUSTOMIZATION_OPTIONS,
	],
};

// Brand new WPCOM plans
PLANS_LIST[ PLAN_WPCOM_STARTER ] = {
	...getDotcomPlanDetails(),
	group: GROUP_WPCOM,
	type: TYPE_STARTER,
	term: TERM_ANNUALLY,
	getTitle: () => i18n.translate( 'WordPress Starter' ),
	getProductId: () => 1033,
	getStoreSlug: () => PLAN_WPCOM_STARTER,
	getPathSlug: () => 'starter',
	getDescription: () =>
		i18n.translate( 'Start with a custom domain name, simple payments, and extra storage.' ),
	getSubTitle: () => i18n.translate( 'Essential features. Freedom to grow.' ),
	getBillingTimeFrame: () => i18n.translate( 'per month, billed yearly' ),
	getPlanCompareFeatures: () => [
		FEATURE_UNLIMITED_TRAFFIC,
		FEATURE_MANAGED_HOSTING,
		FEATURE_FREE_THEMES,
		FEATURE_CUSTOM_DOMAIN,
		FEATURE_UNLIMITED_ADMINS,
		FEATURE_6GB_STORAGE,
		FEATURE_GOOGLE_ANALYTICS,
		FEATURE_PAYMENT_BLOCKS,
		FEATURE_TITAN_EMAIL,
	],
	getIncludedFeatures: () => [ WPCOM_FEATURES_INSTALL_PURCHASED_PLUGINS ],
};

PLANS_LIST[ PLAN_WPCOM_FLEXIBLE ] = {
	// Inherits the free plan
	...PLANS_LIST[ PLAN_FREE ],
	group: GROUP_WPCOM,
	type: TYPE_FLEXIBLE,
	getTitle: () => i18n.translate( 'WordPress Free' ),
	getBillingTimeFrame: () => i18n.translate( 'upgrade when you need' ),
	getDescription: () =>
		i18n.translate( 'Start your free WordPress.com website. Limited functionality and storage.' ),
	getPlanCompareFeatures: () => [ FEATURE_1GB_STORAGE ],
};

PLANS_LIST[ PLAN_WPCOM_PRO ] = {
	...getPlanProDetails(),
	term: TERM_ANNUALLY,
	getProductId: () => 1032,
	getStoreSlug: () => PLAN_WPCOM_PRO,
	getPathSlug: () => 'pro',
	getBillingTimeFrame: () => i18n.translate( 'per month, billed yearly' ),
};

PLANS_LIST[ PLAN_WPCOM_PRO_MONTHLY ] = {
	...getPlanProDetails(),
	...getMonthlyTimeframe(),
	availableFor: ( plan ) => [ PLAN_FREE ].includes( plan ),
	getProductId: () => 1034,
	getStoreSlug: () => PLAN_WPCOM_PRO_MONTHLY,
	getPathSlug: () => 'pro-monthly',
};

PLANS_LIST[ PLAN_WPCOM_PRO_2_YEARS ] = {
	...getPlanProDetails(),
	term: TERM_BIENNIALLY,
	availableFor: ( plan ) =>
		[ PLAN_FREE, PLAN_WPCOM_STARTER, PLAN_WPCOM_PRO, PLAN_WPCOM_PRO_MONTHLY ].includes( plan ),
	getProductId: () => 1035,
	getStoreSlug: () => PLAN_WPCOM_PRO_2_YEARS,
	getPathSlug: () => 'pro-2-years',
	getBillingTimeFrame: WPComGetBiennialBillingTimeframe,
};

PLANS_LIST[ PLAN_ECOMMERCE_TRIAL_MONTHLY ] = {
	...getDotcomPlanDetails(),
	type: TYPE_ECOMMERCE,
	group: GROUP_WPCOM,
	getProductId: () => 1052,
	getPathSlug: () => PLAN_ECOMMERCE_TRIAL_MONTHLY,
	term: TERM_MONTHLY,
	getBillingTimeFrame: () => i18n.translate( 'free trial' ),
	getStoreSlug: () => PLAN_ECOMMERCE_TRIAL_MONTHLY,
	getTitle: getPlanCommerceTrialTitle,
	getDescription: getPlanCommerceTrialTitle,
	getTagline: () => i18n.translate( 'Get a taste of the world’s most popular eCommerce software.' ),
};

if ( isEnabled( 'plans/migration-trial' ) ) {
	PLANS_LIST[ PLAN_MIGRATION_TRIAL_MONTHLY ] = {
		...getPlanBusinessDetails(),
		type: TYPE_BUSINESS,
		group: GROUP_WPCOM,
		getProductId: () => 1057,
		getPathSlug: () => PLAN_MIGRATION_TRIAL_MONTHLY,
		term: TERM_MONTHLY,
		getBillingTimeFrame: () => i18n.translate( 'free trial' ),
		getStoreSlug: () => PLAN_MIGRATION_TRIAL_MONTHLY,
		getTitle: getPlanBusinessTrialTitle,
	};
}

PLANS_LIST[ PLAN_HOSTING_TRIAL_MONTHLY ] = {
	...getPlanBusinessDetails(),
	getPlanTagline: getPlanBusinessTrialTagline,
	type: TYPE_BUSINESS,
	group: GROUP_WPCOM,
	getProductId: () => 1058,
	getPathSlug: () => PLAN_HOSTING_TRIAL_MONTHLY,
	term: TERM_MONTHLY,
	getBillingTimeFrame: () => i18n.translate( 'Try it for 3 days' ),
	getStoreSlug: () => PLAN_HOSTING_TRIAL_MONTHLY,
	getTitle: getPlanBusinessTrialTitle,
	getDescription: () => i18n.translate( 'Hosting free trial' ),
	getTagline: () => i18n.translate( 'Get a taste of unlimited performance and unbeatable uptime' ),
};
