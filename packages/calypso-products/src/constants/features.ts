import {
	PRODUCT_<PERSON><PERSON><PERSON><PERSON><PERSON>_ANTI_SPAM_BI_YEARLY,
	PRODUCT_<PERSON>ETPAC<PERSON>_ANTI_SPAM,
	PRODUCT_JETPAC<PERSON>_ANTI_SPAM_MONTHLY,
	PRODUCT_<PERSON>ETPAC<PERSON>_<PERSON>C<PERSON><PERSON>_DAILY,
	PRODUCT_JETPACK_BACKUP_DAILY_MONTHLY,
	PRODUCT_JETPACK_BACKUP_REALTIME,
	PRODUCT_JETPACK_BACKUP_REALTIME_MONTHLY,
	PRODUCT_JETPACK_BACKUP_T0_MONTHLY,
	PRODUCT_JETPACK_BACKUP_T0_YEARLY,
	PRODUCT_JETPACK_BACKUP_T1_MONTHLY,
	PRODUCT_JETPACK_BACKUP_T1_YEARLY,
	PRODUCT_JETPACK_BACKUP_T1_BI_YEARLY,
	PRODUCT_JETPACK_BAC<PERSON>UP_T2_MONTHLY,
	PRODUCT_JETPACK_BACK<PERSON>_T2_YEARLY,
	PRODUCT_JETPACK_BOOST_BI_YEARLY,
	PRODUCT_JETPACK_BOOST,
	PRODUCT_JETPACK_BOOST_MONTHLY,
	PRODUCT_JETPACK_SOCIAL_V1_YEARLY,
	PRODUCT_JETPACK_SOCIAL_V1_MONTHLY,
	PRODUCT_JETPACK_SOCIAL_V1_BI_YEARLY,
	PRODUCT_JETPACK_CRM,
	PRODUCT_JETPACK_CRM_MONTHLY,
	PRODUCT_JETPACK_SCAN_BI_YEARLY,
	PRODUCT_JETPACK_SCAN,
	PRODUCT_JETPACK_SCAN_MONTHLY,
	PRODUCT_JETPACK_SEARCH_BI_YEARLY,
	PRODUCT_JETPACK_SEARCH,
	PRODUCT_JETPACK_SEARCH_MONTHLY,
	PRODUCT_JETPACK_SOCIAL_BASIC_BI_YEARLY,
	PRODUCT_JETPACK_SOCIAL_BASIC,
	PRODUCT_JETPACK_SOCIAL_BASIC_MONTHLY,
	PRODUCT_JETPACK_VIDEOPRESS_BI_YEARLY,
	PRODUCT_JETPACK_VIDEOPRESS,
	PRODUCT_JETPACK_VIDEOPRESS_MONTHLY,
	PRODUCT_JETPACK_SOCIAL_ADVANCED_BI_YEARLY,
	PRODUCT_JETPACK_SOCIAL_ADVANCED,
	PRODUCT_JETPACK_SOCIAL_ADVANCED_MONTHLY,
	PRODUCT_JETPACK_MONITOR_MONTHLY,
	PRODUCT_JETPACK_MONITOR_YEARLY,
} from './jetpack';

export const FEATURE_WP_SUBDOMAIN = 'wordpress-subdomain';
export const FEATURE_BLOG_DOMAIN = 'blog-domain';
export const FEATURE_CUSTOM_DOMAIN = 'custom-domain';
export const FEATURE_SET_PRIMARY_CUSTOM_DOMAIN = 'set-primary-custom-domain';
export const FEATURE_JETPACK_ESSENTIAL = 'jetpack-essential';
export const FEATURE_JETPACK_ADVANCED = 'jetpack-advanced';
export const FEATURE_FREE_THEMES = 'free-themes';
export const FEATURE_1GB_STORAGE = '1gb-storage';
export const FEATURE_3GB_STORAGE = '3gb-storage';
export const FEATURE_6GB_STORAGE = '6gb-storage';
export const FEATURE_13GB_STORAGE = '13gb-storage';
export const FEATURE_50GB_STORAGE = '50gb-storage';
export const FEATURE_200GB_STORAGE = '200gb-storage';
export const FEATURE_LEGACY_STORAGE_200GB = 'upload-space-200gb';
export const FEATURE_UNLIMITED_STORAGE = 'unlimited-storage';
export const FEATURE_COMMUNITY_SUPPORT = 'community-support';
export const EXPERT_SUPPORT_ALL_DAYS = 'expert-support-all-days';
export const FEATURE_EMAIL_FORWARDING_EXTENDED_LIMIT = 'email-forwarding-extended-limit';
export const FEATURE_PREMIUM_SUPPORT = 'priority-support';
export const FEATURE_GOOGLE_ANALYTICS = 'google-analytics';
export const FEATURE_GOOGLE_MY_BUSINESS = 'google-my-business';
export const FEATURE_SFTP = 'sftp';
export const FEATURE_SSH = 'ssh';
export const FEATURE_SITE_STAGING_SITES = 'staging-sites';
export const FEATURE_SEAMLESS_STAGING_PRODUCTION_SYNCING = 'seamless-staging-production-syncing';
export const FEATURE_NO_ADS = 'no-adverts';
export const FEATURE_VIDEO_UPLOADS = 'video-upload';
export const FEATURE_VIDEO_UPLOADS_JETPACK_PREMIUM = 'video-upload-jetpack-premium';
export const FEATURE_VIDEO_UPLOADS_JETPACK_PRO = 'video-upload-jetpack-pro';
export const FEATURE_AUDIO_UPLOADS = 'audio-upload';
export const FEATURE_WORDADS_INSTANT = 'wordads-instant';
export const FEATURE_NO_BRANDING = 'no-wp-branding';
export const FEATURE_ADVANCED_SEO = 'advanced-seo';
export const FEATURE_UPLOAD_PLUGINS = 'upload-plugins';
export const FEATURE_UPLOAD_PLUGINS_SUMMER_SPECIAL = 'upload-plugins-summer-special';
export const FEATURE_INSTALL_PLUGINS = 'install-plugins';
export const FEATURE_INSTALL_THEMES = 'install-themes';
export const FEATURE_UPLOAD_THEMES = 'upload-themes';
export const FEATURE_PERFORMANCE = 'performance';
export const FEATURE_REPUBLICIZE = 'republicize';
export const FEATURE_SIMPLE_PAYMENTS = 'simple-payments';
export const FEATURE_ALL_FREE_FEATURES = 'all-free-features';
export const FEATURE_ALL_FREE_FEATURES_JETPACK = 'all-free-features-jetpack';
export const FEATURE_ALL_PERSONAL_FEATURES = 'all-personal-features';
export const FEATURE_ALL_PERSONAL_FEATURES_JETPACK = 'all-personal-features-jetpack';
export const FEATURE_ALL_PREMIUM_FEATURES = 'all-premium-features';
export const FEATURE_ALL_PREMIUM_FEATURES_JETPACK = 'all-premium-features-jetpack';
export const FEATURE_ADVANCED_DESIGN_CUSTOMIZATION = 'advanced-design-customization';
export const FEATURE_UPLOAD_THEMES_PLUGINS = 'upload-themes-and-plugins';
export const FEATURE_FREE_DOMAIN = 'free-custom-domain';
export const FEATURE_FREE_BLOG_DOMAIN = 'free-blog-domain';
export const FEATURE_MONETISE = 'monetise-your-site';
export const FEATURE_EARN_AD = 'earn-ad-revenue';
export const FEATURE_WP_SUBDOMAIN_SIGNUP = 'wordpress-subdomain-signup';
export const FEATURE_ADVANCED_SEO_TOOLS = 'advanced-seo-tools';
export const FEATURE_ADVANCED_SEO_EXPANDED_ABBR = 'advanced-seo-expanded-abbreviation';
export const FEATURE_FREE_THEMES_SIGNUP = 'free-themes-signup';
export const FEATURE_MEMBERSHIPS = 'memberships';
export const FEATURE_DONATIONS = 'donations';
export const FEATURE_RECURRING_PAYMENTS = 'recurring-payments';
// This is a legacy alias, FEATURE_PREMIUM_CONTENT_CONTAINER should be used instead.
export const FEATURE_PREMIUM_CONTENT_BLOCK = 'premium-content-block';
export const FEATURE_PREMIUM_CONTENT_CONTAINER = 'premium-content/container';
export const FEATURE_HOSTING = 'hosting';
export const PREMIUM_DESIGN_FOR_STORES = 'premium-design-for-stores';
export const FEATURE_SFTP_DATABASE = 'sftp-and-database-access';
export const FEATURE_SITE_BACKUPS_AND_RESTORE = 'site-backups-and-restore';
export const FEATURE_SECURITY_SETTINGS = 'security-settings';
export const FEATURE_WOOP = 'woop';
/*
 * TODO: To avoid confusion, this constant value should be renamed to `premium-themes` after
 * `WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED` has been renamed to `premium-themes-unlimited`
 * (see comment below).
 */
export const FEATURE_PREMIUM_THEMES = 'premium-themes-v3';
export const FEATURE_STATS_PAID = 'stats-paid';
export const FEATURE_STATS_FREE = 'stats-free';
export const FEATURE_STATS_COMMERCIAL = 'stats-commercial';
export const FEATURE_STATS_BASIC = 'stats-basic';
// Jetpack features constants
export const FEATURE_BLANK = 'blank-feature';
export const FEATURE_STANDARD_SECURITY_TOOLS = 'standard-security-tools';
export const FEATURE_SITE_STATS = 'site-stats';
export const FEATURE_TRAFFIC_TOOLS = 'traffic-tools';
export const FEATURE_MANAGE = 'jetpack-manage';
export const FEATURE_SPAM_AKISMET_PLUS = 'spam-akismet-plus';
export const FEATURE_OFFSITE_BACKUP_VAULTPRESS_DAILY = 'offsite-backup-vaultpress-daily';
export const FEATURE_OFFSITE_BACKUP_VAULTPRESS_REALTIME = 'offsite-backup-vaultpress-realtime';
export const FEATURE_BACKUP_ARCHIVE_30 = 'backup-archive-30';
export const FEATURE_BACKUP_ARCHIVE_UNLIMITED = 'backup-archive-unlimited';
export const FEATURE_BACKUP_STORAGE_SPACE_UNLIMITED = 'backup-storage-space-unlimited';
export const FEATURE_AUTOMATED_RESTORES = 'automated-restores';
export const FEATURE_EASY_SITE_MIGRATION = 'easy-site-migration';
export const FEATURE_MALWARE_SCANNING_DAILY = 'malware-scanning-daily';
export const FEATURE_MALWARE_SCANNING_DAILY_AND_ON_DEMAND = 'malware-scanning-daily-and-on-demand';
export const FEATURE_ONE_CLICK_THREAT_RESOLUTION = 'one-click-threat-resolution';
export const FEATURE_AUTOMATIC_SECURITY_FIXES = 'automatic-security-fixes';
export const FEATURE_ACTIVITY_LOG = 'site-activity-log';
export const FEATURE_FREE_WORDPRESS_THEMES = 'free-wordpress-themes';
export const FEATURE_SEO_PREVIEW_TOOLS = 'seo-preview-tools';
export const FEATURE_SEARCH = 'search';
export const FEATURE_ACCEPT_PAYMENTS = 'accept-payments';
export const FEATURE_SHIPPING_CARRIERS = 'shipping-carriers';
export const FEATURE_UNLIMITED_PRODUCTS_SERVICES = 'unlimited-products-service';
export const FEATURE_ECOMMERCE_MARKETING = 'ecommerce-marketing';
export const FEATURE_PREMIUM_CUSTOMIZABE_THEMES = 'premium-customizable-themes';
export const FEATURE_ALL_BUSINESS_FEATURES = 'all-business-features';
export const FEATURE_BACKUP_DAILY_V2 = 'backup-daily-v2';
export const FEATURE_BACKUP_REALTIME_V2 = 'backup-realtime-v2';
export const FEATURE_PRODUCT_BACKUP_DAILY_V2 = 'product-backup-daily-v2';
export const FEATURE_PRODUCT_BACKUP_REALTIME_V2 = 'product-backup-realtime-v2';
export const FEATURE_SCAN_V2 = 'scan-v2';
export const FEATURE_PRODUCT_SCAN_DAILY_V2 = 'product-scan-daily-v2';
export const FEATURE_PRODUCT_SCAN_REALTIME_V2 = 'product-scan-realtime-v2';
export const FEATURE_ANTISPAM_V2 = 'antispam-v2';
export const FEATURE_WAF = 'waf';
export const FEATURE_ACTIVITY_LOG_1_YEAR_V2 = 'activity-log-1-year-v2';
export const FEATURE_SEARCH_V2 = 'search-v2';
export const FEATURE_PRODUCT_SEARCH_V2 = 'product-search-v2';
export const FEATURE_PLAN_SECURITY_DAILY = 'security-daily';
export const FEATURE_VIDEO_HOSTING_V2 = 'video-hosting-v2';
export const FEATURE_CRM_V2 = 'crm-v2';
export const FEATURE_CRM_INTEGRATED_WITH_WORDPRESS = 'crm-integrated-with-wordpress';
export const FEATURE_CRM_LEADS_AND_FUNNEL = 'crm-leads-and-funnel';
export const FEATURE_CRM_PROPOSALS_AND_INVOICES = 'crm-proposals-and-invoices';
export const FEATURE_CRM_TRACK_TRANSACTIONS = 'crm-track-transactions';
export const FEATURE_CRM_NO_CONTACT_LIMITS = 'crm-no-contact-limits';
export const FEATURE_COLLECT_PAYMENTS_V2 = 'collect-payments-v2';
export const FEATURE_SECURE_STORAGE_V2 = 'secure-storage-v2';
export const FEATURE_ONE_CLICK_RESTORE_V2 = 'one-click-restore-v2';
export const FEATURE_ONE_CLICK_FIX_V2 = 'one-click-fix-v2';
export const FEATURE_INSTANT_EMAIL_V2 = 'instant-email-v2';
export const FEATURE_AKISMET_V2 = 'akismet-v2';
export const FEATURE_SPAM_BLOCK_V2 = 'spam-block-v2';
export const FEATURE_SPAM_10K_PER_MONTH = 'spam-block-10k';
export const FEATURE_FILTERING_V2 = 'filtering-v2';
export const FEATURE_LANGUAGE_SUPPORT_V2 = 'language-support-v2';
export const FEATURE_SPELLING_CORRECTION_V2 = 'spelling-correction-v2';
export const FEATURE_SUPPORTS_WOOCOMMERCE_V2 = 'supports-woocommerce-v2';
export const FEATURE_JETPACK_BACKUP_DAILY = PRODUCT_JETPACK_BACKUP_DAILY;
export const FEATURE_JETPACK_BACKUP_DAILY_MONTHLY = PRODUCT_JETPACK_BACKUP_DAILY_MONTHLY;
export const FEATURE_JETPACK_BACKUP_REALTIME = PRODUCT_JETPACK_BACKUP_REALTIME;
export const FEATURE_JETPACK_BACKUP_REALTIME_MONTHLY = PRODUCT_JETPACK_BACKUP_REALTIME_MONTHLY;
export const FEATURE_JETPACK_BACKUP_T0_YEARLY = PRODUCT_JETPACK_BACKUP_T0_YEARLY;
export const FEATURE_JETPACK_BACKUP_T0_MONTHLY = PRODUCT_JETPACK_BACKUP_T0_MONTHLY;
export const FEATURE_JETPACK_BACKUP_T1_YEARLY = PRODUCT_JETPACK_BACKUP_T1_YEARLY;
export const FEATURE_JETPACK_BACKUP_T1_BI_YEARLY = PRODUCT_JETPACK_BACKUP_T1_BI_YEARLY;
export const FEATURE_JETPACK_BACKUP_T1_MONTHLY = PRODUCT_JETPACK_BACKUP_T1_MONTHLY;
export const FEATURE_JETPACK_BACKUP_T2_YEARLY = PRODUCT_JETPACK_BACKUP_T2_YEARLY;
export const FEATURE_JETPACK_BACKUP_T2_MONTHLY = PRODUCT_JETPACK_BACKUP_T2_MONTHLY;
export const FEATURE_JETPACK_SCAN_BI_YEARLY = PRODUCT_JETPACK_SCAN_BI_YEARLY;
export const FEATURE_JETPACK_SCAN_DAILY = PRODUCT_JETPACK_SCAN;
export const FEATURE_JETPACK_SCAN_DAILY_MONTHLY = PRODUCT_JETPACK_SCAN_MONTHLY;
export const FEATURE_JETPACK_ANTI_SPAM_BI_YEARLY = PRODUCT_JETPACK_ANTI_SPAM_BI_YEARLY;
export const FEATURE_JETPACK_ANTI_SPAM = PRODUCT_JETPACK_ANTI_SPAM;
export const FEATURE_JETPACK_ANTI_SPAM_MONTHLY = PRODUCT_JETPACK_ANTI_SPAM_MONTHLY;
export const FEATURE_JETPACK_SEARCH_BI_YEARLY = PRODUCT_JETPACK_SEARCH_BI_YEARLY;
export const FEATURE_JETPACK_SEARCH = PRODUCT_JETPACK_SEARCH;
export const FEATURE_JETPACK_SEARCH_MONTHLY = PRODUCT_JETPACK_SEARCH_MONTHLY;
export const FEATURE_JETPACK_VIDEOPRESS_BI_YEARLY = PRODUCT_JETPACK_VIDEOPRESS_BI_YEARLY;
export const FEATURE_JETPACK_VIDEOPRESS = PRODUCT_JETPACK_VIDEOPRESS;
export const FEATURE_JETPACK_VIDEOPRESS_MONTHLY = PRODUCT_JETPACK_VIDEOPRESS_MONTHLY;
export const FEATURE_JETPACK_VIDEOPRESS_EDITOR = 'jetpack-videopress-editor';
export const FEATURE_JETPACK_VIDEOPRESS_STORAGE = 'jetpack-videopress-storage';
export const FEATURE_JETPACK_VIDEOPRESS_UNBRANDED = 'jetpack-videopress-unbranded';
export const FEATURE_JETPACK_CRM = PRODUCT_JETPACK_CRM;
export const FEATURE_JETPACK_CRM_MONTHLY = PRODUCT_JETPACK_CRM_MONTHLY;
export const FEATURE_JETPACK_BOOST_BI_YEARLY = PRODUCT_JETPACK_BOOST_BI_YEARLY;
export const FEATURE_JETPACK_BOOST = PRODUCT_JETPACK_BOOST;
export const FEATURE_JETPACK_BOOST_MONTHLY = PRODUCT_JETPACK_BOOST_MONTHLY;
export const FEATURE_CLOUD_CRITICAL_CSS = 'cloud-critical-css';
export const FEATURE_JETPACK_SOCIAL_ADVANCED_BI_YEARLY = PRODUCT_JETPACK_SOCIAL_ADVANCED_BI_YEARLY;
export const FEATURE_JETPACK_SOCIAL_ADVANCED = PRODUCT_JETPACK_SOCIAL_ADVANCED;
export const FEATURE_JETPACK_SOCIAL_ADVANCED_MONTHLY = PRODUCT_JETPACK_SOCIAL_ADVANCED_MONTHLY;
export const FEATURE_JETPACK_SOCIAL_BASIC_BI_YEARLY = PRODUCT_JETPACK_SOCIAL_BASIC_BI_YEARLY;
export const FEATURE_JETPACK_SOCIAL_BASIC = PRODUCT_JETPACK_SOCIAL_BASIC;
export const FEATURE_JETPACK_SOCIAL_BASIC_MONTHLY = PRODUCT_JETPACK_SOCIAL_BASIC_MONTHLY;
export const FEATURE_JETPACK_SOCIAL_V1_YEARLY = PRODUCT_JETPACK_SOCIAL_V1_YEARLY;
export const FEATURE_JETPACK_SOCIAL_V1_MONTHLY = PRODUCT_JETPACK_SOCIAL_V1_MONTHLY;
export const FEATURE_JETPACK_SOCIAL_V1_BI_YEARLY = PRODUCT_JETPACK_SOCIAL_V1_BI_YEARLY;
export const FEATURE_SOCIAL_AUTO_SHARE = 'social-auto-share';
export const FEATURE_SOCIAL_SHARES_1000 = 'social-shares-1000';
export const FEATURE_SOCIAL_IMAGE_GENERATOR = 'social-image-generator';
export const FEATURE_SOCIAL_ENHANCED_PUBLISHING = 'social-enhanced-publishing';
export const FEATURE_SOCIAL_THREADS_CONNECTION = 'social-threads-connection';
export const FEATURE_JETPACK_MONITOR_MONTHLY = PRODUCT_JETPACK_MONITOR_MONTHLY;
export const FEATURE_JETPACK_MONITOR_YEARLY = PRODUCT_JETPACK_MONITOR_YEARLY;
export const FEATURE_MONITOR_1_MINUTE_CHECK_INTERVAL = 'monitor-1-minute-check-interval';
export const FEATURE_MONITOR_MULTIPLE_EMAIL_RECIPIENTS = 'monitor-multiple-email-recipients';
export const FEATURE_MONITOR_SMS_NOTIFICATIONS = 'monitor-sms-notifications';

// Jetpack tiered product features
export const FEATURE_JETPACK_1GB_BACKUP_STORAGE = 'jetpack-1gb-backup-storage';
export const FEATURE_JETPACK_10GB_BACKUP_STORAGE = 'jetpack-10gb-backup-storage';
export const FEATURE_JETPACK_1TB_BACKUP_STORAGE = 'jetpack-1tb-backup-storage';
export const FEATURE_JETPACK_1_YEAR_ARCHIVE_ACTIVITY_LOG = 'jetpack-1-year-archive-activity-log';
export const FEATURE_JETPACK_30_DAY_ARCHIVE_ACTIVITY_LOG = 'jetpack-30-day-archive-activity-log';
export const FEATURE_JETPACK_REAL_TIME_CLOUD_BACKUPS = 'jetpack-real-time-cloud-backups';
export const FEATURE_JETPACK_REAL_TIME_MALWARE_SCANNING = 'jetpack-real-time-malware-scanning';
export const FEATURE_JETPACK_PRODUCT_BACKUP = 'jetpack-product-backup';
export const FEATURE_JETPACK_PRODUCT_VIDEOPRESS = 'jetpack-product-videopress';
export const FEATURE_JETPACK_ALL_BACKUP_SECURITY_FEATURES = 'jetpack-all-backup-security-features';

// P2 project features
export const FEATURE_P2_3GB_STORAGE = 'p2-3gb-storage';
export const FEATURE_P2_UNLIMITED_USERS = 'p2-unlimited-users';
export const FEATURE_P2_UNLIMITED_POSTS_PAGES = 'p2-unlimited-posts-pages';
export const FEATURE_P2_SIMPLE_SEARCH = 'p2-simple-search';
export const FEATURE_P2_CUSTOMIZATION_OPTIONS = 'p2-customization-options';
export const FEATURE_P2_13GB_STORAGE = 'p2-13gb-storage';
export const FEATURE_P2_ADVANCED_SEARCH = 'p2-advanced-search';
export const FEATURE_P2_VIDEO_SHARING = 'p2-video-sharing';
export const FEATURE_P2_MORE_FILE_TYPES = 'p2-more-file-types';
export const FEATURE_P2_PRIORITY_CHAT_EMAIL_SUPPORT = 'p2-priority-chat-email-support';
export const FEATURE_P2_ACTIVITY_OVERVIEW = 'p2-activity-overview';

// New features Flexible and Pro plans introduced.
export const FEATURE_MANAGED_HOSTING = 'managed-hosting';
export const FEATURE_UNLIMITED_USERS = 'unlimited-users';
export const FEATURE_UNLIMITED_POSTS_PAGES = 'unlimited-posts-pages';
export const FEATURE_PAYMENT_BLOCKS = 'payment-blocks';
export const FEATURE_TITAN_EMAIL = 'titan-email';
export const FEATURE_UNLIMITED_ADMINS = 'unlimited-admins';
export const FEATURE_ADDITIONAL_SITES = 'additional-sites';
export const FEATURE_WOOCOMMERCE = 'woocommerce';
export const FEATURE_SOCIAL_MEDIA_TOOLS = 'social-media-tools';

// From class-wpcom-features.php in WPCOM
export const WPCOM_FEATURES_AI_ASSISTANT = 'ai-assistant';
export const WPCOM_FEATURES_AKISMET = 'akismet';
export const WPCOM_FEATURES_ANTISPAM = 'antispam';
export const WPCOM_FEATURES_ATOMIC = 'atomic';
export const WPCOM_FEATURES_BACKUPS = 'backups';
export const WPCOM_FEATURES_BACKUPS_RESTORE = 'restore';
export const WPCOM_FEATURES_CLASSIC_SEARCH = 'search';
export const WPCOM_FEATURES_COPY_SITE = 'copy-site';
export const WPCOM_FEATURES_FULL_ACTIVITY_LOG = 'full-activity-log';
export const WPCOM_FEATURES_INSTALL_PLUGINS = 'install-plugins';
export const WPCOM_FEATURES_INSTALL_PURCHASED_PLUGINS = 'install-purchased-plugins';
export const WPCOM_FEATURES_INSTANT_SEARCH = 'instant-search';
export const WPCOM_FEATURES_LIVE_SUPPORT = 'live_support';
export const WPCOM_FEATURES_MANAGE_PLUGINS = 'manage-plugins';
export const WPCOM_FEATURES_NO_ADVERTS = 'no-adverts/no-adverts.php';
export const WPCOM_FEATURES_NO_WPCOM_BRANDING = 'no-wpcom-branding';
/*
 * TODO: This constant value should be renamed (here and in `class-wpcom-features.php` in
 * WPCOM) to `premium-themes-unlimited` so it's not confused with `FEATURE_PREMIUM_THEMES`.
 */
export const WPCOM_FEATURES_PREMIUM_THEMES_UNLIMITED = 'premium-themes';
/*
 * TODO: This constant value should be renamed (here and in `class-wpcom-features.php` in
 * WPCOM) to `premium-themes-limited` so it better reflects the actual feature.
 */
export const WPCOM_FEATURES_PREMIUM_THEMES_LIMITED = 'personal-themes';
export const WPCOM_FEATURES_PRIORITY_SUPPORT = 'priority_support';
export const WPCOM_FEATURES_REAL_TIME_BACKUPS = 'real-time-backups';
export const WPCOM_FEATURES_SCAN = 'scan';
export const WPCOM_FEATURES_SCHEDULED_UPDATES = 'scheduled-updates';
export const WPCOM_FEATURES_SEO_PREVIEW_TOOLS = 'seo-preview-tools';
export const WPCOM_FEATURES_SUBSCRIPTION_GIFTING = 'subscription-gifting';
export const WPCOM_FEATURES_LOCKED_MODE = 'locked-mode';
export const WPCOM_FEATURES_LEGACY_CONTACT = 'legacy-contact';
export const WPCOM_FEATURES_UPLOAD_AUDIO_FILES = 'upload-audio-files';
export const WPCOM_FEATURES_UPLOAD_PLUGINS = 'upload-plugins';
export const WPCOM_FEATURES_UPLOAD_VIDEO_FILES = 'upload-video-files';
export const WPCOM_FEATURES_VAULTPRESS_BACKUPS = 'vaultpress-backups';
export const WPCOM_FEATURES_VIDEOPRESS = 'videopress';
export const WPCOM_FEATURES_VIDEOPRESS_UNLIMITED_STORAGE = 'videopress-unlimited-storage';
export const WPCOM_FEATURES_VIDEO_HOSTING = 'video-hosting';
export const WPCOM_FEATURES_WORDADS = 'wordads';
export const WPCOM_FEATURES_CUSTOM_DESIGN = 'custom-design';
export const WPCOM_FEATURES_GLOBAL_STYLES = 'global-styles';
export const WPCOM_FEATURES_SITE_PREVIEW_LINKS = 'site-preview-links';

// Signup flow related features
export const FEATURE_UNLIMITED_EMAILS = 'unlimited-emails';
export const FEATURE_UNLIMITED_SUBSCRIBERS = 'unlimited-subscribers';
export const FEATURE_IMPORT_SUBSCRIBERS = 'import-subscribers';
export const FEATURE_ADD_MULTIPLE_PAGES_NEWSLETTER = 'add-multiple-pages-newsletter';
export const FEATURE_AD_FREE_EXPERIENCE = 'ad-free-experience';
export const FEATURE_COLLECT_PAYMENTS_NEWSLETTER = 'collect-payments-newsletter';
export const FEATURE_POST_BY_EMAIL = 'post-by-email';
export const FEATURE_REAL_TIME_ANALYTICS = 'real-time-analytics';
export const FEATURE_GOOGLE_ANALYTICS_V2 = 'google-analytics-v2';
export const FEATURE_ADD_UNLIMITED_LINKS = 'add-unlimited-links';
export const FEATURE_CUSTOMIZE_THEMES_BUTTONS_COLORS = 'customize-themes-buttons-colors';
export const FEATURE_TRACK_VIEWS_CLICKS = 'track-views-clicks';
export const FEATURE_COLLECT_PAYMENTS_LINK_IN_BIO = 'collect-payments-link-in-bio';
export const FEATURE_NEWSLETTER_IMPORT_SUBSCRIBERS_FREE = 'newsletter-import-subscribers-free';
export const FEATURE_PAYMENT_TRANSACTION_FEES_10 = 'payment-transaction-fees-10';
export const FEATURE_PAYMENT_TRANSACTION_FEES_8 = 'payment-transaction-fees-8';
export const FEATURE_PAYMENT_TRANSACTION_FEES_4 = 'payment-transaction-fees-4';
export const FEATURE_PAYMENT_TRANSACTION_FEES_2 = 'payment-transaction-fees-2';
export const FEATURE_PAYMENT_TRANSACTION_FEES_0 = 'payment-transaction-fees-0';
export const FEATURE_PAYMENT_TRANSACTION_FEES_0_WOO = 'payment-transaction-fees-0-woo';
export const FEATURE_PAYMENT_TRANSACTION_FEES_0_ALL = 'payment-transaction-fees-0-all';
export const FEATURE_PAYMENT_TRANSACTION_FEES_2_REGULAR = 'payment-transaction-fees-2-regular';
export const FEATURE_GROUP_PAYMENT_TRANSACTION_FEES = 'payment-transaction-fees-group';
export const FEATURE_COMMISSION_FEE_STANDARD_FEATURES = 'payment-commission-fee-standard';
export const FEATURE_COMMISSION_FEE_WOO_FEATURES = 'payment-commission-fee-woo';
export const FEATURE_THE_READER = 'the-reader';

// Pricing Grid 2023 Features
export const FEATURE_BEAUTIFUL_THEMES = 'beautiful-themes';
export const FEATURE_PAGES = 'pages-v1';
export const FEATURE_USERS = 'users-v1';
export const FEATURE_NEWSLETTERS_RSS = 'newsletter-rss';
export const FEATURE_POST_EDITS_HISTORY = 'post-edits-history';
export const FEATURE_SECURITY_BRUTE_FORCE = 'security-brute-force';
export const FEATURE_SMART_REDIRECTS = 'smart-redirects';
export const FEATURE_ALWAYS_ONLINE = 'always-online';
export const FEATURE_FAST_DNS = 'fast-dns';
export const FEATURE_STYLE_CUSTOMIZATION = 'style-customization';
export const FEATURE_DESIGN_TOOLS = 'design-tools';
export const FEATURE_WORDADS = 'wordads-v2';
export const FEATURE_PLUGINS_THEMES = 'plugins-themes-v1';
export const FEATURE_BANDWIDTH = 'bandwidth-v1';
export const FEATURE_BURST = 'burst-v1';
export const FEATURE_WAF_V2 = 'waf-v2';
export const FEATURE_CDN = 'cdn-v1';
export const FEATURE_CPUS = 'cpus-v1';
export const FEATURE_DATACENTRE_FAILOVER = 'datacentre-failover';
export const FEATURE_ISOLATED_INFRA = 'isolated-infra';
export const FEATURE_SECURITY_MALWARE = 'security-malware';
export const FEATURE_REAL_TIME_SECURITY_SCANS = 'real-time-security-scans';
export const FEATURE_SECURITY_VULNERABILITY_NOTIFICATIONS = 'security-vulnerability-notifications';
export const FEATURE_SECURITY_DDOS = 'security-ddos';
export const FEATURE_DEV_TOOLS = 'dev-tools';
export const FEATURE_DEV_TOOLS_SSH = 'dev-tools-ssh';
export const FEATURE_DEV_TOOLS_GIT = 'dev-tools-git';
export const FEATURE_WP_UPDATES = 'wp-updates';
export const FEATURE_MULTI_SITE = 'multi-site';
export const FEATURE_SELL_SHIP = 'sell-ship';
export const FEATURE_SELL_INTERNATIONALLY = 'sell-internationally';
export const FEATURE_AUTOMATIC_SALES_TAX = 'automatic-sales-tax';
export const FEATURE_AUTOMATED_BACKUPS_SECURITY_SCAN = 'automated-backups-security-scan';
export const FEATURE_INTEGRATED_SHIPMENT_TRACKING = 'integrated-shipment-tracking';
export const FEATURE_SELL_EGIFTS_AND_VOUCHERS = 'sell-e-gifts-and-vouchers';
export const FEATURE_EMAIL_MARKETING = 'email-marketing';
export const FEATURE_MARKETPLACE_SYNC_SOCIAL_MEDIA_INTEGRATION =
	'marketplace-sync-social-media-integration';
export const FEATURE_BACK_IN_STOCK_NOTIFICATIONS = 'back-in-stock-notifications';
export const FEATURE_MARKETING_AUTOMATION = 'marketing-automation';
export const FEATURE_AUTOMATED_EMAIL_TRIGGERS = 'automated-email-triggers';
export const FEATURE_CART_ABANDONMENT_EMAILS = 'cart-abandonment-emails';
export const FEATURE_REFERRAL_PROGRAMS = 'referral-programs';
export const FEATURE_CUSTOMER_BIRTHDAY_EMAILS = 'customer-birthday-emails';
export const FEATURE_LOYALTY_POINTS_PROGRAMS = 'loyalty-points-programs';
export const FEATURE_OFFER_BULK_DISCOUNTS = 'offer-bulk-discounts';
export const FEATURE_RECOMMEND_ADD_ONS = 'recommend-add-ons';
export const FEATURE_ASSEMBLED_PRODUCTS_AND_KITS = 'assembled-products-and-kits';
export const FEATURE_MIN_MAX_ORDER_QUANTITY = 'min-max-order-quantity';
export const FEATURE_CUSTOM_STORE = 'custom-store';
export const FEATURE_INVENTORY = 'inventory';
export const FEATURE_CHECKOUT = 'checkout-v1';
export const FEATURE_ACCEPT_PAYMENTS_V2 = 'accept-payments-v2';
export const FEATURE_SALES_REPORTS = 'sales-reports';
export const FEATURE_EXTENSIONS = 'extensions-v1';
export const FEATURE_STATS_JP = 'stats-jp';
export const FEATURE_SPAM_JP = 'spam-jp';
export const FEATURE_LTD_SOCIAL_MEDIA_JP = 'ltd-social-media-jp';
export const FEATURE_SHARES_SOCIAL_MEDIA_JP = 'shares-social-media-jp';
export const FEATURE_CONTACT_FORM_JP = 'contact-form-jp';
export const FEATURE_ADVANCED_FORM_FEATURES_JP = 'advanced-form-features-jp';
export const FEATURE_PAID_SUBSCRIBERS_JP = 'paid-subscribers-jp';
export const FEATURE_VIDEOPRESS_JP = 'videopress-jp';
export const FEATURE_UNLTD_SOCIAL_MEDIA_JP = 'unltd-social-media-jp';
export const FEATURE_SEO_JP = 'seo-jp';
export const FEATURE_BRUTE_PROTECT_JP = 'brute-protect-jp';
export const FEATURE_REALTIME_BACKUPS_JP = 'realtime-backups-jp';
export const FEATURE_UPTIME_MONITOR_JP = 'uptime-monitor-jp';
export const FEATURE_GLOBAL_EDGE_CACHING = 'global-edge-caching';
export const FEATURE_ES_SEARCH_JP = 'es-search-jp';
export const FEATURE_PLUGIN_AUTOUPDATE_JP = 'plugin-autoupdate-jp';
export const FEATURE_PREMIUM_CONTENT_JP = 'premium-content-jp';
export const FEATURE_SITE_ACTIVITY_LOG_JP = 'site-activity-log-jp';
export const FEATURE_DONATIONS_AND_TIPS_JP = 'donations-and-tips-jp';
export const FEATURE_PAYPAL_JP = 'payments-paypal-jp';
export const FEATURE_PAYMENT_BUTTONS_JP = 'payment-buttons-jp';
export const FEATURE_AUTOMATTIC_DATACENTER_FAILOVER = 'automattic-datacenter-fail-over';
export const FEATURE_PREMIUM_STORE_THEMES = 'premium-store-themes';
export const FEATURE_WOOCOMMERCE_HOSTING = 'woocommerce-hosting';
export const FEATURE_STORE_DESIGN = 'store-design';
export const FEATURE_UNLIMITED_PRODUCTS = 'unlimited-products';
export const FEATURE_DISPLAY_PRODUCTS_BRAND = 'display-products-brand';
export const FEATURE_PRODUCT_ADD_ONS = 'product-add-ons';
export const FEATURE_ASSEMBLED_KITS = 'assembled-kits';
export const FEATURE_STOCK_NOTIFS = 'stock-notifs';
export const FEATURE_DYNAMIC_UPSELLS = 'dynamic-upsells';
export const FEATURE_CUSTOM_MARKETING_AUTOMATION = 'custom-marketing-automation';
export const FEATURE_BULK_DISCOUNTS = 'bulk-discounts';
export const FEATURE_INVENTORY_MGMT = 'inventory-mgmt';
export const FEATURE_STREAMLINED_CHECKOUT = 'streamlined-checkout';
export const FEATURE_SELL_60_COUNTRIES = 'sell-60-countries';
export const FEATURE_SHIPPING_INTEGRATIONS = 'shipping-integrations';
export const FEATURE_UNLIMITED_TRAFFIC = 'unlimited-traffic';
export const FEATURE_TIERED_STORAGE_PLANS_AVAILABLE = 'tiered-storage-plans-available';
export const FEATURE_FAST_SUPPORT_FROM_EXPERTS = 'fast-support-from-experts';
export const FEATURE_PRIORITY_24_7_SUPPORT = 'priority-24-7-support';
export const FEATURE_SUPPORT = 'support';
export const FEATURE_SUPPORT_FROM_EXPERTS = 'support-from-experts';
export const FEATURE_UPLOAD_VIDEO = 'upload-video';
export const FEATURE_STATS_BASIC_20250206 = 'stats-basic-20250206';
export const FEATURE_STATS_ADVANCED_20250206 = 'stats-advanced-20250206';
export const FEATURE_AI_ASSISTANT = 'ai-assistant';

// Woo Express Features
export const FEATURE_WOOCOMMERCE_STORE = 'woocommerce-store'; // WooCommerce store
export const FEATURE_WOOCOMMERCE_MOBILE_APP = 'woocommerce-mobile-app'; // WooCommerce mobile app
export const FEATURE_WORDPRESS_CMS = 'wordpress-cms'; // WordPress CMS
export const FEATURE_WORDPRESS_MOBILE_APP = 'wordpress-mobile-app'; // WordPress mobile app
export const FEATURE_FREE_SSL_CERTIFICATE = 'free-ssl-certificate'; // Free SSL certificate
export const FEATURE_GOOGLE_ANALYTICS_V3 = 'google-analytics-v3'; // Google Analytics
export const FEATURE_LIST_UNLIMITED_PRODUCTS = 'list-unlimited-products'; // List unlimited products
export const FEATURE_GIFT_CARDS = 'gift-cards'; // Gift cards
export const FEATURE_PRODUCT_BUNDLES = 'product-bundles'; // Product bundles
export const FEATURE_CUSTOM_PRODUCT_KITS = 'custom-product-kits'; // Custom product kits
export const FEATURE_LIST_PRODUCTS_BY_BRAND = 'list-products-by-brand'; // List products by brand
export const FEATURE_PRODUCT_RECOMMENDATIONS = 'product-recommendations'; // Product recommendations
export const FEATURE_INTEGRATED_PAYMENTS = 'integrated-payments'; // Integrated payments
export const FEATURE_INTERNATIONAL_PAYMENTS = 'international-payments'; // International payments
export const FEATURE_AUTOMATED_SALES_TAXES = 'automated-sales-taxes'; // Automated sales taxes
export const FEATURE_ACCEPT_LOCAL_PAYMENTS = 'accept-local-payments'; // Accept local payments
export const FEATURE_PROMOTE_ON_TIKTOK = 'promote-on-tiktok'; // Promote on TikTok
export const FEATURE_SYNC_WITH_PINTEREST = 'sync-with-pinterest'; // Sync with Pinterest
export const FEATURE_CONNECT_WITH_FACEBOOK = 'connect-with-facebook'; // Connect with Facebook
export const FEATURE_ABANDONED_CART_RECOVERY = 'abandoned-cart-recovery'; // Abandoned cart recovery
export const FEATURE_ADVERTISE_ON_GOOGLE = 'advertise-on-google'; // Advertise on Google
export const FEATURE_CUSTOM_ORDER_EMAILS = 'custom-order-emails'; // Custom order emails
export const FEATURE_LIVE_SHIPPING_RATES = 'live-shipping-rates'; // Live shipping rates
export const FEATURE_DISCOUNTED_SHIPPING = 'discounted-shipping'; // Discounted shipping
export const FEATURE_PRINT_SHIPPING_LABELS = 'print-shipping-labels'; // Print shipping labels
export const FEATURE_AI_ASSISTED_PRODUCT_DESCRIPTION = 'ai-assisted-product-descriptions'; // AI-assisted product descriptions

// Sensei Features
export const FEATURE_SENSEI_SUPPORT = 'sensei-support';
export const FEATURE_SENSEI_UNLIMITED = 'sensei-unlimited';
export const FEATURE_SENSEI_INTERACTIVE = 'sensei-interactive';
export const FEATURE_SENSEI_QUIZZES = 'sensei-quizzes';
export const FEATURE_SENSEI_SELL_COURSES = 'sensei-sell-courses';
export const FEATURE_SENSEI_STORAGE = 'sensei-storage';
export const FEATURE_SENSEI_HOSTING = 'sensei-hosting';
export const FEATURE_SENSEI_JETPACK = 'sensei-jetpack';

// Feature types
export const FEATURE_TYPE_JETPACK_ANTI_SPAM = 'jetpack_anti_spam';
export const FEATURE_TYPE_JETPACK_ACTIVITY_LOG = 'jetpack_activity_log';
export const FEATURE_TYPE_JETPACK_BACKUP = 'jetpack_backup';
export const FEATURE_TYPE_JETPACK_BOOST = 'jetpack_boost';
export const FEATURE_TYPE_JETPACK_SCAN = 'jetpack_scan';
export const FEATURE_TYPE_JETPACK_SOCIAL = 'jetpack_social';
export const FEATURE_TYPE_JETPACK_SEARCH = 'jetpack_search';
export const FEATURE_TYPE_JETPACK_STATS = 'jetpack_stats';
export const FEATURE_TYPE_JETPACK_VIDEOPRESS = 'jetpack_videopress';

export const FEATURE_THEMES_PREMIUM_AND_STORE = 'feature-themes-premium-and-store';
export const FEATURE_UNLIMITED_ENTITIES = 'feature-unlimited-entities';
export const FEATURE_WOO_THEMES = 'feature-woo-themes';
export const FEATURE_WOO_SOCIAL_MEDIA_INTEGRATIONS = 'feature-woo-social-media-integrations';
export const FEATURE_WOO_PAYMENTS = 'feature-woo-payments';
export const FEATURE_WOO_SHIPPING_TRACKING = 'feature-woo-shipping-tracking';
export const FEATURE_WOO_TAX_SOLUTIONS = 'feature-woo-tax-solutions';
export const FEATURE_WOO_BRANDS = 'feature-woo-brands';
export const FEATURE_WOO_AUTOMATE = 'feature-woo-automate';
export const FEATURE_GOOGLE_LISTING_ADS = 'feature-google-listing-ads';
export const FEATURE_CONNECT_ANALYTICS = 'feature-connect-analytics';
export const FEATURE_LIMITED_SITE_ACTIVITY_LOG = 'feature-limited-site-activity-log';
export const FEATURE_BIG_SKY_WEBSITE_BUILDER = 'feature-big-sky-website-builder';
export const FEATURE_BIG_SKY_WEBSITE_BUILDER_CHECKOUT = 'feature-big-sky-website-builder-checkout';
