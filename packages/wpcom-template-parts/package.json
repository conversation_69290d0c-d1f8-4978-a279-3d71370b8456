{"name": "@automattic/wpcom-template-parts", "version": "1.0.0", "description": "WordPress.com template parts to be used everywhere.", "homepage": "https://github.com/Automattic/wp-calypso", "license": "GPL-2.0-or-later", "author": "Automattic Inc.", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "calypso:src": "src/index.ts", "exports": {".": {"calypso:src": "./src/index.ts", "types": "./dist/types/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "sideEffects": ["*.css", "*.scss"], "repository": {"type": "git", "url": "git+https://github.com/Automattic/wp-calypso.git", "directory": "packages/wpcom-template-parts"}, "publishConfig": {"access": "public"}, "private": true, "bugs": "https://github.com/Automattic/wp-calypso/issues", "types": "dist/types", "scripts": {"clean": "tsc --build ./tsconfig.json ./tsconfig-cjs.json --clean && rm -rf dist", "build": "tsc --build ./tsconfig.json ./tsconfig-cjs.json", "prepack": "yarn run clean && yarn run build", "watch": "tsc --build ./tsconfig.json --watch"}, "dependencies": {"@automattic/color-studio": "^4.1.0", "@automattic/components": "workspace:^", "@automattic/i18n-utils": "workspace:^", "@wordpress/url": "^4.23.0", "i18n-calypso": "workspace:^", "social-logos": "^3.1.18"}, "devDependencies": {"@automattic/calypso-typescript-config": "workspace:^", "typescript": "^5.8.3"}, "peerDependencies": {"@wordpress/data": "^10.23.0", "react": "^18.3.1", "react-dom": "^18.3.1"}}