{"name": "@automattic/explat-client-react-helpers", "version": "0.1.1", "description": "Standalone ExPlat Client: React Helpers.", "bugs": "https://github.com/Automattic/wp-calypso/issues", "homepage": "https://github.com/Automattic/wp-calypso", "author": "Automattic Inc.", "license": "GPL-2.0-or-later", "main": "dist/cjs/index.js", "browser": "dist/esm/index.js", "module": "dist/esm/index.js", "calypso:src": "src/index.tsx", "types": "dist/types/index.d.ts", "exports": {".": {"calypso:src": "./src/index.tsx", "types": "./dist/types/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "sideEffects": false, "repository": {"type": "git", "url": "git+https://github.com/Automattic/wp-calypso.git", "directory": "packages/explat-client-react-helpers"}, "scripts": {"clean": "tsc --build ./tsconfig.json ./tsconfig-cjs.json --clean && rm -rf dist", "build": "tsc --build ./tsconfig.json ./tsconfig-cjs.json", "watch": "tsc --build ./tsconfig.json --watch", "prepack": "yarn run clean && yarn run build", "test": "yarn jest"}, "dependencies": {"@automattic/explat-client": "workspace:^", "react": "^18.3.1", "tslib": ">=2.8.1"}, "devDependencies": {"@automattic/calypso-typescript-config": "workspace:^", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "jest": "^29.7.0", "react-dom": "^18.3.1", "typescript": "^5.8.3"}}