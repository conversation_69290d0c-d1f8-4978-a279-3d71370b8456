# Changelog

## 0.1.1

- Remove console.log and bump dependency version

## 0.1.0

- Fix bug with late loading of initial render of useExperiment

## 0.0.6

- Bump explat-client version to latest that includes a fix for the experiment name validation to match server side validation: [a-z0-9_]*

## 0.0.5

- Loosen the requirements around dependencies

## 0.0.4

- Added ExperimentOptions and ExPlatClientReactHelpers Typescript interfaces to export

## 0.0.3

- Update dependencies

## 0.0.2

- Add isEligibility option

## 0.0.1

Initial release.
See pbmo2S-HC-p2
