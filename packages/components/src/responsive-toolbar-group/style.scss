@use '@automattic/typography/styles/variables';

.responsive-toolbar-group__dropdown .responsive-toolbar-group__button-item,
.responsive-toolbar-group__dropdown .responsive-toolbar-group__menu-item,
.responsive-toolbar-group__swipe .responsive-toolbar-group__swipe-item {
	height: 36px;
	padding: 8px 12px;
	border-radius: 4px;
	line-height: 20px;

	&::before {
		left: 4px;
		right: 4px;
		height: 36px;
		border-radius: 4px;
		background-color: var(--studio-white);
	}
}

.responsive-toolbar-group__grouped-list,
.responsive-toolbar-group__swipe-list {
	align-items: center;
}

.responsive-toolbar-group__dropdown {
	position: relative;
	padding-bottom: 24px;

	.responsive-toolbar-group__grouped-list {
		background-color: transparent;
		border-color: transparent;
		justify-content: space-between;
		flex-wrap: nowrap;
		display: flex;
		opacity: 0;

		.responsive-toolbar-group__menu-item {
			font-size: variables.rem(13px);

			&.is-selected {
				color: var(--color-text-inverted);

				&::before {
					background-color: #1e1e1e;
				}
			}

			&:not(.is-selected):not(.responsive-toolbar-group__more-item) {
				&:hover::before {
					background: var(--studio-gray-0);
				}
			}

			&:not(:first-child) {
				margin-top: 8px;
			}
		}

		// Stretch selected item border to page border
		>div:first-of-type {
			margin-left: -8px;
		}
		>div:last-of-type {
			margin-right: -8px;
		}

		.responsive-toolbar-group__button-item:not([class*="is-pressed"]) {
			&:hover::before {
				background: var(--studio-gray-0);
			}
		}

		.responsive-toolbar-group__button-item.is-pressed:hover {
			color: var(--color-text-inverted);
		}
	}

	.is-visible {
		opacity: 1;
	}

	.responsive-toolbar-group__full-list {
		overflow: hidden;
		position: absolute;
		height: 0;
		visibility: hidden;
		top: 0;
		left: 0;
		right: 0;
	}

	.responsive-toolbar-group__button-item {
		flex-basis: max-content;
		white-space: nowrap;

		font-size: 0.875rem;
	}
}

.responsive-toolbar-group__swipe {
	width: calc(100% + 20px);

	.responsive-toolbar-group__swipe-list {
		padding: 0 8px;
		display: flex;
		flex-wrap: nowrap;
		overflow-x: scroll;
		overflow-y: hidden;
		border: none;

		// hides the scrollbar
		scrollbar-width: none; // hides the scrollbar in firefox

		&::-webkit-scrollbar { // hides the scrollbar in webkit browsers
			display: none;
		}

		.responsive-toolbar-group__swipe-item {
			font-size: 0.875rem;
		}

		// Core override - prevent buttons from wordwrapping content
		>div {
			flex-shrink: 0;
		}
	}
}
