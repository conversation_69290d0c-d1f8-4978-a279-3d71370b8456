@use '@wordpress/base-styles/colors';

.domain-suggestion-cta {
	justify-content: center !important;

	&.is-secondary {
		color: var( --domain-search-secondary-action-color );
		box-shadow: inset 0 0 0 1px var( --domain-search-secondary-action-box-shadow-color );
	}

	&:disabled:not( .is-primary ):not( .is-pressed ) {
		box-shadow: 0 0 0 1px rgba( 0, 0, 0, 0.1 ) !important;
		color: rgba( 0, 0, 0, 0.1 ) !important;
	}
}

.domain-suggestion-cta--continue {
	&:disabled {
		background: var( --wp-components-color-foreground, #1e1e1e ) !important;
		color: rgba( 255, 255, 255, 0.3 ) !important;
	}
}

.domain-suggestion-cta--error {
	flex: 1;
}

.domain-suggestion-cta-error {
	margin-top: auto;
	display: flex;
}

.domain-suggestion-cta-error__tooltip {
	max-width: calc( var( --popover-available-width ) - 64px );
}
