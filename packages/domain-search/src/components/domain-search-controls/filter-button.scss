.domain-search-controls__filters {
	position: relative;
	flex-shrink: 0;

	.components-button {
		background: #fff;
		height: var( --domain-search-input-size );
		width: var( --domain-search-input-size );
		box-shadow: inset 0 0 0 1px var( --domain-search-secondary-action-box-shadow-color );
		color: var( --domain-search-secondary-action-color );
	}

	&-count {
		display: flex;
		height: 1.5rem;
		min-width: 1.5rem;
		position: absolute;
		pointer-events: none;
		top: 0;
		right: 0;
		transform: translate( 50%, -50% );
		background: var( --wp-admin-theme-color );
		border: solid 2px var( --domain-search-v2-background-color );
		border-radius: 50%;
		color: #fff;
		font-size: 0.75rem;
		align-items: center;
		justify-content: center;
	}
}
