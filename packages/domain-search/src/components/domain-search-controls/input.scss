@import '@wordpress/base-styles/colors';

.domain-search-controls__input {
	width: 100%;

	@supports ( -moz-appearance: none ) {
		// Force hardware acceleration and sub-pixel alignment on Firefox.
		transform: translateZ( 0 );
		will-change: transform;
	}

	.components-input-base {
		border-radius: var( --domain-search-input-border-radius );
	}

	.components-input-control__container {
		box-shadow: inset 0 0 0 1px #{$gray-600};
		background: #fff;

		input.components-input-control__input {
			min-height: var( --domain-search-input-size );
			font-size: 1.25rem;
			padding-left: 1rem;
		}

		.components-input-control__suffix > div {
			padding-inline-end: 1rem;
		}
	}
}
