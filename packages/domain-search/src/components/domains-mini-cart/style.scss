@import '@wordpress/base-styles/breakpoints';
@import '@wordpress/base-styles/mixins';

.domains-mini-cart__cushion {
	margin-top: 36px;
	height: var( --domain-search-mini-cart-height );
}

.domains-mini-cart__container {
	position: fixed !important;
	left: 0;
	right: 0;
	bottom: 0;
	height: var( --domain-search-mini-cart-height );
}

.domains-mini-cart {
	justify-content: center;
	align-items: center;
	padding-inline: 1rem;
	display: flex;
	box-sizing: border-box;
	height: 100%;
}

.domains-mini-cart__content {
	box-sizing: border-box;
	width: 100%;
	max-width: 64rem;
}

.domains-mini-cart-actions__view-cart {
	// We need to use !important here because WPCOM adds overrides to the button with a very high specificity.
	--wp-components-color-accent: var( --domain-search-secondary-action-color ) !important;
	--wp-components-color-accent-darker-20: var( --domain-search-secondary-action-color ) !important;
	text-decoration: underline;
}

.domains-mini-cart-summary {
	padding: 0;
}

.domains-mini-cart-summary__total {
	font-size: 1.125rem !important; /* stylelint-disable-line scales/font-sizes */
}
