{"name": "@automattic/create-calypso-config", "version": "1.0.0-alpha.0", "description": "Creates the calypso configuration api.", "homepage": "https://github.com/Automattic/wp-calypso", "license": "GPL-2.0-or-later", "author": "Automattic Inc.", "sideEffects": true, "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "calypso:src": "src/index.ts", "exports": {".": {"calypso:src": "./src/index.ts", "types": "./dist/types/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "repository": {"type": "git", "url": "git+https://github.com/Automattic/wp-calypso.git", "directory": "packages/create-calypso-config"}, "files": ["dist", "src"], "types": "dist/types", "publishConfig": {"access": "public"}, "bugs": "https://github.com/Automattic/wp-calypso/issues", "scripts": {"clean": "tsc --build ./tsconfig.json ./tsconfig-cjs.json --clean && rm -rf dist", "build": "tsc --build ./tsconfig.json ./tsconfig-cjs.json", "prepare": "yarn run build", "prepack": "yarn run clean && yarn run build", "watch": "tsc --build ./tsconfig.json --watch"}, "dependencies": {"cookie": "^0.7.2", "tslib": "^2.8.1"}, "devDependencies": {"@automattic/calypso-typescript-config": "workspace:^", "typescript": "^5.8.3"}}