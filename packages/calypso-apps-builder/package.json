{"name": "@automattic/calypso-apps-builder", "author": "Automattic Inc.", "version": "1.0.0", "description": "This package is used to build calypso apps (anything in /apps).", "type": "module", "main": "index.js", "bin": "./index.js", "license": "GPL-2.0-or-later", "homepage": "https://github.com/Automattic/wp-calypso", "bugs": "https://github.com/Automattic/wp-calypso/issues", "repository": {"type": "git", "url": "https://github.com/Automattic/wp-calypso.git", "directory": "packages/calypso-apps-builder"}, "dependencies": {"chokidar": "^3.6.0", "html-webpack-plugin": "^5.6.3", "npm-run-all": "^4.1.5", "tree-kill": "^1.2.2", "webpack": "^5.99.8", "yargs": "^17.0.1"}, "devDependencies": {"@automattic/calypso-eslint-overrides": "workspace:^", "@automattic/calypso-typescript-config": "workspace:^"}}