{"name": "@automattic/webpack-config-flag-plugin", "version": "1.0.0", "description": "A webpack plugin to replace `config.isEnabled` flag checks with literals.", "homepage": "https://github.com/Automattic/wp-calypso", "bugs": "https://github.com/Automattic/wp-calypso/issues", "main": "index.js", "peerDependencies": {"webpack": "^5.99.8"}, "author": "Automattic Inc.", "private": true, "repository": {"type": "git", "url": "git+https://github.com/Automattic/wp-calypso.git", "directory": "packages/webpack-config-flag-plugin"}, "keywords": [], "license": "GPL-2.0-or-later", "publishConfig": {"access": "public"}, "devDependencies": {"@automattic/calypso-typescript-config": "workspace:^", "rimraf": "^3.0.2"}}