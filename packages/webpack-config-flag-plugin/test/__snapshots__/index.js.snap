// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`webpack-config-flag-plugin should produce expected output: Output bundle should match snapshot 1`] = `
"\\"use strict\\";
(() => {
var exports = {};
exports.id = \\"main\\";
exports.ids = [\\"main\\"];
exports.modules = {

/***/ \\"./main.js\\":
/***/ ((__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) => {


// NAMESPACE OBJECT: ./default-import/config/index.js
var default_import_config_namespaceObject = {};
__webpack_require__.r(default_import_config_namespaceObject);

// NAMESPACE OBJECT: ./named-import/config/index.js
var named_import_config_namespaceObject = {};
__webpack_require__.r(named_import_config_namespaceObject);

// NAMESPACE OBJECT: ./namespace-import/config/index.js
var namespace_import_config_namespaceObject = {};
__webpack_require__.r(namespace_import_config_namespaceObject);

;// ./config.js
function isEnabled( flag ) {
	return 'argh';
}

/* harmony default export */ const config = ({ isEnabled });

;// ./default-import/config/function.js


function fn() {
	// Should be replaced with true
	if ( true ) {
	}
}

;// ./default-import/config/module.js


// Should be replaced with true
if ( true ) {
}

;// ./default-import/config/rename.js


// Should be replaced with true
if ( true ) {
}

;// ./default-import/config/shadowed-by-param.js


function shadowed_by_param_fn( config ) {
	// Should NOT be replaced with true
	if ( config.isEnabled( 'foo' ) ) {
	}
}

;// ./default-import/config/shadowed-by-var.js


function shadowed_by_var_fn() {
	const config = { isEnabled: () => false };
	// Should NOT be replaced with true
	if ( config.isEnabled( 'foo' ) ) {
	}
}

;// ./default-import/config/wrong-flag.js


// Should NOT be replaced with true.
if ( config.isEnabled( 'bar' ) ) {
}

;// ./default-import/config/wrong-path.js


// Should NOT be replaced with true
if ( default_import_config_namespaceObject[\\"default\\"].isEnabled( 'foo' ) ) {
}

;// ./default-import/config/index.js








;// ./default-import/calypso-config/function.js


function function_fn() {
	// Should be replaced with true
	if ( true ) {
	}
}

;// ./default-import/calypso-config/module.js


// Should be replaced with true
if ( true ) {
}

;// ./default-import/calypso-config/rename.js


// Should be replaced with true
if ( true ) {
}

;// ./default-import/calypso-config/shadowed-by-param.js


function calypso_config_shadowed_by_param_fn( config ) {
	// Should NOT be replaced with true
	if ( config.isEnabled( 'foo' ) ) {
	}
}

;// ./default-import/calypso-config/shadowed-by-var.js


function calypso_config_shadowed_by_var_fn() {
	const config = { isEnabled: () => false };
	// Should NOT be replaced with true
	if ( config.isEnabled( 'foo' ) ) {
	}
}

;// ./default-import/calypso-config/wrong-flag.js


// Should NOT be replaced with true.
if ( config.isEnabled( 'bar' ) ) {
}

;// ./default-import/calypso-config/index.js







;// ./default-import/index.js



;// ./named-import/config/function.js


function config_function_fn() {
	// Should be replaced with true
	if ( true ) {
	}
}

;// ./named-import/config/module.js


// Should be replaced with true
if ( true ) {
}

;// ./named-import/config/rename.js


// Should be replaced with true
if ( true ) {
}

;// ./named-import/config/shadowed-by-param.js


function config_shadowed_by_param_fn( isEnabled ) {
	// Should NOT be replaced with true
	if ( isEnabled( 'foo' ) ) {
	}
}

;// ./named-import/config/shadowed-by-var.js


function config_shadowed_by_var_fn() {
	const isEnabled = () => false;
	// Should NOT be replaced with true
	if ( isEnabled( 'foo' ) ) {
	}
}

;// ./named-import/config/wrong-flag.js


// Should NOT be replaced with true.
if ( isEnabled( 'bar' ) ) {
}

;// ./named-import/config/wrong-path.js
// eslint-disable-next-line import/named


// Should NOT be replaced with true
if ( (0,named_import_config_namespaceObject.isEnabled)( 'foo' ) ) {
}

;// ./named-import/config/index.js








;// ./named-import/calypso-config/function.js


function calypso_config_function_fn() {
	// Should be replaced with true
	if ( true ) {
	}
}

;// ./named-import/calypso-config/module.js


// Should be replaced with true
if ( true ) {
}

;// ./named-import/calypso-config/rename.js


// Should be replaced with true
if ( true ) {
}

;// ./named-import/calypso-config/shadowed-by-param.js


function named_import_calypso_config_shadowed_by_param_fn( isEnabled ) {
	// Should NOT be replaced with true
	if ( isEnabled( 'foo' ) ) {
	}
}

;// ./named-import/calypso-config/shadowed-by-var.js


function named_import_calypso_config_shadowed_by_var_fn() {
	const isEnabled = () => false;
	// Should NOT be replaced with true
	if ( isEnabled( 'foo' ) ) {
	}
}

;// ./named-import/calypso-config/wrong-flag.js


// Should NOT be replaced with true.
if ( isEnabled( 'bar' ) ) {
}

;// ./named-import/calypso-config/index.js







;// ./named-import/index.js



;// ./namespace-import/config/function.js


function namespace_import_config_function_fn() {
	// Should be replaced with true
	if ( true ) {
	}
}

;// ./namespace-import/config/module.js


// Should be replaced with true
if ( true ) {
}

;// ./namespace-import/config/rename.js


// Should be replaced with true
if ( true ) {
}

;// ./namespace-import/config/shadowed-by-param.js


function namespace_import_config_shadowed_by_param_fn( config ) {
	// Should NOT be replaced with true
	if ( config.isEnabled( 'foo' ) ) {
	}
}

;// ./namespace-import/config/shadowed-by-var.js


function namespace_import_config_shadowed_by_var_fn() {
	const config = { isEnabled: () => false };
	// Should NOT be replaced with true
	if ( config.isEnabled( 'foo' ) ) {
	}
}

;// ./namespace-import/config/wrong-flag.js


// Should NOT be replaced with true.
if ( isEnabled( 'bar' ) ) {
}

;// ./namespace-import/config/wrong-path.js
/* eslint-disable import/namespace */


// Should NOT be replaced with true
if ( namespace_import_config_namespaceObject.isEnabled( 'foo' ) ) {
}

;// ./namespace-import/config/index.js








;// ./namespace-import/calypso-config/function.js


function namespace_import_calypso_config_function_fn() {
	// Should be replaced with true
	if ( true ) {
	}
}

;// ./namespace-import/calypso-config/module.js


// Should be replaced with true
if ( true ) {
}

;// ./namespace-import/calypso-config/rename.js


// Should be replaced with true
if ( true ) {
}

;// ./namespace-import/calypso-config/shadowed-by-param.js


function namespace_import_calypso_config_shadowed_by_param_fn( config ) {
	// Should NOT be replaced with true
	if ( config.isEnabled( 'foo' ) ) {
	}
}

;// ./namespace-import/calypso-config/shadowed-by-var.js


function namespace_import_calypso_config_shadowed_by_var_fn() {
	const config = { isEnabled: () => false };
	// Should NOT be replaced with true
	if ( config.isEnabled( 'foo' ) ) {
	}
}

;// ./namespace-import/calypso-config/wrong-flag.js


// Should NOT be replaced with true.
if ( isEnabled( 'bar' ) ) {
}

;// ./namespace-import/calypso-config/index.js







;// ./namespace-import/index.js



;// ./main.js





/***/ })

};
;

// load runtime
var __webpack_require__ = require(\\"./runtime~main.js\\");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__(\\"./main.js\\"));

})();"
`;
