{"name": "@automattic/effective-module-tree", "version": "0.1.0", "description": "A tool to generate the effective list of dependencies of any project.", "main": "index.js", "author": "Automattic Inc.", "homepage": "https://github.com/Automattic/wp-calypso", "bugs": "https://github.com/Automattic/wp-calypso/issues", "repository": {"type": "git", "url": "git+https://github.com/Automattic/wp-calypso.git", "directory": "packages/effective-module-tree"}, "keywords": ["dependencies", "node_modules"], "license": "GPL-2.0-or-later", "bin": "./cli.js", "dependencies": {"debug": "^4.4.1", "object-treeify": "^1.1.33", "yargs": "^17.0.1"}, "devDependencies": {"@automattic/calypso-eslint-overrides": "workspace:^", "@automattic/calypso-typescript-config": "workspace:^"}}