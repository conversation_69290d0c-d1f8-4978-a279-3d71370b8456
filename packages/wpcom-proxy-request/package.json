{"name": "wpcom-proxy-request", "version": "7.0.6", "description": "Proxied cookie-authenticated REST API requests to WordPress.com.", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "calypso:src": "src/index.js", "exports": {".": {"calypso:src": "./src/index.js", "types": "./types/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "sideEffects": false, "keywords": ["browser", "wpcom", "wordpress", "rest", "api", "cookie"], "author": "Automattic Inc.", "license": "MIT", "homepage": "https://github.com/Automattic/wp-calypso", "repository": {"type": "git", "url": "git+https://github.com/Automattic/wp-calypso.git", "directory": "packages/wpcom-proxy-request"}, "publishConfig": {"access": "public"}, "bugs": "https://github.com/Automattic/wp-calypso/issues", "files": ["dist", "types", "History.md", "README.md"], "types": "types", "scripts": {"clean": "rm -rf dist", "build": "transpile", "prepack": "yarn run clean && yarn run build"}, "dependencies": {"debug": "^4.4.1", "wp-error": "^1.3.0"}, "devDependencies": {"@automattic/calypso-build": "workspace:^", "@automattic/calypso-eslint-overrides": "workspace:^", "@automattic/calypso-typescript-config": "workspace:^", "postcss": "^8.5.3", "webpack": "^5.99.8"}}