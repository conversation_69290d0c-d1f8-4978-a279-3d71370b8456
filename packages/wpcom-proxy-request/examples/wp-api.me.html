<!DOCTYPE html>
<html>
	<head>
		<title>WP-API users `Me` request</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	</head>
	<body>
		<script src="dist/wpcom-proxy-request.js"></script>
		<script>
			var output = '';

			WPCOMProxyRequest( {
				metaAPI: { accessAllUsersBlogs: true }
			}, function( err ) {
				if ( err ) {
					throw err;
				}

				console.log( 'proxy now running in "access all user\'s blogs" mode' );
			} );

			WPCOMProxyRequest( {
				path: '/sites/en.blog.wordpress.com/users/me',
				apiNamespace: 'wp/v2'
			}, function( err, res ) {
				if ( err ) {
					return console.error( err );
				}

				console.log( res );

				output = document.createElement( 'pre' );
				output.innerHTML = JSON.stringify( res, null, 2 );
				document.body.appendChild( output );
			} );

		</script>
	</body>
</html>
