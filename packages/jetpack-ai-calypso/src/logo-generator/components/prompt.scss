.jetpack-ai-logo-generator__prompt {
	display: flex;
	flex-direction: column;
	gap: 8px;
	font-size: $font-body-small;
}

.jetpack-ai-logo-generator__prompt-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	align-self: stretch;

	.jetpack-ai-logo-generator__prompt-label {
		font-weight: 500;
	}

	.jetpack-ai-logo-generator__prompt-actions {
		display: flex;
		font-size: $font-body-extra-small;
		line-height: 20px;
		gap: 16px;

		.jetpack-ai-logo-generator-icon {
			margin-right: 4px;

			// tricky SVG
			path {
				fill: currentColor
			};
		}
	}
}

.jetpack-ai-logo-generator__prompt-query {
	display: flex;
	padding: 8px 8px 8px var(--grid-unit-15, 16px);
	justify-content: space-between;
	align-items: flex-end;
	align-self: stretch;
	border-radius: calc(4px * 2);
	border: 1px solid var(--studio-gray-10, #ccc);
	background: var(--studio-white, #fff);
	gap: 8px;

	@media (min-width: 700px) {
		gap: 48px;
	}

	.prompt-query__input {
		border: 0;
		resize: none;
		flex-grow: 1;
		padding: 6px 0;
		vertical-align: baseline;
		color: var(--studio-gray-100);
		line-height: 1.6;
		word-break: break-word;

		&:focus,
		&:active {
			outline: 0;
		}

		&[contentEditable="false"] {
			color: var(--studio-gray-50, #646970);
		}

		&[data-placeholder]:empty::before {
			content: attr(data-placeholder);
			color: var(--studio-gray-50, #646970);
		}

		&[data-placeholder]:empty:focus::before {
			content: "";
		}
	}
}

.jetpack-ai-logo-generator__prompt-footer {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.jetpack-ai-logo-generator__prompt-requests {
	color: var(--studio-gray-50, #646970);
	font-size: $font-body-extra-small;
	line-height: 21px;
	display: flex;

	& .prompt-footer__icon {
		height: 20px;
		width: 20px;

		path {
			fill: var(--studio-gray-20, #a7aaad);
		}
	}
}

.jetpack-ai-logo-generator__prompt-error {
	color: var(--studio-red-50, #d63638);
	font-size: $font-body-extra-small;
	line-height: 21px;
}
