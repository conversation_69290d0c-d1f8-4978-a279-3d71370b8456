{"name": "@automattic/jetpack-ai-calypso", "version": "2.0.0", "description": "Jetpack AI utilities available to Calypso.", "calypso:src": "src/index.ts", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "exports": {".": {"calypso:src": "./src/index.ts", "types": "./dist/types/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "sideEffects": ["*.css", "*.scss"], "keywords": ["wordpress", "jetpack", "ai", "utilities"], "author": "Automattic Inc.", "contributors": [], "homepage": "https://github.com/Automattic/wp-calypso", "license": "GPL-2.0-or-later", "repository": {"type": "git", "url": "git+https://github.com/Automattic/wp-calypso.git", "directory": "packages/jetpack-ai-calypso"}, "publishConfig": {"access": "public"}, "bugs": {"url": "https://github.com/Automattic/wp-calypso/issues"}, "files": ["dist", "src"], "types": "dist/types", "scripts": {"clean": "tsc --build ./tsconfig.json ./tsconfig-cjs.json --clean && rm -rf dist", "build": "tsc --build ./tsconfig.json ./tsconfig-cjs.json && copy-assets", "prepare": "yarn run build", "prepack": "yarn run clean && yarn run build", "watch": "tsc --build ./tsconfig.json --watch"}, "devDependencies": {"@automattic/calypso-build": "workspace:^", "@automattic/calypso-typescript-config": "workspace:^", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "postcss": "^8.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "typescript": "^5.8.3", "webpack": "^5.99.8"}, "peerDependencies": {"@babel/runtime": "^7.27.1", "@wordpress/data": "^10.23.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "peerDependenciesMeta": {"@babel/runtime": {"optional": true}}, "dependencies": {"@automattic/calypso-analytics": "workspace:^", "@automattic/data-stores": "workspace:^", "@automattic/i18n-utils": "workspace:^", "@automattic/languages": "workspace:^", "clsx": "^2.1.1", "debug": "^4.4.1"}}