{"name": "@automattic/babel-plugin-preserve-i18n", "version": "1.0.0", "description": "A Babel plugin to preserves translation functions even when minified.", "main": "src/index.js", "homepage": "https://github.com/Automattic/wp-calypso", "bugs": "https://github.com/Automattic/wp-calypso/issues", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/Automattic/wp-calypso.git", "directory": "packages/babel-plugin-preserve-i18n"}, "keywords": [], "author": "Automattic Inc.", "license": "GPL-2.0-or-later", "devDependencies": {"@automattic/calypso-eslint-overrides": "workspace:^", "@automattic/calypso-typescript-config": "workspace:^"}}