module.exports = {
	'i18n-ellipsis': require( './i18n-ellipsis' ),
	'i18n-mismatched-placeholders': require( './i18n-mismatched-placeholders' ),
	'i18n-named-placeholders': require( './i18n-named-placeholders' ),
	'i18n-no-collapsible-whitespace': require( './i18n-no-collapsible-whitespace' ),
	'i18n-no-placeholders-only': require( './i18n-no-placeholders-only' ),
	'i18n-no-this-translate': require( './i18n-no-this-translate' ),
	'i18n-no-variables': require( './i18n-no-variables' ),
	'i18n-translate-identifier': require( './i18n-translate-identifier' ),
	'i18n-unlocalized-url': require( './i18n-unlocalized-url' ),
	'jsx-classname-namespace': require( './jsx-classname-namespace' ),
	'jsx-gridicon-size': require( './jsx-gridicon-size' ),
	'post-message-no-wildcard-targets': require( './post-message-no-wildcard-targets' ),
	'redux-no-bound-selectors': require( './redux-no-bound-selectors' ),
	'no-unsafe-wp-apis': require( './no-unsafe-wp-apis' ),
};
