{"name": "eslint-plugin-wpcalypso", "version": "8.0.0", "description": "Custom ESLint rules for the WordPress.com Calypso project.", "repository": {"type": "git", "url": "https://github.com/Automattic/wp-calypso.git", "directory": "packages/eslint-plugin-wpcalypso"}, "homepage": "https://github.com/Automattic/wp-calypso", "bugs": "https://github.com/Automattic/wp-calypso/issues", "license": "GPL-2.0-or-later", "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "author": "Automattic Inc.", "main": "lib/index.js", "files": ["lib/"], "engines": {"node": ">=v22.9.0"}, "peerDependencies": {"@babel/core": ">=7.27.1", "eslint": ">=8.57.1", "eslint-plugin-inclusive-language": "^2.2.1", "eslint-plugin-jsdoc": "^46.10.1", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.3.0"}, "peerDependenciesMeta": {"eslint-plugin-react": {"optional": true}, "eslint-plugin-react-hooks": {"optional": true}}, "devDependencies": {"@automattic/calypso-typescript-config": "workspace:^", "@babel/core": "^7.27.1", "@babel/eslint-parser": "^7.27.1", "@babel/parser": "^7.27.2", "eslint": "^8.57.1"}}