{"name": "@automattic/browser-data-collector", "version": "3.0.0", "description": "A tool to collect data from different browser APIs.", "homepage": "https://github.com/Automattic/wp-calypso", "license": "GPL-2.0-or-later", "author": "Automattic Inc.", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "calypso:src": "src/index.ts", "exports": {".": {"calypso:src": "./src/index.ts", "types": "./dist/types/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "repository": {"type": "git", "url": "git+https://github.com/Automattic/wp-calypso.git", "directory": "packages/browser-data-collector"}, "types": "dist/types", "bugs": "https://github.com/Automattic/wp-calypso/issues", "scripts": {"clean": "tsc --build ./tsconfig.json ./tsconfig-cjs.json --clean && rm -rf dist", "build": "tsc --build ./tsconfig.json ./tsconfig-cjs.json", "prepack": "yarn run clean && yarn run build", "watch": "tsc --build ./tsconfig.json --watch"}, "dependencies": {"tslib": "^2.8.1", "wpcom-proxy-request": "workspace:^"}, "devDependencies": {"@automattic/calypso-typescript-config": "workspace:^", "typescript": "^5.8.3"}}