{"name": "@automattic/babel-plugin-transform-wpcalypso-async", "version": "2.0.0", "license": "GPL-2.0-or-later", "author": "Automattic Inc.", "description": "A Babel plugin to facilitate optional code-splitting by applying transformations to a `asyncRequire` global function or the `<AsyncLoad />` React component.", "homepage": "https://github.com/Automattic/wp-calypso", "bugs": "https://github.com/Automattic/wp-calypso/issues", "repository": {"type": "git", "url": "git://github.com/Automattic/wp-calypso.git", "directory": "packages/babel-plugin-transform-wpcalypso-async"}, "dependencies": {"lodash": "^4.17.21"}, "devDependencies": {"@automattic/calypso-typescript-config": "workspace:^", "@babel/core": "^7.27.1"}, "publishConfig": {"access": "public"}, "files": ["index.js"]}