.color-scheme.is-fresh {
	/* Theme Properties */
	--color-primary: var(--color-deprecated-blue-50);
	--color-primary-dark: var(--color-deprecated-blue-70);
	--color-primary-light: var(--color-deprecated-blue-30);
	--color-primary-0: var(--color-deprecated-blue-0);
	--color-primary-5: var(--color-deprecated-blue-5);
	--color-primary-10: var(--color-deprecated-blue-10);
	--color-primary-20: var(--color-deprecated-blue-20);
	--color-primary-30: var(--color-deprecated-blue-30);
	--color-primary-40: var(--color-deprecated-blue-40);
	--color-primary-50: var(--color-deprecated-blue-50);
	--color-primary-60: var(--color-deprecated-blue-60);
	--color-primary-70: var(--color-deprecated-blue-70);
	--color-primary-80: var(--color-deprecated-blue-80);
	--color-primary-90: var(--color-deprecated-blue-90);
	--color-primary-100: var(--color-deprecated-blue-100);
	--color-accent: var(--color-deprecated-blue-50);
	--color-accent-dark: var(--color-deprecated-blue-70);
	--color-accent-light: var(--color-deprecated-blue-30);
	--color-accent-0: var(--color-deprecated-blue-0);
	--color-accent-5: var(--color-deprecated-blue-5);
	--color-accent-10: var(--color-deprecated-blue-10);
	--color-accent-20: var(--color-deprecated-blue-20);
	--color-accent-30: var(--color-deprecated-blue-30);
	--color-accent-40: var(--color-deprecated-blue-40);
	--color-accent-50: var(--color-deprecated-blue-50);
	--color-accent-60: var(--color-deprecated-blue-60);
	--color-accent-70: var(--color-deprecated-blue-70);
	--color-accent-80: var(--color-deprecated-blue-80);
	--color-accent-90: var(--color-deprecated-blue-90);
	--color-accent-100: var(--color-deprecated-blue-100);
	--color-wp-admin-button-background: #008ec2;
	--color-wp-admin-button-border: #006799;

	/* WP Admin Default Theme */
	--theme-text-color: #f0f0f1; /* Direct from wp-admin */
	--theme-base-color: #1d2327; /* Direct from wp-admin */
	--theme-submenu-background-color: #131619; /* $menu-submenu-background */
	--theme-submenu-text-color: #c3c4c7; /* $menu-submenu-text */
	--theme-icon-color: rgba(240, 246, 252, 0.6); /* $icon-color */
	--theme-highlight-color: #72aee6; /* $highlight-color */
	--theme-notification-color: #d54e21; /* Direct from wp-admin */

	/* Masterbar */
	--color-masterbar-background: var(--theme-base-color);
	--color-masterbar-border: #8c8f94; /* $adminbar-avatar-frame */
	--color-masterbar-text: var(--theme-text-color);
	--color-masterbar-submenu-text: var(--theme-submenu-text-color);
	--color-masterbar-icon: var(--theme-icon-color);
	--color-masterbar-highlight: var(--theme-highlight-color);
	--color-masterbar-unread-dot-background: var(--color-accent-20);

	--color-masterbar-item-hover-background: #2c3338; /* $menu-submenu-background: darken( $base-color, 7% ) */
	--color-masterbar-item-active-background: #23282d;
	--color-masterbar-item-new-editor-background: var(--studio-gray-50);
	--color-masterbar-item-new-editor-hover-background: var(--studio-gray-40);

	/* Sidebar */
	--color-sidebar-background: #23282d; /* theme-default */
	--color-sidebar-border: #333; /* theme-default */
	--color-sidebar-text: #eee; /* theme-default */
	--color-sidebar-text-alternative: #a2aab2; /* theme-default */
	--color-sidebar-gridicon-fill: #a2aab2; /* theme-default */
	--color-sidebar-gridicon-hover-fill: #00b9eb; /* theme-default */
	--color-sidebar-gridicon-selected-fill: #00b9eb; /* theme-default */
	--color-sidebar-notice-background: #3c434a; /* theme-default */

	/* Sidebar Selected */
	--color-sidebar-menu-selected-background: #0073aa; /* theme-default */
	--color-sidebar-menu-selected-text: var(--color-text-inverted); /* theme-default */
	/* Sidebar Hover */
	--color-sidebar-menu-hover-background: #1a1e23;/* theme-default */
	--color-sidebar-menu-hover-text: #00b9eb; /* theme-default */

	/* Sidebar Submenu - Nav Unification */
	--color-sidebar-submenu-background: #32373c; /* theme-default */
	--color-sidebar-submenu-text: #b4b9be;
	--color-sidebar-submenu-hover-background: transparent; /* theme-default */
	--color-sidebar-submenu-hover-text: #00b9eb; /* theme-default */
	--color-sidebar-submenu-selected-background: transparent; /* theme-default */
	--color-sidebar-submenu-selected-text: var(--color-text-inverted); /* theme-default */

	/* Sidebar Submenu - Nav Redesign */
	--color-navredesign-sidebar-submenu-text: rgba(240, 246, 252, 0.7); /* Direct from wp-admin */
	--color-navredesign-sidebar-menu-selected-text: var(--theme-text-color);
	--color-navredesign-sidebar-submenu-selected-text: var(--theme-text-color);
	--color-navredesign-sidebar-submenu-hover-text: #72aee6; /* Direct from wp-admin */

	/* Gutenberg components */
	--wp-admin-theme-color: var(--color-accent);
	--wp-admin-theme-color-darker-20: var(--color-accent-60);
}
