.color-scheme.is-sunset {
	/* Theme Properties */
	--color-primary: var(--studio-red-50);
	--color-primary-dark: var(--studio-red-70);
	--color-primary-light: var(--studio-red-30);
	--color-primary-0: var(--studio-red-0);
	--color-primary-5: var(--studio-red-5);
	--color-primary-10: var(--studio-red-10);
	--color-primary-20: var(--studio-red-20);
	--color-primary-30: var(--studio-red-30);
	--color-primary-40: var(--studio-red-40);
	--color-primary-50: var(--studio-red-50);
	--color-primary-60: var(--studio-red-60);
	--color-primary-70: var(--studio-red-70);
	--color-primary-80: var(--studio-red-80);
	--color-primary-90: var(--studio-red-90);
	--color-primary-100: var(--studio-red-100);
	--color-accent: var(--studio-orange-50);
	--color-accent-dark: var(--studio-orange-70);
	--color-accent-light: var(--studio-orange-30);
	--color-accent-0: var(--studio-orange-0);
	--color-accent-5: var(--studio-orange-5);
	--color-accent-10: var(--studio-orange-10);
	--color-accent-20: var(--studio-orange-20);
	--color-accent-30: var(--studio-orange-30);
	--color-accent-40: var(--studio-orange-40);
	--color-accent-50: var(--studio-orange-50);
	--color-accent-60: var(--studio-orange-60);
	--color-accent-70: var(--studio-orange-70);
	--color-accent-80: var(--studio-orange-80);
	--color-accent-90: var(--studio-orange-90);
	--color-accent-100: var(--studio-orange-100);
	--color-masterbar-background: var(--studio-red-80);
	--color-masterbar-border: var(--studio-red-80);
	--color-masterbar-text: var(--studio-white);
	--color-masterbar-icon: var(--studio-white);
	--color-masterbar-highlight: var(--studio-yellow-20);
	--color-masterbar-item-hover-background: var(--studio-red-90);
	--color-masterbar-item-active-background: var(--studio-red-100);
	--color-masterbar-item-new-editor-background: var(--studio-gray-50);
	--color-masterbar-item-new-editor-hover-background: var(--studio-gray-60);
	--color-masterbar-toggle-drafts-editor-background: var(--studio-gray-60);
	--color-masterbar-toggle-drafts-editor-hover-background: var(--studio-gray-40);
	--color-masterbar-toggle-drafts-editor-border: var(--studio-gray-10);

	--color-sidebar-background: var(--studio-red-70);
	--color-sidebar-border: var(--studio-red-80);
	--color-sidebar-text: var(--studio-white);
	--color-sidebar-text-alternative: var(--studio-red-10);
	--color-sidebar-gridicon-fill: var(--studio-red-5);
	--color-sidebar-menu-selected-background: var(--studio-yellow-20);
	--color-sidebar-menu-selected-text: var(--studio-yellow-80);
	--color-sidebar-menu-hover-background: var(--studio-red-80);
	--color-sidebar-menu-hover-text: var(--studio-white);

	/* Sidebar Submenu - Nav Unification */
	--color-sidebar-submenu-background: var(--studio-red-60);
	--color-sidebar-submenu-text: var(--studio-white);
	--color-sidebar-submenu-hover-text: var(--studio-yellow-20);
	--color-sidebar-submenu-selected-text: var(--studio-white);

	/* Sidebar Menu - Nav Redesign */
	--color-navredesign-sidebar-menu-selected-text: var(--studio-yellow-80);

	/* Sidebar Submenu - Nav Redesign */
	--color-navredesign-sidebar-submenu-text: var(--color-sidebar-submenu-text);
	--color-navredesign-sidebar-submenu-hover-text: var(--color-sidebar-submenu-hover-text);

	/* Collapse Menu Button */
	--color-collapse-menu-text: #facfd2; /* Direct from wp-admin */

	/* Gutenberg components */
	--wp-admin-theme-color: var(--color-accent);
	--wp-admin-theme-color-darker-20: var(--color-accent-60);
}
