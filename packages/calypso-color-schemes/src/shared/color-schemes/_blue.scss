/*
WP Admin Unthemed definitions:

$text-color: #fff !default;
$base-color: #23282d !default;
$icon-color: hsl( hue( $base-color ), 7%, 95% ) !default;
$highlight-color: #0073aa !default;
$notification-color: #d54e21 !default;

WP Admin Blue Definition:

$base-color: #52accc;
$icon-color: #e5f8ff;
$highlight-color: #096484;
$notification-color: #e1a948;
$button-color: #e1a948;

$menu-submenu-text: #e2ecf1;
$menu-submenu-focus-text: #fff;
$menu-submenu-background: #4796b3;

Used studio-blue for the primary+accent.
*/

.color-scheme.is-blue {
	/* Variables used in Calypso blue */
	--theme-text-color: #fff; /* $text-color */
	--theme-base-color: #52accc; /* $base-color */
	--theme-submenu-text-color: #e2ecf1; /* $menu-submenu-text */
	--theme-submenu-background-color: #4796b3; /* From wp-admin*/
	--theme-icon-color: #e5f8ff; /* $icon-color */
	--theme-highlight-color: #096484; /* $highlight-color */
	--theme-notification-color: #e1a948; /* Direct from wp-admin */

	/* Primary */
	--color-primary: var(--theme-highlight-color);
	--color-primary-dark: var(--color-deprecated-blue-70);
	--color-primary-light: var(--color-deprecated-blue-30);
	--color-primary-0: var(--color-deprecated-blue-0);
	--color-primary-5: var(--color-deprecated-blue-5);
	--color-primary-10: var(--color-deprecated-blue-10);
	--color-primary-20: var(--color-deprecated-blue-20);
	--color-primary-30: var(--color-deprecated-blue-30);
	--color-primary-40: var(--color-deprecated-blue-40);
	--color-primary-50: var(--color-deprecated-blue-50);
	--color-primary-60: var(--color-deprecated-blue-60);
	--color-primary-70: var(--color-deprecated-blue-70);
	--color-primary-80: var(--color-deprecated-blue-80);
	--color-primary-90: var(--color-deprecated-blue-90);
	--color-primary-100: var(--color-deprecated-blue-100);
	--color-accent: var(--theme-highlight-color);
	--color-accent-dark: var(--color-deprecated-blue-70);
	--color-accent-light: var(--color-deprecated-blue-30);
	--color-accent-0: var(--color-deprecated-blue-0);
	--color-accent-5: var(--color-deprecated-blue-5);
	--color-accent-10: var(--color-deprecated-blue-10);
	--color-accent-20: var(--color-deprecated-blue-20);
	--color-accent-30: var(--color-deprecated-blue-30);
	--color-accent-40: var(--color-deprecated-blue-40);
	--color-accent-50: var(--color-deprecated-blue-50);
	--color-accent-60: var(--color-deprecated-blue-60);
	--color-accent-70: var(--color-deprecated-blue-70);
	--color-accent-80: var(--color-deprecated-blue-80);
	--color-accent-90: var(--color-deprecated-blue-90);
	--color-accent-100: var(--color-deprecated-blue-100);
	--color-masterbar-background: var(--theme-base-color);
	--color-masterbar-border: #6eb9d4; /* $adminbar-avatar-frame */
	--color-masterbar-text: var(--theme-text-color);
	--color-masterbar-submenu-text: var(--theme-submenu-text-color);
	--color-masterbar-icon: var(--theme-icon-color);
	--color-masterbar-highlight: var(--theme-text-color);
	--color-masterbar-unread-dot-background: var(--theme-notification-color);

	--color-masterbar-item-hover-background: var(--theme-submenu-background-color);
	--color-masterbar-item-active-background: var(--theme-submenu-background-color);
	--color-masterbar-item-new-editor-background: var(--studio-gray-50);
	--color-masterbar-item-new-editor-hover-background: var(--studio-gray-60);

	--color-masterbar-toggle-drafts-editor-background: var(--studio-gray-60);
	--color-masterbar-toggle-drafts-editor-hover-background: var(--studio-gray-40);
	--color-masterbar-toggle-drafts-editor-border: var(--studio-gray-10);

	/* Sidebar */
	--color-sidebar-background: var(--theme-base-color);
	--color-sidebar-border: var(--theme-submenu-background-color);
	--color-sidebar-text: var(--theme-text-color);
	--color-sidebar-text-alternative: #e2ecf1; /* $menu-submenu-text: */
	--color-sidebar-gridicon-fill: var(--theme-icon-color);

	/* Sidebar Selected */
	--color-sidebar-menu-selected-background: var(--theme-highlight-color);
	--color-sidebar-menu-selected-text: var(--theme-text-color);
	--color-sidebar-menu-hover-background: var(--theme-highlight-color);
	--color-sidebar-menu-hover-text: var(--theme-text-color);

	/* Sidebar Hover - Nav unification */

	/* Sidebar Submenu - Nav Unification */
	--color-sidebar-submenu-background: var(--theme-submenu-background-color);
	--color-sidebar-submenu-text: var(--theme-submenu-text-color);
	--color-sidebar-submenu-hover-background: transparent;
	--color-sidebar-submenu-hover-text: var(--theme-text-color);

	/* Sidebar Submenu - Nav Redesign */
	--color-navredesign-sidebar-menu-selected-text: var(--theme-text-color);

	/* Sidebar Submenu - Nav Redesign */
	--color-navredesign-sidebar-submenu-text: #e2ecf1; /* $menu-submenu-text: */
	--color-navredesign-sidebar-submenu-selected-text: var(--theme-text-color);
	--color-navredesign-sidebar-submenu-hover-text: var(--theme-text-color);

	/* Collapse Menu Button */
	--color-collapse-menu-text: var(--theme-icon-color);

	/* Gutenberg components */
	--wp-admin-theme-color: var(--color-accent);
	--wp-admin-theme-color-darker-20: var(--color-accent-60);
}
