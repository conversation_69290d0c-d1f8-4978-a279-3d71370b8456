.color-scheme.is-classic-bright {
	/* Theme Properties */
	--theme-text-color: #044b7a; /* Direct from wp-admin */
	--color-primary: var(--color-deprecated-blue-50);
	--color-primary-dark: var(--color-deprecated-blue-70);
	--color-primary-light: var(--color-deprecated-blue-30);
	--color-primary-0: var(--color-deprecated-blue-0);
	--color-primary-5: var(--color-deprecated-blue-5);
	--color-primary-10: var(--color-deprecated-blue-10);
	--color-primary-20: var(--color-deprecated-blue-20);
	--color-primary-30: var(--color-deprecated-blue-30);
	--color-primary-40: var(--color-deprecated-blue-40);
	--color-primary-50: var(--color-deprecated-blue-50);
	--color-primary-60: var(--color-deprecated-blue-60);
	--color-primary-70: var(--color-deprecated-blue-70);
	--color-primary-80: var(--color-deprecated-blue-80);
	--color-primary-90: var(--color-deprecated-blue-90);
	--color-primary-100: var(--color-deprecated-blue-100);
	--color-accent: var(--studio-pink-50);
	--color-accent-dark: var(--studio-pink-70);
	--color-accent-light: var(--studio-pink-30);
	--color-accent-0: var(--studio-pink-0);
	--color-accent-5: var(--studio-pink-5);
	--color-accent-10: var(--studio-pink-10);
	--color-accent-20: var(--studio-pink-20);
	--color-accent-30: var(--studio-pink-30);
	--color-accent-40: var(--studio-pink-40);
	--color-accent-50: var(--studio-pink-50);
	--color-accent-60: var(--studio-pink-60);
	--color-accent-70: var(--studio-pink-70);
	--color-accent-80: var(--studio-pink-80);
	--color-accent-90: var(--studio-pink-90);
	--color-accent-100: var(--studio-pink-100);
	--color-masterbar-background: var(--color-deprecated-blue-60);
	--color-masterbar-border: var(--color-deprecated-blue-70);
	--color-masterbar-text: var(--studio-white);
	--color-masterbar-icon: var(--studio-white);
	--color-masterbar-highlight: var(--color-accent-30);
	--color-masterbar-item-hover-background: var(--color-deprecated-blue-70);
	--color-masterbar-item-active-background: var(--color-deprecated-blue-90);
	--color-masterbar-item-new-editor-background: var(--studio-gray-50);
	--color-masterbar-item-new-editor-hover-background: var(--studio-gray-40);
	--color-masterbar-unread-dot-background: var(--color-accent-20);

	--color-masterbar-toggle-drafts-editor-background: var(--studio-gray-40);
	--color-masterbar-toggle-drafts-editor-hover-background: var(--studio-gray-40);
	--color-masterbar-toggle-drafts-editor-border: var(--studio-gray-10);

	/* Sidebar */
	--color-sidebar-background: var(--color-surface);
	--color-sidebar-border: var(--studio-gray-5);
	--color-sidebar-text: var(--studio-gray-80);
	--color-sidebar-text-alternative: var(--studio-gray-50);
	--color-sidebar-gridicon-fill: var(--studio-gray-50);

	/* Sidebar Selected */
	--color-sidebar-menu-selected-background: var(--color-deprecated-blue-5);
	--color-sidebar-menu-selected-text: var(--color-deprecated-blue-70);
	--color-sidebar-menu-hover-background: var(--studio-gray-5);
	--color-sidebar-menu-hover-text: var(--studio-gray-90);

	/* Sidebar Submenu - Nav Unification */
	--color-sidebar-submenu-background: var(--color-deprecated-blue-0);
	--color-sidebar-submenu-text: var(--color-deprecated-blue-70);
	--color-sidebar-submenu-hover-text: var(--color-accent);
	--color-sidebar-submenu-selected-text: var(--color-accent);
	--color-sidebar-submenu-selected-hover-text: var(--color-accent);

	/* Sidebar Submenu - Nav Redesign */
	--color-navredesign-sidebar-submenu-text: var(--color-sidebar-submenu-text);
	--color-navredesign-sidebar-menu-selected-text: var(--theme-text-color);
	--color-navredesign-sidebar-submenu-selected-text: var(--color-accent);
	--color-navredesign-sidebar-submenu-hover-text: var(--color-accent);
	--color-navredesign-sidebar-submenu-selected-hover-text: var(--color-accent);

	/* Collapse Menu Button */
	--color-collapse-menu-text: var(--color-sidebar-text-alternative);

	/* Gutenberg components */
	--wp-admin-theme-color: var(--color-accent);
	--wp-admin-theme-color-darker-20: var(--color-accent-60);
}
