/*
Light

This is a theme ported directly from wp-admin as a part of the nav unification
project.

WP Admin Unthemed definitions:

$text-color: #fff !default;
$base-color: #23282d !default;
$icon-color: hsl( hue( $base-color ), 7%, 95% ) !default;
$highlight-color: #0073aa !default;
$notification-color: #d54e21 !default;

WP Admin Light Definition:

$base-color: #e5e5e5;
$icon-color: #999;
$text-color: #333;
$highlight-color: #04a4cc;
$notification-color: #d64e07;

$body-background: #f5f5f5;

$menu-highlight-text: #fff;
$menu-highlight-icon: #ccc;
$menu-highlight-background: #888;

$menu-bubble-text: #fff;
$menu-avatar-frame: #aaa;
$menu-submenu-background: #fff;

$menu-collapse-text: #777;
$menu-collapse-focus-icon: #555;

Primary+Accent is studio blue.
*/


.color-scheme.is-light {
	/* Variables used in Calypso Light */
	--theme-text-color: #333; /* $text-color */
	--theme-base-color: #e5e5e5; /* $base-color */
	--theme-submenu-text-color: #686868; /* $menu-submenu-text */
	--theme-submenu-background-color: #fff; /* $menu-submenu-background */
	--theme-icon-color: #999; /* $icon-color */
	--theme-highlight-color: #04a4cc; /* $highlight-color */
	--theme-notification-color: #d64e07; /* Direct from wp-admin */

	/* Primary */
	--color-primary: var(--theme-highlight-color);
	--color-primary-dark: var(--color-deprecated-blue-70);
	--color-primary-light: var(--color-deprecated-blue-30);
	--color-primary-0: var(--color-deprecated-blue-0);
	--color-primary-5: var(--color-deprecated-blue-5);
	--color-primary-10: var(--color-deprecated-blue-10);
	--color-primary-20: var(--color-deprecated-blue-20);
	--color-primary-30: var(--color-deprecated-blue-30);
	--color-primary-40: var(--color-deprecated-blue-40);
	--color-primary-50: var(--color-deprecated-blue-50);
	--color-primary-60: var(--color-deprecated-blue-60);
	--color-primary-70: var(--color-deprecated-blue-70);
	--color-primary-80: var(--color-deprecated-blue-80);
	--color-primary-90: var(--color-deprecated-blue-90);
	--color-primary-100: var(--color-deprecated-blue-100);
	--color-accent: var(--theme-highlight-color);
	--color-accent-dark: var(--color-deprecated-blue-70);
	--color-accent-light: var(--color-deprecated-blue-30);
	--color-accent-0: var(--color-deprecated-blue-0);
	--color-accent-5: var(--color-deprecated-blue-5);
	--color-accent-10: var(--color-deprecated-blue-10);
	--color-accent-20: var(--color-deprecated-blue-20);
	--color-accent-30: var(--color-deprecated-blue-30);
	--color-accent-40: var(--color-deprecated-blue-40);
	--color-accent-50: var(--color-deprecated-blue-50);
	--color-accent-60: var(--color-deprecated-blue-60);
	--color-accent-70: var(--color-deprecated-blue-70);
	--color-accent-80: var(--color-deprecated-blue-80);
	--color-accent-90: var(--color-deprecated-blue-90);
	--color-accent-100: var(--color-deprecated-blue-100);
	--color-masterbar-background: var(--theme-base-color);
	--color-masterbar-border: #f7f7f7; /* $adminbar-avatar-frame */
	--color-masterbar-text: var(--theme-text-color);
	--color-masterbar-submenu-text: var(--theme-submenu-text-color);
	--color-masterbar-icon: var(--theme-icon-color);
	--color-masterbar-highlight: var(--theme-highlight-color);
	--color-masterbar-unread-dot-background: var(--theme-notification-color);

	--color-masterbar-item-hover-background: var(--theme-submenu-background-color);
	--color-masterbar-item-active-background: var(--theme-submenu-background-color);
	--color-masterbar-item-new-editor-background: var(--studio-gray-50);
	--color-masterbar-item-new-editor-hover-background: var(--studio-gray-40);

	--color-masterbar-toggle-drafts-editor-background: var(--studio-gray-40);
	--color-masterbar-toggle-drafts-editor-hover-background: var(--studio-gray-60);
	--color-masterbar-toggle-drafts-editor-border: var(--studio-gray-90);


	/* Sidebar */
	--color-sidebar-background: var(--theme-base-color);
	--color-sidebar-border: var(--theme-submenu-background-color);
	--color-sidebar-text: var(--theme-text-color);
	--color-sidebar-text-alternative: var(--studio-gray-90);
	--color-sidebar-gridicon-fill: var(--theme-icon-color);

	/* Sidebar Selected */
	--color-sidebar-menu-selected-text: #fff;
	--color-sidebar-menu-selected-background: #888;
	--color-sidebar-menu-hover-background: #888;
	--color-sidebar-menu-hover-text: #fff;

	/* Sidebar Hover - Nav unification */

	/* Sidebar Submenu - Nav Unification */
	--color-sidebar-submenu-background: var(--theme-submenu-background-color);
	--color-sidebar-submenu-text: var(--theme-submenu-text-color);
	--color-sidebar-submenu-hover-background: transparent;
	--color-sidebar-submenu-hover-text: var(--theme-highlight-color);
	--color-sidebar-submenu-selected-text: #333;
	--color-sidebar-submenu-selected-hover-text: var(--theme-highlight-color);

	/* Sidebar Submenu - Nav Redesign */
	--color-navredesign-sidebar-submenu-text: var(--color-sidebar-submenu-text);
	--color-navredesign-sidebar-submenu-selected-text: var(--theme-text-color);
	--color-navredesign-sidebar-submenu-hover-text: var(--theme-highlight-color);
	--color-navredesign-sidebar-submenu-selected-hover-text: var(--theme-highlight-color);

	/* Collapse Menu Button */
	--color-collapse-menu-text: #777;

	/* Fix for notification bell */
	.masterbar__item-notifications {
		.gridicon {
			fill: #000;
		}
	}

	/* Gutenberg components */
	--wp-admin-theme-color: var(--color-accent);
	--wp-admin-theme-color-darker-20: var(--color-accent-60);
}
