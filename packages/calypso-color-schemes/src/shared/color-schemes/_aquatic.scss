.color-scheme.is-aquatic {
	/* Theme Properties */
	--color-primary: var(--color-deprecated-blue-50);
	--color-primary-dark: var(--color-deprecated-blue-70);
	--color-primary-light: var(--color-deprecated-blue-30);
	--color-primary-0: var(--color-deprecated-blue-0);
	--color-primary-5: var(--color-deprecated-blue-5);
	--color-primary-10: var(--color-deprecated-blue-10);
	--color-primary-20: var(--color-deprecated-blue-20);
	--color-primary-30: var(--color-deprecated-blue-30);
	--color-primary-40: var(--color-deprecated-blue-40);
	--color-primary-50: var(--color-deprecated-blue-50);
	--color-primary-60: var(--color-deprecated-blue-60);
	--color-primary-70: var(--color-deprecated-blue-70);
	--color-primary-80: var(--color-deprecated-blue-80);
	--color-primary-90: var(--color-deprecated-blue-90);
	--color-primary-100: var(--color-deprecated-blue-100);
	--color-accent: var(--studio-celadon-50);
	--color-accent-dark: var(--studio-celadon-70);
	--color-accent-light: var(--studio-celadon-30);
	--color-accent-0: var(--studio-celadon-0);
	--color-accent-5: var(--studio-celadon-5);
	--color-accent-10: var(--studio-celadon-10);
	--color-accent-20: var(--studio-celadon-20);
	--color-accent-30: var(--studio-celadon-30);
	--color-accent-40: var(--studio-celadon-40);
	--color-accent-50: var(--studio-celadon-50);
	--color-accent-60: var(--studio-celadon-60);
	--color-accent-70: var(--studio-celadon-70);
	--color-accent-80: var(--studio-celadon-80);
	--color-accent-90: var(--studio-celadon-90);
	--color-accent-100: var(--studio-celadon-100);
	--color-masterbar-background: var(--color-deprecated-blue-80);
	--color-masterbar-border: var(--color-deprecated-blue-80);
	--color-masterbar-text: var(--studio-white);
	--color-masterbar-icon: var(--studio-white);
	--color-masterbar-highlight: var(--studio-yellow-20);
	--color-masterbar-item-hover-background: var(--color-deprecated-blue-90);
	--color-masterbar-item-active-background: var(--color-deprecated-blue-100);
	--color-masterbar-item-new-editor-background: var(--studio-gray-50);
	--color-masterbar-item-new-editor-hover-background: var(--studio-gray-60);
	--color-masterbar-unread-dot-background: var(--color-accent-30);
	--color-masterbar-toggle-drafts-editor-background: var(--studio-gray-60);
	--color-masterbar-toggle-drafts-editor-hover-background: var(--studio-gray-40);
	--color-masterbar-toggle-drafts-editor-border: var(--studio-gray-10);

	--color-sidebar-background: var(--color-deprecated-blue-60);
	--color-sidebar-border: var(--color-deprecated-blue-70);
	--color-sidebar-text: var(--studio-white);
	--color-sidebar-text-alternative: var(--color-deprecated-blue-5);
	--color-sidebar-gridicon-fill: var(--color-deprecated-blue-5);
	--color-sidebar-menu-selected-background: var(--studio-yellow-20);
	--color-sidebar-menu-selected-text: var(--color-deprecated-blue-90);
	--color-sidebar-menu-hover-background: var(--color-deprecated-blue-50);
	--color-sidebar-menu-hover-text: var(--studio-white);

	/* Sidebar Submenu - Nav Unification */
	--color-sidebar-submenu-background: var(--color-deprecated-blue-80);
	--color-sidebar-submenu-text: var(--studio-white);
	--color-sidebar-submenu-hover-text: var(--studio-yellow-20);

	/* Sidebar Menu - Nav Redesign */
	--color-navredesign-sidebar-menu-selected-text: var(--color-deprecated-blue-90);

	/* Sidebar Submenu - Nav Redesign */
	--color-navredesign-sidebar-submenu-text: var(--color-sidebar-submenu-text);
	--color-navredesign-sidebar-submenu-hover-text: var(--color-sidebar-submenu-hover-text);

	/* Collapse Menu Button */
	--color-collapse-menu-text: var(--color-sidebar-submenu-hover-text);

	/* Gutenberg components */
	--wp-admin-theme-color: var(--color-accent);
	--wp-admin-theme-color-darker-20: var(--color-accent-60);
}
