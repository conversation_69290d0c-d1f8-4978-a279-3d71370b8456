.theme-jetpack-cloud,
.color-scheme.is-jetpack-cloud {
	/* Theme Properties */
	--color-primary: var(--studio-black);
	--color-primary-dark: var(--studio-gray-70);
	--color-primary-light: var(--studio-gray-30);
	--color-primary-0: var(--studio-gray-0);
	--color-primary-5: var(--studio-gray-5);
	--color-primary-10: var(--studio-gray-10);
	--color-primary-20: var(--studio-gray-20);
	--color-primary-30: var(--studio-gray-30);
	--color-primary-40: var(--studio-gray-40);
	--color-primary-50: var(--studio-gray-50);
	--color-primary-60: var(--studio-gray-60);
	--color-primary-70: var(--studio-gray-70);
	--color-primary-80: var(--studio-gray-80);
	--color-primary-90: var(--studio-gray-90);
	--color-primary-100: var(--studio-gray-100);
	--color-accent: var(--studio-black);
	--color-accent-dark: var(--studio-gray-70);
	--color-accent-light: var(--studio-gray-30);
	--color-accent-0: var(--studio-gray-0);
	--color-accent-5: var(--studio-gray-5);
	--color-accent-10: var(--studio-gray-10);
	--color-accent-20: var(--studio-gray-20);
	--color-accent-30: var(--studio-gray-30);
	--color-accent-40: var(--studio-gray-40);
	--color-accent-50: var(--studio-gray-50);
	--color-accent-60: var(--studio-gray-60);
	--color-accent-70: var(--studio-gray-70);
	--color-accent-80: var(--studio-gray-80);
	--color-accent-90: var(--studio-gray-90);
	--color-accent-100: var(--studio-gray-100);
	--color-link: var(--studio-jetpack-green-50);
	--color-link-dark: var(--studio-jetpack-green-70);
	--color-link-light: var(--studio-jetpack-green-30);

	/* Component Properties */
	--color-masterbar-background: var(--studio-white);
	--color-masterbar-border: var(--studio-gray-5);
	--color-masterbar-text: var(--studio-gray-50);
	--color-masterbar-item-hover-background: var(--studio-white);
	--color-masterbar-item-active-background: var(--studio-white);
	--color-masterbar-item-new-editor-background: var(--studio-white);
	--color-masterbar-item-new-editor-hover-background: var(--studio-white);
	--color-masterbar-unread-dot-background: var(--color-accent-30);
	--color-masterbar-toggle-drafts-editor-background: var(--studio-gray-60);
	--color-masterbar-toggle-drafts-editor-hover-background: var(--studio-gray-40);
	--color-masterbar-toggle-drafts-editor-border: var(--studio-gray-10);

	--color-sidebar-background: var(--studio-white);
	--color-sidebar-border: var(--studio-gray-5);
	--color-sidebar-text: var(--studio-gray-60);
	--color-sidebar-text-alternative: var(--studio-gray-60);
	--color-sidebar-gridicon-fill: var(--studio-gray-60);
	--color-sidebar-menu-selected-text: var(--studio-gray-90);
	--color-sidebar-menu-selected-background: var(--studio-gray-0);
	--color-sidebar-menu-selected-gridicon-fill: var(--studio-jetpack-green-50);
	--color-sidebar-menu-hover-text: var(--studio-gray-90);
	--color-sidebar-menu-hover-background: var(--studio-gray-0);
	--color-scary-0: var(--studio-red-0);
	--color-scary-5: var(--studio-red-5);
	--color-scary-40: var(--studio-red-40);
	--color-scary-50: var(--studio-red-50);
	--color-scary-60: var(--studio-red-60);
	--color-scary-70: var(--studio-red-70);

	/* Gutenberg components */
	--wp-admin-theme-color: var(--color-accent);
	--wp-admin-theme-color-darker-20: var(--color-accent-60);
}
