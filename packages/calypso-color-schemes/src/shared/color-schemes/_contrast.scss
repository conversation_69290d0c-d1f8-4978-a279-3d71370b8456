.color-scheme.is-contrast {
	/* Theme Properties */
	--color-primary: var(--studio-gray-80);
	--color-primary-dark: var(--studio-gray-100);
	--color-primary-light: var(--studio-gray-60);
	--color-primary-0: var(--studio-gray-0);
	--color-primary-5: var(--studio-gray-5);
	--color-primary-10: var(--studio-gray-10);
	--color-primary-20: var(--studio-gray-20);
	--color-primary-30: var(--studio-gray-30);
	--color-primary-40: var(--studio-gray-40);
	--color-primary-50: var(--studio-gray-50);
	--color-primary-60: var(--studio-gray-60);
	--color-primary-70: var(--studio-gray-70);
	--color-primary-80: var(--studio-gray-80);
	--color-primary-90: var(--studio-gray-90);
	--color-primary-100: var(--studio-gray-100);
	--color-accent: var(--color-deprecated-blue-70);
	--color-accent-dark: var(--color-deprecated-blue-90);
	--color-accent-light: var(--color-deprecated-blue-50);
	--color-accent-0: var(--color-deprecated-blue-0);
	--color-accent-5: var(--color-deprecated-blue-5);
	--color-accent-10: var(--color-deprecated-blue-10);
	--color-accent-20: var(--color-deprecated-blue-20);
	--color-accent-30: var(--color-deprecated-blue-30);
	--color-accent-40: var(--color-deprecated-blue-40);
	--color-accent-50: var(--color-deprecated-blue-50);
	--color-accent-60: var(--color-deprecated-blue-60);
	--color-accent-70: var(--color-deprecated-blue-70);
	--color-accent-80: var(--color-deprecated-blue-80);
	--color-accent-90: var(--color-deprecated-blue-90);
	--color-accent-100: var(--color-deprecated-blue-100);
	--color-surface-backdrop: var(--studio-white);
	--color-text: var(--studio-gray-100);
	--color-text-subtle: var(--studio-gray-70);
	--color-premium-domain: var(--studio-gray-100);

	--color-masterbar-background: var(--studio-gray-100);
	--color-masterbar-border: var(--studio-gray-90);
	--color-masterbar-text: var(--studio-white);
	--color-masterbar-icon: var(--studio-white);
	--color-masterbar-highlight: var(--studio-yellow-20);
	--color-masterbar-item-hover-background: var(--studio-gray-90);
	--color-masterbar-item-active-background: var(--studio-gray-90);
	--color-masterbar-item-new-editor-background: var(--studio-gray-70);
	--color-masterbar-item-new-editor-hover-background: var(--studio-gray-90);
	--color-masterbar-unread-dot-background: var(--studio-yellow-20);
	--color-masterbar-toggle-drafts-editor-background: var(--studio-gray-70);
	--color-masterbar-toggle-drafts-editor-hover-background: var(--studio-gray-70);
	--color-masterbar-toggle-drafts-editor-border: var(--studio-gray-10);

	--color-sidebar-background: var(--color-surface);
	--color-sidebar-border: var(--studio-gray-5);
	--color-sidebar-text: var(--studio-gray-90);
	--color-sidebar-text-alternative: var(--studio-gray-90);
	--color-sidebar-gridicon-fill: var(--studio-gray-90);
	--color-sidebar-menu-selected-background: var(--studio-gray-100);
	--color-sidebar-menu-selected-text: var(--studio-white);
	--color-sidebar-menu-hover-background: var(--studio-gray-60);
	--color-sidebar-menu-hover-text: var(--studio-white);

	/* Sidebar Submenu - Nav Unification */
	--color-sidebar-submenu-background: var(--studio-gray-90);
	--color-sidebar-submenu-text: var(--studio-gray-10);
	--color-sidebar-submenu-hover-text: var(--color-masterbar-unread-dot-background);
	--color-sidebar-submenu-selected-text: var(--studio-white);
	--color-sidebar-submenu-selected-hover-text: var(--color-sidebar-submenu-selected-text);

	/* Sidebar Submenu - Nav Redesign */
	--color-navredesign-sidebar-submenu-selected-hover-text: var(--color-sidebar-submenu-selected-text);

	/* Gutenberg components */
	--wp-admin-theme-color: var(--color-accent);
	--wp-admin-theme-color-darker-20: var(--color-accent-60);
}
