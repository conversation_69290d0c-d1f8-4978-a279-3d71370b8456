.color-scheme.is-classic-dark {
	/* Theme Properties */
	--color-primary: var(--studio-gray-90);
	--color-primary-dark: var(--studio-gray-70);
	--color-primary-light: var(--studio-gray-30);
	--color-primary-0: var(--studio-gray-0);
	--color-primary-5: var(--studio-gray-5);
	--color-primary-10: var(--studio-gray-10);
	--color-primary-20: var(--studio-gray-20);
	--color-primary-30: var(--studio-gray-30);
	--color-primary-40: var(--studio-gray-40);
	--color-primary-50: var(--studio-gray-50);
	--color-primary-60: var(--studio-gray-60);
	--color-primary-70: var(--studio-gray-70);
	--color-primary-80: var(--studio-gray-80);
	--color-primary-90: var(--studio-gray-90);
	--color-primary-100: var(--studio-gray-100);
	--color-accent: var(--studio-pink-50);
	--color-accent-dark: var(--studio-pink-70);
	--color-accent-light: var(--studio-pink-30);
	--color-accent-0: var(--studio-pink-0);
	--color-accent-5: var(--studio-pink-5);
	--color-accent-10: var(--studio-pink-10);
	--color-accent-20: var(--studio-pink-20);
	--color-accent-30: var(--studio-pink-30);
	--color-accent-40: var(--studio-pink-40);
	--color-accent-50: var(--studio-pink-50);
	--color-accent-60: var(--studio-pink-60);
	--color-accent-70: var(--studio-pink-70);
	--color-accent-80: var(--studio-pink-80);
	--color-accent-90: var(--studio-pink-90);
	--color-accent-100: var(--studio-pink-100);
	--color-neutral: var(--studio-gray-50);
	--color-neutral-dark: var(--studio-gray-70);
	--color-neutral-light: var(--studio-gray-30);
	--color-neutral-0: var(--studio-gray-0);
	--color-neutral-5: var(--studio-gray-5);
	--color-neutral-10: var(--studio-gray-10);
	--color-neutral-20: var(--studio-gray-20);
	--color-neutral-30: var(--studio-gray-30);
	--color-neutral-40: var(--studio-gray-40);
	--color-neutral-50: var(--studio-gray-50);
	--color-neutral-60: var(--studio-gray-60);
	--color-neutral-70: var(--studio-gray-70);
	--color-neutral-80: var(--studio-gray-80);
	--color-neutral-90: var(--studio-gray-90);
	--color-neutral-100: var(--studio-gray-100);
	--color-success: var(--studio-green-50);
	--color-success-dark: var(--studio-green-70);
	--color-success-light: var(--studio-green-30);
	--color-success-0: var(--studio-green-0);
	--color-success-5: var(--studio-green-5);
	--color-success-10: var(--studio-green-10);
	--color-success-20: var(--studio-green-20);
	--color-success-30: var(--studio-green-30);
	--color-success-40: var(--studio-green-40);
	--color-success-50: var(--studio-green-50);
	--color-success-60: var(--studio-green-60);
	--color-success-70: var(--studio-green-70);
	--color-success-80: var(--studio-green-80);
	--color-success-90: var(--studio-green-90);
	--color-success-100: var(--studio-green-100);
	--color-warning: var(--studio-yellow-50);
	--color-warning-dark: var(--studio-yellow-70);
	--color-warning-light: var(--studio-yellow-30);
	--color-warning-0: var(--studio-yellow-0);
	--color-warning-5: var(--studio-yellow-5);
	--color-warning-10: var(--studio-yellow-10);
	--color-warning-20: var(--studio-yellow-20);
	--color-warning-30: var(--studio-yellow-30);
	--color-warning-40: var(--studio-yellow-40);
	--color-warning-50: var(--studio-yellow-50);
	--color-warning-60: var(--studio-yellow-60);
	--color-warning-70: var(--studio-yellow-70);
	--color-warning-80: var(--studio-yellow-80);
	--color-warning-90: var(--studio-yellow-90);
	--color-warning-100: var(--studio-yellow-100);
	--color-error: var(--studio-red-50);
	--color-error-dark: var(--studio-red-70);
	--color-error-light: var(--studio-red-30);
	--color-error-0: var(--studio-red-0);
	--color-error-5: var(--studio-red-5);
	--color-error-10: var(--studio-red-10);
	--color-error-20: var(--studio-red-20);
	--color-error-30: var(--studio-red-30);
	--color-error-40: var(--studio-red-40);
	--color-error-50: var(--studio-red-50);
	--color-error-60: var(--studio-red-60);
	--color-error-70: var(--studio-red-70);
	--color-error-80: var(--studio-red-80);
	--color-error-90: var(--studio-red-90);
	--color-error-100: var(--studio-red-100);
	--color-surface: var(--studio-white);
	--color-surface-backdrop: var(--studio-gray-0);
	--color-text: var(--studio-gray-80);
	--color-text-subtle: var(--studio-gray-50);
	--color-text-inverted: var(--studio-white);
	--color-border: var(--color-neutral-20);
	--color-border-subtle: var(--color-neutral-5);
	--color-border-shadow: var(--color-neutral-0);
	--color-border-inverted: var(--studio-white);
	--color-border-secondary: #f1f1f1;

	/* Component Properties */
	--color-plan-free: var(--studio-gray-30);
	--color-plan-blogger: var(--studio-celadon-30);
	--color-plan-personal: var(--color-deprecated-blue-30);
	--color-plan-premium: var(--studio-yellow-30);
	--color-plan-business: var(--studio-orange-30);
	--color-plan-ecommerce: var(--studio-purple-30);
	--color-premium-domain: var(--studio-wordpress-blue-60);

	--color-jetpack-plan-free: var(--color-deprecated-blue-30);
	--color-jetpack-plan-personal: var(--studio-yellow-30);
	--color-jetpack-plan-premium: var(--studio-jetpack-green-30);
	--color-jetpack-plan-professional: var(--studio-purple-30);

	--color-masterbar-background: #101517;
	--color-masterbar-border: #333;
	--color-masterbar-text: var(--studio-white);
	--color-masterbar-icon: var(--studio-white);
	--color-masterbar-highlight: #00b9eb;
	--color-masterbar-item-hover-background: #333;
	--color-masterbar-item-active-background: #23282d;
	--color-masterbar-item-new-editor-background: var(--studio-gray-50);
	--color-masterbar-item-new-editor-hover-background: var(--studio-gray-40);
	--color-masterbar-unread-dot-background: var(--color-accent-20);
	--color-masterbar-toggle-drafts-editor-background: var(--studio-gray-40);
	--color-masterbar-toggle-drafts-editor-hover-background: var(--studio-gray-40);
	--color-masterbar-toggle-drafts-editor-border: var(--studio-gray-10);

	--color-checkout-masterbar-background: transparent;
	--color-checkout-masterbar-text: var(--studio-gray-80);
	--color-checkout-masterbar-item-hover-background: var(--studio-gray-5);
	--color-checkout-masterbar-item-active-background: var(--studio-gray-20);

	--color-sidebar-background: #23282d;
	--color-sidebar-border: #333;
	--color-sidebar-text: #eee;
	--color-sidebar-text-alternative: #a2aab2;
	--color-sidebar-gridicon-fill: #a2aab2;
	--color-sidebar-menu-selected-background: #0073aa;
	--color-sidebar-menu-selected-text: var(--studio-white);
	--color-sidebar-menu-hover-background: #1a1e23;
	--color-sidebar-menu-hover-text: #00b9eb;

	--color-jetpack-onboarding-text: var(--studio-white);
	--color-jetpack-onboarding-background: var(--color-deprecated-blue-100);
	--color-automattic: var(--color-deprecated-blue-40);
	--color-jetpack: var(--studio-jetpack-green);
	--color-simplenote: var(--studio-simplenote-blue);
	--color-woocommerce: var(--studio-woocommerce-purple);
	--color-wordpress-com: var(--studio-wordpress-blue);
	--color-wordpress-org: #585c60;

	--color-blogger: #ff5722;
	--color-bluesky: #0085ff;
	--color-eventbrite: #ff8000;
	--color-facebook: #0866ff;
	--color-godaddy: #5ea95a;
	--color-google-plus: #df4a32;
	--color-instagram: #d93174;
	--color-linkedin: #0976b4;
	--color-mastodon: #6364ff;
	--color-medium: #12100e;
	--color-nextdoor: #8ed500;
	--color-pinterest: #cc2127;
	--color-pocket: #ee4256;
	--color-print: #f8f8f8;
	--color-reddit: #5f99cf;
	--color-skype: #00aff0;
	--color-stumbleupon: #eb4924;
	--color-substack: #ff6719;
	--color-squarespace: #222;
	--color-telegram: #08c;
	--color-threads: #000;
	--color-tumblr: #35465c;
	--color-twitter: #55acee;
	--color-whatsapp: #43d854;
	--color-wix: #faad4d;
	--color-x: #000;

	--color-email: var(--studio-gray-0);
	--color-podcasting: #9b4dd5;

	/* WP Admin */
	--color-wp-admin-button-background: #008ec2;
	--color-wp-admin-button-border: #006799;

	/* Sidebar Submenu - Nav Unification */
	--color-sidebar-submenu-background: #32373c;
	--color-sidebar-submenu-text: #b4b9be;
	--color-sidebar-submenu-hover-text: #00b9eb;
	--color-sidebar-submenu-selected-text: var(--studio-white);

	/* Sidebar Submenu - Nav Redesign */
	--color-navredesign-sidebar-submenu-hover-text: var(--color-sidebar-submenu-hover-text);
	--color-navredesign-sidebar-submenu-selected-hover-text: var(--color-sidebar-submenu-text);

	/* Collapse Menu Button */
	--color-collapse-menu-text: var(--color-sidebar-text-alternative);

	/* Gutenberg components */
	--wp-admin-theme-color: var(--color-accent);
	--wp-admin-theme-color-darker-20: var(--color-accent-60);
}
