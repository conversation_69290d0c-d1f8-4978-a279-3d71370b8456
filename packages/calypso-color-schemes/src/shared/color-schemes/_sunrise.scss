/*
Sunrise

This is a theme ported directly from wp-admin as a part of the nav unification
project.

WP Admin Unthemed definitions:

$text-color: #fff !default;
$base-color: #23282d !default;
$icon-color: hsl( hue( $base-color ), 7%, 95% ) !default;
$highlight-color: #0073aa !default;
$notification-color: #d54e21 !default;

WP Admin Sunrise Definition:

$base-color: #cf4944;
$icon-color: None, so ... using the wp-admin definition above: = #f3f1f1
$highlight-color: #dd823b;
$notification-color: #ccaf0b;
$menu-submenu-focus-text: lighten( $highlight-color, 35% ) = #f7e3d3

Well use studio-orange for both the primary and accent colors
*/

.color-scheme.is-sunrise {
	/* Variables used in Calypso Sunrise */
	--theme-text-color: #fff; /* $text-color: */
	--theme-base-color: #cf4944; /* $base-color */
	--theme-submenu-text-color: #f1c8c7; /* $menu-submenu-text */
	--theme-submenu-background-color: #be3631; /* $menu-submenu-background */
	--theme-submenu-hover-text-color: #f7e3d3; /* Direct from wp-admin */
	--theme-icon-color: #f3f1f1; /* $icon-color */
	--theme-highlight-color: #dd823b; /* $highlight-color */
	--theme-notification-color: #ccaf0b; /* Direct from wp-admin */

	/* Primary */
	--color-primary: var(--theme-highlight-color);
	--color-primary-dark: var(--studio-orange-70);
	--color-primary-light: var(--studio-orange-30);
	--color-primary-0: var(--studio-orange-0);
	--color-primary-5: var(--studio-orange-5);
	--color-primary-10: var(--studio-orange-10);
	--color-primary-20: var(--studio-orange-20);
	--color-primary-30: var(--studio-orange-30);
	--color-primary-40: var(--studio-orange-40);
	--color-primary-50: var(--studio-orange-50);
	--color-primary-60: var(--studio-orange-60);
	--color-primary-70: var(--studio-orange-70);
	--color-primary-80: var(--studio-orange-80);
	--color-primary-90: var(--studio-orange-90);
	--color-primary-100: var(--studio-orange-100);
	--color-accent: var(--theme-highlight-color);
	--color-accent-dark: var(--studio-orange-70);
	--color-accent-light: var(--studio-orange-30);
	--color-accent-0: var(--studio-orange-0);
	--color-accent-5: var(--studio-orange-5);
	--color-accent-10: var(--studio-orange-10);
	--color-accent-20: var(--studio-orange-20);
	--color-accent-30: var(--studio-orange-30);
	--color-accent-40: var(--studio-orange-40);
	--color-accent-50: var(--studio-orange-50);
	--color-accent-60: var(--studio-orange-60);
	--color-accent-70: var(--studio-orange-70);
	--color-accent-80: var(--studio-orange-80);
	--color-accent-90: var(--studio-orange-90);
	--color-accent-100: var(--studio-orange-100);
	--color-masterbar-background: var(--theme-base-color);
	--color-masterbar-border: #d66560; /* $adminbar-avatar-frame */
	--color-masterbar-text: var(--theme-text-color);
	--color-masterbar-submenu-text: var(--theme-submenu-text-color);
	--color-masterbar-icon: var(--theme-icon-color);
	--color-masterbar-highlight: var(--theme-submenu-hover-text-color);
	--color-masterbar-unread-dot-background: var(--theme-notification-color);

	--color-masterbar-item-hover-background: var(--theme-submenu-background-color);
	--color-masterbar-item-active-background: var(--theme-submenu-background-color);
	--color-masterbar-item-new-editor-background: var(--studio-gray-50);
	--color-masterbar-item-new-editor-hover-background: var(--studio-gray-60);

	--color-masterbar-toggle-drafts-editor-background: var(--studio-gray-60);
	--color-masterbar-toggle-drafts-editor-hover-background: var(--studio-gray-40);
	--color-masterbar-toggle-drafts-editor-border: var(--studio-gray-10);


	/* Sidebar */
	--color-sidebar-background: var(--theme-base-color);
	--color-sidebar-border: var(--theme-submenu-background-color);
	--color-sidebar-text: var(--theme-text-color);
	--color-sidebar-text-alternative: var(--studio-gray-0);
	--color-sidebar-gridicon-fill: var(--theme-icon-color);

	/* Sidebar Selected */
	--color-sidebar-menu-selected-background: var(--theme-highlight-color);
	--color-sidebar-menu-selected-text: var(--theme-text-color);
	--color-sidebar-menu-hover-background: var(--theme-highlight-color);
	--color-sidebar-menu-hover-text: var(--theme-text-color);

	/* Sidebar Submenu - Nav Unification */
	--color-sidebar-submenu-background: var(--theme-submenu-background-color);
	--color-sidebar-submenu-text: var(--theme-submenu-text-color);
	--color-sidebar-submenu-hover-background: transparent;
	--color-sidebar-submenu-hover-text: var(--theme-submenu-hover-text-color);
	--color-sidebar-submenu-selected-hover-text: var(--theme-submenu-hover-text-color);

	/* Sidebar Submenu - Nav Redesign */
	--color-navredesign-sidebar-submenu-text: var(--color-sidebar-submenu-text);
	--color-navredesign-sidebar-menu-selected-text: var(--theme-text-color);
	--color-navredesign-sidebar-submenu-selected-text: var(--theme-text-color);
	--color-navredesign-sidebar-submenu-hover-text: var(--theme-submenu-hover-text-color);
	--color-navredesign-sidebar-submenu-selected-hover-text: var(--theme-submenu-hover-text-color);

	/* Collapse Menu Button */
	--color-collapse-menu-text: var(--theme-icon-color);

	/* Gutenberg components */
	--wp-admin-theme-color: var(--color-accent);
	--wp-admin-theme-color-darker-20: var(--color-accent-60);
}
