/*
WP Admin Unthemed definitions:

$text-color: #fff !default;
$base-color: #23282d !default;
$icon-color: hsl( hue( $base-color ), 7%, 95% ) !default;
$highlight-color: #0073aa !default;
$notification-color: #d54e21 !default;

WP Admin Ectoplasm Definition:

$base-color: #523f6d;
$icon-color: #ece6f6;
$highlight-color: #a3b745;
$notification-color: #d46f15;

Created this definition in color-studio to generate 0-100 shades:
	{
		name: 'Ectoplasm Green',
		default: 50,
		specs: {
			hue_start: 70,
			hue_end: 72,
			hue_curve: 'easeOutSine',
			sat_steps: [
				8, 27, 56, 80, 100, 100, 100, 100, 100, 100, 100, 100
			],
			lum_steps: [
				96, 96, 95, 94, 87, 75.1, 61.6, 49, 40.5, 31, 20, 11
			]
		}
	}
*/

.color-scheme.is-ectoplasm {
	/* Variables used in Calypso Ectoplasm */
	--theme-text-color: #fff; /* $text-color */
	--theme-base-color: #523f6d; /* $base-color */
	--theme-submenu-text-color: #cbc5d3; /* $menu-submenu-text */
	--theme-submenu-background-color: #413256; /* $menu-submenu-background */
	--theme-icon-color: #ece6f6; /* $icon-color */
	--theme-highlight-color: #a3b745; /* $highlight-color */
	--theme-notification-color: #d46f15; /* Direct from wp-admin */

	--ectoplasm-green-0: #f2f5e1;
	--ectoplasm-green-5: #e9f5b3;
	--ectoplasm-green-10: #daf26b;
	--ectoplasm-green-20: #cdf030;
	--ectoplasm-green-30: #b5de00;
	--ectoplasm-green-40: #9bc000;
	--ectoplasm-green-50: #7f9d00;
	--ectoplasm-green-60: #647d00;
	--ectoplasm-green-70: #536700;
	--ectoplasm-green-80: #3f4f00;
	--ectoplasm-green-90: #293300;
	--ectoplasm-green-100: #161c00;
	--ectoplasm-green: #7f9d00;
	--color-primary: var(--theme-highlight-color);
	--color-primary-dark: var(--ectoplasm-green-70);
	--color-primary-light: var(--ectoplasm-green-30);
	--color-primary-0: var(--ectoplasm-green-0);
	--color-primary-5: var(--ectoplasm-green-5);
	--color-primary-10: var(--ectoplasm-green-10);
	--color-primary-20: var(--ectoplasm-green-20);
	--color-primary-30: var(--ectoplasm-green-30);
	--color-primary-40: var(--ectoplasm-green-40);
	--color-primary-50: var(--ectoplasm-green-50);
	--color-primary-60: var(--ectoplasm-green-60);
	--color-primary-70: var(--ectoplasm-green-70);
	--color-primary-80: var(--ectoplasm-green-80);
	--color-primary-90: var(--ectoplasm-green-90);
	--color-primary-100: var(--ectoplasm-green-100);
	--color-accent: var(--theme-highlight-color);
	--color-accent-dark: var(--ectoplasm-green-70);
	--color-accent-light: var(--ectoplasm-green-30);
	--color-accent-0: var(--ectoplasm-green-0);
	--color-accent-5: var(--ectoplasm-green-5);
	--color-accent-10: var(--ectoplasm-green-10);
	--color-accent-20: var(--ectoplasm-green-20);
	--color-accent-30: var(--ectoplasm-green-30);
	--color-accent-40: var(--ectoplasm-green-40);
	--color-accent-50: var(--ectoplasm-green-50);
	--color-accent-60: var(--ectoplasm-green-60);
	--color-accent-70: var(--ectoplasm-green-70);
	--color-accent-80: var(--ectoplasm-green-80);
	--color-accent-90: var(--ectoplasm-green-90);
	--color-accent-100: var(--ectoplasm-green-100);
	--color-masterbar-background: var(--theme-base-color);
	--color-masterbar-border: #634c84; /* $adminbar-avatar-frame */
	--color-masterbar-text: var(--theme-text-color);
	--color-masterbar-submenu-text: var(--theme-submenu-text-color);
	--color-masterbar-icon: var(--theme-icon-color);
	--color-masterbar-highlight: var(--theme-highlight-color);
	--color-masterbar-unread-dot-background: var(--theme-notification-color);

	--color-masterbar-item-hover-background: var(--theme-submenu-background-color);
	--color-masterbar-item-active-background: var(--theme-submenu-background-color);
	--color-masterbar-item-new-editor-background: var(--studio-gray-50);
	--color-masterbar-item-new-editor-hover-background: var(--studio-gray-60);

	--color-masterbar-toggle-drafts-editor-background: var(--studio-gray-60);
	--color-masterbar-toggle-drafts-editor-hover-background: var(--studio-gray-40);
	--color-masterbar-toggle-drafts-editor-border: var(--studio-gray-10);

	/* Sidebar */
	--color-sidebar-background: var(--theme-base-color);
	--color-sidebar-border: var(--theme-submenu-background-color);
	--color-sidebar-text: var(--theme-text-color);
	--color-sidebar-text-alternative: var(--theme-text-color);
	--color-sidebar-gridicon-fill: var(--theme-icon-color);

	/* Sidebar Selected */
	--color-sidebar-menu-selected-background: var(--theme-highlight-color);
	--color-sidebar-menu-selected-text: var(--theme-text-color);
	--color-sidebar-menu-hover-background: var(--theme-highlight-color);
	--color-sidebar-menu-hover-text: var(--theme-text-color);

	/* Sidebar Submenu - Nav Unification */
	--color-sidebar-submenu-background: var(--theme-submenu-background-color);
	--color-sidebar-submenu-text: var(--theme-submenu-text-color);
	--color-sidebar-submenu-hover-background: transparent;
	--color-sidebar-submenu-hover-text: var(--theme-highlight-color);
	--color-sidebar-submenu-selected-hover-text: var(--theme-highlight-color);

	/* Sidebar Submenu - Nav Redesign */
	--color-navredesign-sidebar-submenu-text: var(--color-sidebar-submenu-text);
	--color-navredesign-sidebar-menu-selected-text: var(--theme-text-color);
	--color-navredesign-sidebar-submenu-hover-text: var(--theme-highlight-color);
	--color-navredesign-sidebar-submenu-selected-hover-text: var(--theme-highlight-color);

	/* Gutenberg components */
	--wp-admin-theme-color: var(--color-accent);
	--wp-admin-theme-color-darker-20: var(--color-accent-60);
}
