/*
Ocean is a color scheme ported from wp-admin.

WP Admin Unthemed definitions:

$text-color: #fff !default;
$base-color: #23282d !default;
$icon-color: hsl( hue( $base-color ), 7%, 95% ) !default;
$highlight-color: #0073aa !default;
$notification-color: #d54e21 !default;

WP Admin Ocean Definition:

$base-color: #738e96;
$icon-color: #f2fcff;
$highlight-color: #9ebaa0;
$notification-color: #aa9d88;
$low-contrast-theme: "true";

Used Studio-celadon for primary+accent colors.
  - WP Highlight color Hue: 193.71
  - Celadon Hue range: 160-170
  - Wordpress Blue hue range: 197 - 205
Wordpress Blue is technically a closer hue but looked worse, in my
opinion.  Celadon seems to match the ocean colors better.
*/

.color-scheme.is-ocean {
	/* Variables used in Calypso Ectoplasm */
	--theme-text-color: #fff; /* $text-color */
	--theme-base-color: #738e96; /* $base-color */
	--theme-submenu-text-color: #d5dde0; /* $menu-submenu-text */
	--theme-submenu-background-color: #627c83; /* $menu-submenu-background */
	--theme-icon-color: #f2fcff; /* $icon-color */
	--theme-highlight-color: #9ebaa0; /* $highlight-color */
	--theme-notification-color: #aa9d88; /* Direct from wp-admin */

	/* Primary */
	--color-primary: var(--theme-highlight-color);
	--color-primary-dark: var(--studio-celadon-70);
	--color-primary-light: var(--studio-celadon-30);
	--color-primary-0: var(--studio-celadon-0);
	--color-primary-5: var(--studio-celadon-5);
	--color-primary-10: var(--studio-celadon-10);
	--color-primary-20: var(--studio-celadon-20);
	--color-primary-30: var(--studio-celadon-30);
	--color-primary-40: var(--studio-celadon-40);
	--color-primary-50: var(--studio-celadon-50);
	--color-primary-60: var(--studio-celadon-60);
	--color-primary-70: var(--studio-celadon-70);
	--color-primary-80: var(--studio-celadon-80);
	--color-primary-90: var(--studio-celadon-90);
	--color-primary-100: var(--studio-celadon-100);
	--color-accent: var(--theme-highlight-color);
	--color-accent-dark: var(--studio-celadon-70);
	--color-accent-light: var(--studio-celadon-30);
	--color-accent-0: var(--studio-celadon-0);
	--color-accent-5: var(--studio-celadon-5);
	--color-accent-10: var(--studio-celadon-10);
	--color-accent-20: var(--studio-celadon-20);
	--color-accent-30: var(--studio-celadon-30);
	--color-accent-40: var(--studio-celadon-40);
	--color-accent-50: var(--studio-celadon-50);
	--color-accent-60: var(--studio-celadon-60);
	--color-accent-70: var(--studio-celadon-70);
	--color-accent-80: var(--studio-celadon-80);
	--color-accent-90: var(--studio-celadon-90);
	--color-accent-100: var(--studio-celadon-100);
	--color-masterbar-background: var(--theme-base-color);
	--color-masterbar-border: #879ea5; /* $adminbar-avatar-frame */
	--color-masterbar-text: var(--theme-text-color);
	--color-masterbar-submenu-text: var(--theme-submenu-text-color);
	--color-masterbar-icon: var(--theme-icon-color);
	--color-masterbar-highlight: var(--theme-highlight-color);
	--color-masterbar-unread-dot-background: var(--theme-notification-color);

	--color-masterbar-item-hover-background: var(--theme-submenu-background-color);
	--color-masterbar-item-active-background: var(--theme-submenu-background-color);
	--color-masterbar-item-new-editor-background: var(--studio-gray-50);
	--color-masterbar-item-new-editor-hover-background: var(--studio-gray-60);

	--color-masterbar-toggle-drafts-editor-background: var(--studio-gray-60);
	--color-masterbar-toggle-drafts-editor-hover-background: var(--studio-gray-40);
	--color-masterbar-toggle-drafts-editor-border: var(--studio-gray-10);

	/* Sidebar */
	--color-sidebar-background: var(--theme-base-color);
	--color-sidebar-border: var(--theme-submenu-background-color);
	--color-sidebar-text: var(--theme-text-color);
	--color-sidebar-text-alternative: var(--theme-text-color);
	--color-sidebar-gridicon-fill: var(--theme-icon-color);

	/* Sidebar Selected */
	--color-sidebar-menu-selected-background: var(--theme-highlight-color);
	--color-sidebar-menu-selected-text: var(--theme-text-color);
	--color-sidebar-menu-hover-background: var(--theme-highlight-color);
	--color-sidebar-menu-hover-text: var(--theme-text-color);

	/* Sidebar Submenu - Nav Unification */
	--color-sidebar-submenu-background: var(--theme-submenu-background-color);
	--color-sidebar-submenu-text: var(--theme-submenu-text-color);
	--color-sidebar-submenu-hover-background: transparent;
	--color-sidebar-submenu-hover-text: var(--theme-highlight-color);
	--color-sidebar-submenu-selected-hover-text: var(--theme-highlight-color);

	/* Sidebar Submenu - Nav Redesign */
	--color-navredesign-sidebar-submenu-text: var(--color-sidebar-submenu-text);
	--color-navredesign-sidebar-menu-selected-text: var(--theme-text-color);
	--color-navredesign-sidebar-submenu-selected-text: var(--theme-text-color);
	--color-navredesign-sidebar-submenu-hover-text: var(--theme-highlight-color);
	--color-navredesign-sidebar-submenu-selected-hover-text: var(--theme-highlight-color);

	/* Gutenberg components */
	--wp-admin-theme-color: var(--color-accent);
	--wp-admin-theme-color-darker-20: var(--color-accent-60);
}
