{"name": "@automattic/verbum-block-editor", "version": "1.0.0", "description": "A minimal Gutenberg editor form WPCOM and Jetpack comments.", "homepage": "https://github.com/Automattic/wp-calypso", "license": "GPL-2.0-or-later", "author": "Automattic Inc.", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "calypso:src": "src/index.ts", "exports": {".": {"calypso:src": "./src/index.ts", "types": "./dist/types/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "sideEffects": ["*.css", "*.scss"], "repository": {"type": "git", "url": "git+https://github.com/Automattic/wp-calypso.git", "directory": "packages/verbum-block-editor"}, "publishConfig": {"access": "public"}, "bugs": "https://github.com/Automattic/wp-calypso/issues", "types": "dist/types", "scripts": {"clean": "rm -rf dist", "build": "NODE_ENV=production yarn dev", "upload": "yarn build && yarn translate && rsync -ahz --delete --exclude='.*' dist/ wpcom-sandbox:/home/<USER>/public_html/widgets.wp.com/verbum-block-editor", "build:app": "calypso-build", "build:package": "tsc --build ./tsconfig.json ./tsconfig-cjs.json && copy-assets", "dev": "yarn run calypso-apps-builder --localPath dist --remotePath /home/<USER>/public_html/widgets.wp.com/verbum-block-editor", "watch": "tsc --build ./tsconfig.json --watch", "prepare": "yarn build:package", "translate": "node ./bin/build-languages.js"}, "dependencies": {"@automattic/calypso-apps-builder": "workspace:^", "@automattic/languages": "workspace:^", "@types/wordpress__block-editor": "^11.5.16", "@wordpress/base-styles": "^5.23.0", "@wordpress/block-editor": "^14.18.0", "@wordpress/block-library": "^9.23.0", "@wordpress/blocks": "^14.12.0", "@wordpress/commands": "^1.23.0", "@wordpress/components": "^29.9.0", "@wordpress/data": "^10.23.0", "@wordpress/element": "^6.23.0", "@wordpress/format-library": "^5.23.0", "@wordpress/i18n": "^5.23.0", "@wordpress/icons": "^10.23.0", "@wordpress/is-shallow-equal": "^5.23.0", "@wordpress/keycodes": "^4.23.0", "@wordpress/primitives": "^4.23.0", "@wordpress/rich-text": "^7.23.0", "@wordpress/url": "^4.23.0"}, "devDependencies": {"@automattic/calypso-build": "workspace:^", "@automattic/calypso-color-schemes": "workspace:^", "@automattic/calypso-typescript-config": "workspace:^", "@types/wordpress__blocks": "^12.5.17", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@wordpress/eslint-plugin": "^22.9.0", "@wordpress/stylelint-config": "^23.15.0", "stylelint": "^16.2.1", "stylelint-scss": "^6.4.0", "typescript": "^5.8.3"}, "peerDependencies": {"@babel/core": "*", "@wordpress/data": "^10.23.0", "@wordpress/element": "^6.23.0", "@wordpress/i18n": "^5.23.0", "debug": "^4.4.1", "eslint": "*", "postcss": "*", "react": "^18.3.1", "react-dom": "^18.3.1", "redux": "^5.0.1", "stylelint": "*", "stylelint-scss": "^6.4.0", "webpack": "*"}, "private": true}