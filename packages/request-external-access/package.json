{"name": "@automattic/request-external-access", "version": "1.0.1", "description": "Utility for requesting authorization of sharing services.", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "calypso:src": "src/index.js", "exports": {".": {"calypso:src": "./src/index.js", "types": "./dist/types/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "sideEffects": false, "types": "dist/types", "keywords": ["wordpress", "access", "popup"], "author": "Automattic Inc.", "homepage": "https://github.com/Automattic/wp-calypso/tree/HEAD/packages/request-external-access", "license": "GPL-2.0-or-later", "repository": {"type": "git", "url": "git+https://github.com/Automattic/wp-calypso.git", "directory": "packages/request-external-access"}, "publishConfig": {"access": "public"}, "bugs": "https://github.com/Automattic/wp-calypso/issues", "files": ["dist", "src"], "scripts": {"clean": "rm -rf dist", "build": "run -T transpile", "prepack": "yarn run clean && yarn run build"}, "dependencies": {"@automattic/popup-monitor": "workspace:^", "@babel/runtime": "^7.27.1", "debug": "^4.4.1"}, "devDependencies": {"@automattic/calypso-typescript-config": "workspace:^"}}