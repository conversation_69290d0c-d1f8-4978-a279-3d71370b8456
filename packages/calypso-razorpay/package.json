{"name": "@automattic/calypso-razorpay", "version": "0.0.1", "description": "A set of helper functions and components for using Razorpay on WordPress.com.", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "types": "dist/types/index.d.ts", "calypso:src": "index.ts", "sideEffects": false, "scripts": {"clean": "tsc --build ./tsconfig.json ./tsconfig-cjs.json --clean && rm -rf dist", "build": "tsc --build ./tsconfig.json ./tsconfig-cjs.json", "prepack": "yarn run clean && yarn run build", "watch": "tsc --build ./tsconfig.json --watch"}, "files": ["dist", "src"], "keywords": ["checkout", "payments", "automattic"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/Automattic/wp-calypso.git", "directory": "packages/calypso-stripe"}, "author": "Automattic Inc.", "license": "GPL-2.0-or-later", "bugs": "https://github.com/Automattic/wp-calypso/issues", "homepage": "https://github.com/Automattic/wp-calypso/tree/HEAD/packages/calypso-stripe#readme", "dependencies": {"debug": "^4.4.1", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@automattic/calypso-typescript-config": "workspace:^", "typescript": "^5.8.3"}}