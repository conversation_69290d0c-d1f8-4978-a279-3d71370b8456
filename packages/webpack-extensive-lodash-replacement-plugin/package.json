{"name": "@automattic/webpack-extensive-lodash-replacement-plugin", "private": true, "version": "0.0.2", "author": "Automattic Inc.", "description": "Webpack plugin to replace lodash, lodash/foo and lodash.foo imports with lodash-es.", "keywords": ["Webpack", "lodash"], "homepage": "https://github.com/Automattic/wp-calypso/tree/HEAD/packages/webpack-extensive-lodash-replacement-plugin", "license": "GPL-2.0-or-later", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/Automattic/wp-calypso.git", "directory": "packages/webpack-extensive-lodash-replacement-plugin"}, "bugs": "https://github.com/Automattic/wp-calypso/issues", "peerDependencies": {"webpack": "^5.99.8"}, "dependencies": {"semver": "^7.7.2"}, "devDependencies": {"@automattic/calypso-typescript-config": "workspace:^"}}