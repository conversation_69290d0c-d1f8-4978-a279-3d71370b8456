{"name": "@automattic/calypso-babel-config", "version": "1.0.0", "description": "Shared babel configuration used in Calypso projects.", "keywords": ["babel", "build", "calypso"], "author": "Automattic Inc.", "homepage": "https://github.com/Automattic/wp-calypso", "license": "GPL-2.0-or-later", "main": "./config.js", "repository": {"type": "git", "url": "git+https://github.com/Automattic/wp-calypso.git", "directory": "packages/calypso-babel-config"}, "publishConfig": {"access": "public"}, "bugs": "https://github.com/Automattic/wp-calypso/issues", "dependencies": {"@automattic/babel-plugin-i18n-calypso": "workspace:^", "@automattic/babel-plugin-preserve-i18n": "workspace:^", "@automattic/babel-plugin-transform-wpcalypso-async": "workspace:^", "@babel/cli": "^7.27.2", "@babel/core": "^7.27.1", "@babel/helpers": "^7.27.1", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-transform-react-jsx": "^7.27.1", "@babel/plugin-transform-runtime": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@babel/register": "^7.27.1", "@babel/runtime": "^7.27.1", "@emotion/babel-plugin": "^11.11.0", "@wordpress/babel-plugin-import-jsx-pragma": "^5.23.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-transform-react-remove-prop-types": "^0.4.24"}}