// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`webpack-inline-constant-exports-plugin should produce expected output: Output bundle should match snapshot 1`] = `
"\\"use strict\\";
(() => {
var exports = {};
exports.id = \\"main\\";
exports.ids = [\\"main\\"];
exports.modules = {

/***/ \\"./index.js\\":
/***/ (() => {


;// ./constants2.js
/*
 * The export is eligible for inlining, but the module is not used directly but re-exported from \`./export.js\`.
 * Re-exporting is not supported, therefore no inlining should happen.
 */
const FOO = 'bar';

;// ./paths.js
/*
 * The export is eligible for inlining, but the module is not specified in the plugin config.
 * Therefore no inlining should happen.
 */
const HOME_PATH = '/';

;// ./plans.js
/*
 * Export two plan constants and a a constant array of all plans. The array should not be inlined
 * and the module should stay in the dependency graph.
 */
const BLOGGER = 'BLOGGER_PLAN';
const PREMIUM = 'PREMIUM_PLAN';
/* harmony default export */ const plans = ([ BLOGGER, PREMIUM ]);

;// ./index.js






console.log( /* inline */ 'PLANS_REQUEST', /* inline */ 'PLANS_RECEIVE' );
console.log( /* inline */ 'BLOGGER_PLAN', /* inline */ 'PREMIUM_PLAN', plans );
console.log( /* inline */ 42, /* inline */ 3.14159, /* inline */ true, /* inline */ false, /* inline */ null );
console.log( HOME_PATH );
console.log( FOO );


/***/ })

};
;

// load runtime
var __webpack_require__ = require(\\"./runtime~main.js\\");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__(\\"./index.js\\"));

})();"
`;
