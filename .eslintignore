.cache/
.vscode
.yarn
/assets/
/build/
dist/
/public/
/vendor/
node_modules/
!.eslintrc.js
/client/server/devdocs/search-index.js
/packages/webpack-rtl-plugin/test/dist*/
/packages/wp-babel-makepot/test/examples
/desktop/build/
/desktop/client/
/desktop/config/
/desktop/public/
/desktop/release/
/client/state/data-layer/wpcom/activity-log/update-credentials/vendor/
storybook-static/

# Built packages
/packages/*/dist/
packages/calypso-color-schemes/src/__color-studio
packages/calypso-color-schemes/css
packages/calypso-color-schemes/js
packages/calypso-color-schemes/root-only

#
# Eslint won't lint json, but prettier shares this ignore
# Don't operate on these files
#
/stats.json
/chart.json
/style.json
/client/stats.json
/client/chart.json
/client/style.json
/client/server/devdocs/components-usage-stats.json
/client/server/devdocs/proptypes-index.json
/client/server/bundler/assets.json
/cached-requests.json
/yarn-json-output.json
