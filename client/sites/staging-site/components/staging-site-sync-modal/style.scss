/*
 * Temporary style for the PoC
 * TODO: Remove this file once the PoC is complete
*/

.staging-site-card {
	padding-bottom: 48px;
	&.confirmation-input {
		padding-bottom: 100px;
	}
	.card {
		padding: 24px 22px;
	}
	.hidden {
		display: none;
	}

	&__back-button.components-button.is-link {
		color: #000;
		padding: 12px 0;
		text-decoration: none;
		margin: 4px 0;

		svg {
			margin-right: 2px;
		}
	}

	.status-card__header-content {
		align-items: center;
		display: flex;
		// Given the layout is mobile-first, reverse the div arrangement by default
		flex-direction: column-reverse;
	}

	.status-card__learn-about {
		font-size: $font-body-small;
		font-weight: 400;
		text-decoration-line: underline;
	}

	.status-card__learn-about a {
		color: var( --studio-gray-80 );
	}

	// We are using the deprecated mixin to match the when the mobile navigation appears
	// which is at 660px instead of the 600px break-small breakpoint.
	@include breakpoint-deprecated( '>660px' ) {
		&__back-button.components-button.is-link {
			padding: 0 0 12px 0;
			margin: 0;
		}

		.status-card__header-content {
			// Return to the default div arrangement on the desktop
			flex-direction: row;
			flex-grow: 1;
		}

		.status-card__learn-about {
			margin-left: auto;
		}
	}

	&__header {
		padding-left: 2px;
		padding-right: 2px;

		&.daily-backup-status {
			box-shadow: none;
			margin-bottom: 0;
		}
	}

	&__body {
		overflow-x: auto;
		overflow-y: autodd;
		padding-bottom: 14px;
	}

	.components-checkbox-control {
		margin-left: 4px;
		font-size: $font-body-small;
	}

	.components-input-control__container {
		margin: 2px;
	}

	.file-browser-node {
		margin-top: 2px;
		margin-left: 2px;
		min-width: fit-content;

		&.is-root {
			margin-left: 0;
		}

		&.wordpress > .file-card .file-card__actions {
			margin-top: 0;
		}

		&__loading {
			&.placeholder {
				height: 26px;
				margin: 6px;
				@include placeholder();
			}
		}

		&__contents {
			display: flex;
			flex-direction: column;
			background-color: #fff;
			padding-left: 26px;
		}

		&.is-root > .file-browser-node__contents {
			padding-left: 0;
		}

		.file-browser-node__title {
			white-space: nowrap;
			overflow: hidden;
		}

		// Move directory dropdown icons to the right
		&.dir > .file-browser-node__item .components-button > :first-child {
			order: 1;
		}

		.components-button.has-icon.has-text svg {
			width: 22px;
			height: 22px;
			margin-right: 4px;
			fill: var( --theme-base-color );
		}

		.components-button {
			border: 0;
			font-size: $font-body-small;
			font-weight: normal;
			height: 32px;
			color: var( --theme-base-color );
		}

		.is-alternate {
			background-color: var( --studio-white );
		}

		.file-card {
			background: var( --studio-gray-0 );
			margin: 4px 0;
			padding-top: 20px;
			padding-bottom: 20px;
			max-width: 100%;

			span {
				font-size: $font-body-small;
			}

			@include break-mobile {
				&__detail-group {
					display: flex;
					flex-wrap: wrap;
					justify-content: space-between;
				}
			}

			&__details,
			&__actions {
				margin-left: 24px;
				margin-right: 24px;
			}

			&__actions {
				display: flex;
				gap: 1rem;
				margin-top: 16px;
			}

			&__label {
				font-weight: bold;
			}

			&__prepare-download-spinner {
				display: inline-block;
				margin-right: 6px;
				margin-top: -3px;
				vertical-align: middle;
			}

			&__preview {
				margin: 16px auto 0;
				padding: 0 8px;
				max-width: 620px;
				text-align: center;

				&--is-loading {
					margin-bottom: 0;
					padding: 0;
					max-width: 100%;

					.placeholder {
						height: 100px;
						margin: 0;
					}
				}

				audio,
				video,
				img {
					max-width: 100%;
				}

				&.image,
				&.video {
					padding: 0;
					margin-bottom: -20px;
				}

				&.code,
				&.text {
					margin-bottom: -12px;
					text-align: initial;
				}

				pre {
					background-color: #fff;
					font-size: 0.75rem;
					margin: 0;
					max-height: 400px;
				}
			}

			&__preview-sensitive {
				background-color: var( --studio-gray-60 );
				color: #fff;
				margin: 16px auto 0;
				max-width: 100%;
				padding: 20px 8px;
				text-align: center;

				p {
					font-size: 0.875rem;
				}
			}
		}

		.form-checkbox {
			margin: 8px;
			&.mixed {
				background-color: var( --studio-gray-10 );
			}
		}
	}

	.file-browser-header {
		padding: 0 2px;
		border-bottom: 1px solid var( --studio-gray-5 );
		.file-browser-header__action-buttons .button {
			padding: 8px 32px;
			margin-right: 16px;
		}
		.file-browser-header__selecting {
			display: none;
		}
	}

	.file-browser-header .components-checkbox-control,
	.file-browser-node .components-checkbox-control {
		float: left;

		.components-checkbox-control__input-container {
			margin: 8px;
			line-height: 0;
			width: 16px;
			height: 16px;
		}

		.components-checkbox-control__input {
			height: 16px;
			vertical-align: middle;
			width: 16px;
			min-width: 16px;
		}

		svg.components-checkbox-control__checked,
		svg.components-checkbox-control__indeterminate {
			height: 20px;
			width: 20px;
		}
	}
}

.staging-site-card__footer {
	border-top: 1px solid var(--studio-gray-5);
	position: absolute;
	bottom: 0;
	width: 100%;
	left: 0;
	padding: 16px 32px 16px;
	background: #fff;
}
