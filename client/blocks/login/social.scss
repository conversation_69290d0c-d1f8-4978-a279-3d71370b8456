@import "@automattic/onboarding/styles/mixins";
@import "@wordpress/base-styles/breakpoints";
@import "@wordpress/base-styles/mixins";

.wp-login__container .is-social-first .login__form span.last-used-authentication-method {
	margin-bottom: 5px;
	font-size: 0.875rem;
	font-weight: 600;

	& + .social-buttons__button.magic-login-link > span.social-buttons__service-name {
		max-width: 295px;
		margin-right: -10px;
	}
}

.auth-form__social.is-login {
	margin-bottom: 0;

	/**
	* Social First Logins
	*/
	&.is-social-first {
		padding: 0 0 0 1px;

		.auth-form__social-buttons {
			display: flex;
			flex-direction: column;

			.auth-form__social-buttons-container {
				gap: 16px;
				display: flex;
				flex-direction: column;
				width: 100%;
			}
		}
	}

	padding: 16px;

	@include break-mobile {
		padding: 24px 0;
	}
}

.auth-form__social {
	box-shadow: none;

	.auth-form__social-buttons {
		display: flex;
		flex-direction: column;
	}

	.auth-form__social-buttons-container {
		gap: 10px;
		display: flex;
		flex-direction: column;
	}

	/**
	* Social First Logins
	*/
	&.is-social-first {
		padding: 0 0 0 1px;

		.auth-form__social-buttons {
			display: flex;
			flex-direction: column;

			.auth-form__social-buttons-container {
				gap: 16px;
				display: flex;
				flex-direction: column;
				width: 100%;
			}
		}
	}

	/**
	* Jetpack Login
	*/
	@at-root .is-jetpack-login & {
		padding: 16px;

		@include break-mobile {
			padding: 24px;
		}
	}

	/**
	* Crowdsignal Login
	*/
	@at-root .crowdsignal & {
		.auth-form__social-buttons .auth-form__social-buttons-container .social-buttons__button {
			display: none;

			&.google {
				display: inline-flex;

				@include break-medium {
					margin-bottom: 40px;
				}
			}
		}
	}
}
