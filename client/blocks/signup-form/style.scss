@import "@wordpress/base-styles/breakpoints";
@import "@wordpress/base-styles/mixins";
@import "@automattic/onboarding/styles/mixins";
@import "../../assets/stylesheets/shared/mixins/breakpoints";

.signup-form-social-first {
	display: grid;
	grid-template-areas: "stack";

	&-screen {
		grid-area: stack;
		visibility: hidden;

		&.visible {
			visibility: visible;
		}
	}

	.signup-form__passwordless-email {
		transition: none !important; // The default form element transition breaks the visibility transition between steps.
	}
}

.signup-form .signup-form__input.form-text-input[type] {
	margin-bottom: 20px;
	transition: none;

	&.is-error,
	&[type="password"],
	&[name="password"] {
		margin-bottom: 0;
		text-align: left;
		/*!rtl:ignore*/
		direction: ltr;
	}

	&[name="username"] {
		text-align: left;
		/*!rtl:ignore*/
		direction: ltr;
	}
}

.signup-form__terms-of-service-link {
	font-size: $font-body-extra-small;
	margin: 0 0 20px;
	text-align: left;

	a {
		@include breakpoint-deprecated(">480px") {
			white-space: pre;
		}
	}
}

.auth-form__social {
	max-width: 400px;
	box-sizing: border-box;
	width: 100%;
	// Below is the same as .card.logged-out-form
	margin: 0 auto 16px;
	padding: 16px;

	p {
		font-size: $font-body-small;
		color: var(--color-text-inverted);
		margin: 0 0 12px;
		text-align: center;

		&:last-child {
			margin: 10px 0 0;
		}
	}
}

.auth-form__social-buttons-tos a {
	text-decoration: underline;
}

.signup-form__passwordless-form-wrapper {
	.signup-form__terms-of-service-link {
		margin: 4px 0;
	}

	.logged-out-form__footer {
		margin-top: 0;
	}

	.validation-fieldset__validation-message {
		min-height: auto;
	}
}

.signup-form__recaptcha-tos {
	display: none;
	padding: 20px 10px 10px;
	font-size: $font-body-small;
	color: var(--studio-blue-5);
	text-align: center;

	p {
		margin: 0;
		padding-top: 9px;
	}

	a {
		color: var(--studio-blue-5);
		text-decoration: underline;
	}
}

// Uses the width of the columns to calculate how wide an element that spans all columns needs to be
// The width variable is set in signup/style.scss and changes according to break points
.signup-form__span-columns {
	box-sizing: border-box;
	margin-left: 16px;
	margin-right: 16px;

	// 32px is the column margins
	width: calc(var(--signup-form-column-max-width) - 32px);

	@include break-medium {
		// 2 columns + the 60px wide "or" element
		width: calc(var(--signup-form-column-max-width) * 2 + 60px - 32px);
	}
}

// Replace recaptcha badge with ToS text and space
// everything out a little more.
@media (max-width: 660px) {
	.signup-form__recaptcha-tos {
		display: block;
	}

	.grecaptcha-badge {
		visibility: hidden;
	}

	.signup-form.is-showing-recaptcha-tos {
		.auth-form__social {
			padding-bottom: 28px;
		}

		.logged-out-form__links::before {
			margin-bottom: 16px;
		}
	}
}

body.is-section-signup,
body.is-section-accept-invite {
	.signup__step.is-user-social,
	// ::::: Onboarding_PM_Passwordless_Experiment ::::::
	// This selector is part of an experiment which is supposed to be cleaned up
	// https://github.com/Automattic/wp-calypso/pull/83886
	.is-onboarding-pm .signup__step.is-user.is-passwordless-experiment {
		.signup-form-social-first {
			width: 327px;
			margin: 0 auto;

			.signup-form__notice {
				width: 327px;
				margin-left: 0;
				margin-right: 0;

				&.is-transparent-info {
					color: var(--studio-gray-80, #2c3338);
				}
			}

			.auth-form__social {
				padding: 0 !important;
			}

			.signup-form-social-first__tos-link,
			.signup-form-social-first__email-tos-link {
				color: var(--studio-gray-60, #646970);
				font-family: "SF Pro Text", sans-serif;
				font-size: 0.875rem;
				font-style: normal;
				font-weight: 400;
				line-height: 20px;
				margin-top: 0;
				margin-bottom: 2rem;

				a {
					color: var(--studio-gray-50, #646970);
					text-decoration-line: underline;
				}
			}

			.signup-form-social-first__tos-link {
				text-align: center;
			}
		}

		.signup-form-social-first-email {
			.card {
				box-shadow: none;
				padding-left: 0;
				padding-right: 0;
				background-color: unset;

				button:not(.is-borderless, .signup-form__domain-suggestion-confirmation) {
					display: block;
					max-width: 327px;
					margin: 0 auto;
				}

				button.signup-form__domain-suggestion-confirmation {
					color: var(--color-link);
					cursor: pointer;
					text-decoration: none;

					&:hover {
						color: var(--color-link-dark);
					}
				}

				button.is-borderless {
					color: var(--color-link);
					padding: 0;

					&:hover {
						color: var(--color-link-dark);
					}
				}
			}

			.form-label {
				color: var(--studio-gray-60, #50575e);
				font-family: "SF Pro Text", sans-serif;
				font-size: 0.875rem;
				font-style: normal;
				font-weight: 500;
				line-height: 20px;
			}

			.logged-out-form__footer {
				margin-top: 12px;
				padding-top: 0;
			}

			button.back-button {
				color: #1d2327;
				text-align: center;
				font-family: "SF Pro Text", sans-serif;
				font-size: 0.875rem;
				font-style: normal;
				font-weight: 500;
				line-height: 20px;
				display: block;
				margin: 0 auto;
				text-underline-offset: 5px;
				position: relative;
				top: 0;

				&:hover {
					color: var(--studio-blue-50, #0675c4);
				}

				&:focus {
					outline: 1px dashed var(--studio-blue-50, #0675c4);
					outline-offset: 3px;
					box-shadow: none;
					border-radius: 0;
				}
			}

			button.button:not(.is-borderless) {
				border-radius: 4px;

				&:focus {
					box-shadow: none;
					outline: 2px solid var(--studio-blue-60, #055d9c);
					outline-offset: 1px;
				}
			}
		}
	}
}

.card.auth-form__social.is-signup {
	background: inherit;
	margin-bottom: 0;

	.auth-form__social-buttons-container {
		gap: 10px;
		display: flex;
		flex-direction: column;
	}

	/**
	* Social First Signups
	*/
	&.is-social-first {
		box-shadow: none;
		padding: 0 0 0 1px;

		.auth-form__social-buttons {
			display: flex;
			flex-direction: column;

			.auth-form__social-buttons-container {
				gap: 16px;
				display: flex;
				flex-direction: column;
				width: 100%;
			}
		}
	}

	/**
	* Jetpack Signup
	*/
	@at-root .is-jetpack-login & {
		padding: 16px;

		.auth-form__social-buttons .auth-form__social-buttons-container .social-buttons__button {
			border-radius: 2px;

			> svg {
				margin-right: 0;
			}

			span {
				font-weight: 400;
				font-family: inherit;
				letter-spacing: normal;
				line-height: 22px;
				margin-left: 9px;
				margin-right: 0;
			}
		}

		@include break-mobile {
			padding: 24px;
		}
	}

	/**
	* Crowdsignal Signup
	*/
	@at-root .crowdsignal & {
		.auth-form__social-buttons .auth-form__social-buttons-container .social-buttons__button {
			display: none;

			&.google {
				display: inline-flex;

				@include break-medium {
					margin-bottom: 40px;
				}
			}
		}
	}
}

.signup-form.is-horizontal .calypso-notice.signup-form__notice {
	width: 100%;
	margin: auto;
	margin-bottom: 16px;
}
