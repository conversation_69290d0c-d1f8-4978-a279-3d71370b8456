@import '@wordpress/base-styles/breakpoints';
@import '@wordpress/base-styles/mixins';

.summer-special-banner-wrapper {
	// Default: no height constraint

	// When the banner is fixed, maintain space where it would be
	&.summer-special-banner-wrapper--fixed {
		min-height: 48px; // 12px padding top + 24px min-height + 12px padding bottom
		margin: 0 20px 24px;

		@media ( min-width: 780px ) {
			margin: 0 0 24px;
		}
	}
}

.summer-special-banner {
	margin: 0 20px 24px;
	color: var( --color-text-inverted );
	border-radius: 0;
	box-shadow: none;

	background: linear-gradient( to right, rgba( 68, 88, 228, 1 ) 0%, rgba( 6, 158, 8, 1 ) 100% );
	padding: 3px;

	@media ( min-width: 781px ) {
		margin: 0 0 24px;
	}

	// Fixed positioning for signup version
	&.summer-special-banner--fixed {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 100001;
		margin: 0;

		.layout__secondary:not(:empty) ~ .layout__primary & {
			left: var( --sidebar-width-max );
		}

		@media ( max-width: 781px ) {
			.layout.focus-content .layout__secondary:not(:empty) ~ .layout__primary & {
				left: 0;
			}
		}
	}

	.card {
		background: transparent;
		box-shadow: none;
		margin: 0;
		padding: 0;
	}
}

.summer-special-banner__content {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 17px 55px;
	min-height: 24px;
	position: relative;
	background: var( --color-neutral-100 );
	overflow: hidden;
}

.summer-special-banner__content::before {
	content: '';
	display: block;
	position: absolute;
	top: 50%;
	right: 0;
	transform: translate( 0, -50% );
	width: 100%;
	min-width: 1500px;
	height: 60px;
	background-image: url(calypso/assets/images/plans/promo-banner-artwork.png);
	background-size: 416px 60px;
	background-repeat: no-repeat;
	background-position: 0% 0;
}

@media (max-width: 1170px) {
	.summer-special-banner__content::before {
		min-width: 0;
		background-position: -342px 0;
	}
}

@media (max-width: 1455px) {
	body:not(.is-sidebar-collapsed) .layout__secondary:not(:empty) ~ .layout__primary .summer-special-banner__content::before {
		min-width: 0;
		background-position: -342px 0;
	}
}

.summer-special-banner__text {
	flex: 0 1 auto;
	max-width: 710px;
}

.summer-special-banner__title {
	font-size: 14px;
	font-weight: 400;
	margin: 0;
	color: var( --color-text-inverted );
	line-height: 1.6;

	@include break-small {
		font-size: 15px;
	}
}

.summer-special-banner__description {
	display: none; // Hide description to match single-line design
}

.summer-special-banner__actions {
	flex-shrink: 0;
}

.summer-special-banner__cta {
	color: var( --color-text-inverted ) !important;
	font-weight: 400;
	font-size: 14px;
	text-decoration: underline !important;
	text-underline-offset: 2px;
	white-space: nowrap;
	margin-left: 6px;

	&:hover {
		color: var( --color-text-inverted ) !important;
		text-decoration: none !important;
	}

	@include break-small {
		font-size: 14px;
	}
}

.summer-special-banner__close {
	position: absolute;
	top: 50%;
	right: 12px;
	transform: translateY( -50% );
	color: var( --color-text-inverted ) !important;
	opacity: 0.9;
	min-width: 20px !important;
	height: 20px !important;
	padding: 0 !important;

	&:hover {
		color: var( --color-text-inverted ) !important;
		opacity: 1;
		background: transparent !important;
	}

	@include break-small {
		right: 16px;
		min-width: 24px !important;
		height: 24px !important;
	}
}
