@import "@wordpress/base-styles/breakpoints";
@import "@automattic/components/src/styles/typography";
@import "@automattic/typography/styles/variables";

@font-face {
	font-family: Inter;
	font-weight: 400 700;
	font-style: normal;
	font-named-instance: "Regular";
	src:
		url(https://s0.wp.com/i/fonts/inter/Inter-roman.var.woff2?t=woff2-variations&v=3.19) format("woff2-variations"),
		url(https://s0.wp.com/i/fonts/inter/Inter-roman.var.woff2?t=woff2&v=3.19) format("woff2");
}

$inter: Inter, $sans; // for general
$sfProText: "SF Pro Text", $sans; // for sub title
$colorBlack: #1f1f1f;

body.is-section-signup,
body.is-section-login {
	--font-inter:
		"Inter",
		-apple-system,
		system-ui,
		blinkmacsystemfont,
		"Segoe UI",
		"Roboto",
		"Oxygen-Sans",
		"Ubuntu",
		"Cantarell",
		"Helvetica Neue",
		sans-serif;

	&:has(.is-blaze-pro) {

		.layout:not(.dops):not(.is-wccom-oauth-flow) .formatted-header .formatted-header__title {
			color: $colorBlack;
			font-family: var(--font-inter);
			font-size: 2rem;
			font-weight: 700;
			line-height: 40px;
		}

		.wp-login__one-login-layout-heading-text {
			font-family: Inter, -apple-system, system-ui, blinkmacsystemfont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
			font-size: 2rem;
			font-weight: 700;
			line-height: 40px;
		}

		.wp-login__one-login-layout-heading-subtext {
			font-family: "SF Pro Text", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
			font-size: rem(18px);
			font-weight: 500;
			line-height: 25px;
		}
	}
}

.blaze-pro,
.is-blaze-pro { // is-blaze-pro is used by Sign Up flow (for logged in users)
	font-family: $inter;
	--color-white: #fff;
	--color-black: $colorBlack;
	--color-gray: #50575e;

	--color-link: var(--color-blaze-pro);
	--color-link-dark: var(--color-blaze-pro);

	min-height: 100%;

	$login-form-max-width: 327px;

	.masterbar__blaze-pro-nav {
		display: flex;
		flex-direction: row;
		align-items: center;
		margin: 0;
		list-style-type: none;
		width: 100%;

		.masterbar__login-back-link {
			display: flex;
			font-weight: 500;

			@media (max-width: $break-mobile) {
				display: none;
			}
		}
	}

	.masterbar__blaze-pro-mobile-nav {
		display: none;
		flex-direction: row;
		align-items: center;
		margin: 0;
		list-style-type: none;
		width: 100%;
		position: fixed;
		bottom: 0;
		padding: 24px 0;
		box-shadow: inset 0 1px 0 #e2e4e7;
		background-color: var(--color-white);
		z-index: 999;

		@media (max-width: $break-mobile) {
			display: flex;
			padding-inline: 24px;
		}

		.masterbar__login-back-link {
			@media (max-width: $break-mobile) {
				display: block;
			}
		}
	}

	.masterbar__blaze-pro {
		background: transparent;
		border-bottom: none;
		position: absolute;
		height: 72px;
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 28px 24px;

		.masterbar__blaze-pro-nav {
			display: flex;
			flex-direction: row;
			align-items: center;
			margin: 0;
			list-style-type: none;
			width: 100%;
			gap: 12px;

			.masterbar__login-back-link {
				// margin-top: -10px;
				font-weight: 500;

				@media (max-width: $break-mobile) {
					display: none;
				}
			}
		}
	}

	.masterbar__blaze-pro-nav-wrapper {
		width: 100%;
	}

	.masterbar__blaze-pro-nav-item {
		height: 32px;

		button.masterbar__login-back-link {
			color: var(--studio-gray-100);
			display: flex;
			font-weight: 500;
			height: 32px;
			padding: 0;
		}

		.masterbar__blaze-pro-link img {
			height: 32px;
			width: 32px;
		}
	}

	&.is-section-signup::before,
	&.is-section-signup .layout__primary::before {
		display: none;
	}

	.login__body--continue-as-user {
		align-items: center;
		display: flex;
		flex-direction: column;

		.card.auth-form__social.is-login {
			width: 326px;
		}

		.auth-form__separator::before,
		.auth-form__separator::after {
			display: none;
		}
	}

	.continue-as-user {
		margin: 0;
		width: 326px;
		height: unset;

		~ .auth-form__separator {
			margin: 32px auto;
		}

		.continue-as-user__user-info {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			align-self: stretch;
			border: 1px solid #ccc;
			width: 100%;
			box-sizing: border-box;
			padding: 32px 24px 16px 24px;
			position: relative;
		}

		~ .card.auth-form__social.is-login {
			margin: 0;
			padding: 0;
			max-width: initial;
		}


		.continue-as-user__gravatar-link {
			margin: 0 0 8px;
			padding: 0;
			border: 0;
			border-radius: 0;
			width: 100%;
		}

		.gravatar.continue-as-user__gravatar {
			width: 80px;
			height: 80px;
			box-sizing: border-box;
		}

		.continue-as-user__username {
			color: var(--color-black);
			font-family: $inter;
			font-size: 1rem;
			font-weight: 600;
			line-height: 20px;
		}

		.continue-as-user__email {
			color: var(--studio-gray-60);
			font-family: $inter;
			font-size: 0.875rem;
			font-weight: 500;
			line-height: 20px;
			margin: 0;
			text-align: center;
		}

		.continue-as-user__not-you {
			align-items: center;
			bottom: auto;
			display: flex;
			gap: 4px;
			height: 32px;
			justify-content: center;
			margin-bottom: 0;
			padding: 4px 16px;
			position: inherit;
		}

		.continue-as-user__change-user-link {
			font-size: 0.875rem;
			font-weight: 500;
		}

		.continue-as-user__continue-button {
			font-size: rem(13px);
			font-weight: 600;
			margin-top: 24px;
			width: 326px;
		}
	}

	.card.auth-form__social.is-login {
		box-shadow: none;
	}

	p.verification-code-form__help-text {
		color: var(--color-gray);
		font-family: $sfProText !important;
		font-size: rem(18px);
		font-weight: 500;
		line-height: 25px;
		text-align: center;
	}

	.two-factor-authentication__small-print {
		font-size: 0.75rem;
		font-weight: 400;
		padding-top: 4px;
		padding-bottom: 16px;
	}

	// Sign Up styles

	.auth-form__social .auth-form__social-buttons-container {
		margin-bottom: 24px;

		.social-buttons__button {
			> span {
				font-family: $inter;
				font-size: rem(13px);
			}

		}
	}

	.auth-form__social-buttons-tos,
	.signup-form__terms-of-service-link {
		color: var(--studio-gray-60);
		font-size: 0.75rem;
		font-weight: 400;
		line-height: 20px;
		text-align: center;

		a,
		a:visited {
			color: var(--studio-gray-60);
			text-decoration: underline;
		}
	}

	.logged-out-form__link-item.logged-out-form__back-link {
		display: none;
	}

	.logged-out-form__link-item {
		&,
		&:hover {
			color: var(--studio-gray-60);
		}
	}

	.logged-out-form__links {
		align-items: center;
		display: flex;
		flex-direction: row;
		font-family: $sfProText;
		justify-content: center;
		max-width: 360px;

		&::before {
			display: none;
		}

		span {
			color: var(--studio-gray-60);
		}

		a.logged-out-form__link-item,
		a {
			padding: 0;
			text-decoration: underline;
		}

		a.logged-out-form__link-item,
		a,
		span {
			font-size: rem(18px);
			font-weight: 500;
			line-height: 25px;
		}
	}

	// Log in Styles

	.card.login__form,
	.signup-form .logged-out-form,
	.login__lostpassword-form,
	.two-factor-authentication__verification-code-form-wrapper {
		box-shadow: none;

		.form-label:not(.form-label-core-styles) {
			color: #1e1e1e;
			font-size: rem(11px);
			line-height: 16px;
			text-transform: uppercase;
		}

		.form-text-input:not(.form-text-input-core-styles) {
			border: 1px solid #dddddf;

			&:focus,
			&:focus-visible,
			&:focus-within,
			&:hover {
				border: 1px solid var(--color-gray);
				box-shadow: inset 0 0 0 1px var(--color-gray);
				outline: none !important;
			}
		}
	}

	&.layout:not(.is-grav-powered-client),
	.card.login__form,
	.signup-form .logged-out-form,
	.login__lostpassword-form {
		.login .button.is-primary,
		.login .button.is-primary:hover,
		.form-button {
			background: var(--color-blaze-pro);
			border-color: var(--color-blaze-pro);
			color: var(--color-white);
			font-size: rem(13px);
			font-weight: 600;
			line-height: 14px;
			padding: 12px 16px;
		}
	}

	.signup-form .signup-form__input.form-text-input {
		margin-bottom: 12px;
	}

	.login__lostpassword-subtitle {
		width: 360px;
	}
}

// Hacks for Continue-as-a-user for Sign Up (but user is already logged in)
.is-blaze-pro {
	.step-wrapper__content {
		align-items: center;
		display: flex;
		flex-direction: column;
	}

	&.layout:not(.dops) .step-wrapper .step-wrapper__header {
		margin-top: 0;
	}

	// Do not show ToS twice for Sign In...
	&.layout.is-section-login .continue-as-user .auth-form__social-buttons-tos {
		display: none;
	}

	// ... though ToS should be shown for Sign Up (for logged in users)
	&.layout.is-section-signup.is-logged-in .continue-as-user .auth-form__social-buttons-tos {
		margin-top: 24px;
	}
}
