@import "@automattic/typography/styles/fonts";
@import "@automattic/typography/styles/variables";
@import "../../assets/stylesheets/shared/mixins/breakpoints";

.masterbar__oauth-client {
	background-color: var(--color-surface);

	nav {
		display: flex;
		justify-content: space-between;
		width: 100%;
	}

	ul {
		margin: 0;

		li {
			list-style-type: none;
		}
	}

	a {
		text-decoration: none;
	}

	img {
		display: block;
	}
}

.masterbar__oauth-client-main-nav {
	width: 100%;
}

.masterbar__oauth-client-current {
	float: left;
}

.masterbar__oauth-client-logo img {
	max-height: 47px;
}

.masterbar__oauth-client-wpcc-sign-in {
	background-color: rgba(255, 255, 255, 0.15);
	border-radius: 2px;
	color: #fff;
	display: block;
	float: right;
	/* stylelint-disable-next-line declaration-property-unit-allowed-list */
	line-height: 100%;
	margin-right: 4px;
	position: relative;
	top: 4px;

	&:hover {
		background: rgba(255, 255, 255, 0.2);
	}

	&::after {
		clear: both;
		content: " ";
		display: block;
		height: 0;
		visibility: hidden;
	}
}

.masterbar__oauth-client-wpcom {
	color: rgba(255, 255, 255, 0.8);
	cursor: pointer;
	float: left;
	line-height: 32px;
	padding: 3px 10px;

	&:hover {
		color: #fff;
	}

	&:visited {
		color: rgba(255, 255, 255, 0.8);
	}

	.gridicon {
		margin-bottom: 5px;
		margin-right: 5px;
		vertical-align: middle;
	}
}

.dops {
	.locale-suggestions {
		margin-top: 0;
	}
}

.crowdsignal {
	background: var(--color-surface);
	color: var(--studio-gray-100);

	@include breakpoint-deprecated(">660px") {
		background: var(--studio-gray-0);
	}

	.button {
		background-color: var(--studio-gray-0);
		border-radius: 2px;
		border-width: 1px 1px 2px;
		box-shadow: none;
		box-sizing: border-box;
		font-size: $font-body-small;
		font-weight: 600;
		height: 45px;
		line-height: normal;
		padding: 13px 40px 12px;

		&.is-borderless {
			background-color: transparent;
		}

		&.is-primary {
			background-color: var(--color-accent);
			border-color: var(--color-accent-dark);
		}
	}

	.card {
		--color-link-hover: #4ccee4;
		clip-path: unset;
	}
}

.a8c-for-agencies {
	background: var(--studio-gray-0);
	min-height: 100%;

	// make sure jetpack logo is the correct green
	.jetpack-logo__icon-circle {
		fill: var(--studio-jetpack-green-40);
	}

	.a4a-logo {
		width: 200px;
		height: 28px;
	}

	.step-wrapper .formatted-header {
		margin-top: 0;
	}

	.login__form-post-header {
		margin-top: 24px;
	}

	.auth-form__social,
	.login__form,
	.two-factor-authentication__verification-code-form,
	.two-factor-authentication__actions {
		width: 100%;
		max-width: 375px;
	}

	.login__form-action button {
		font-size: $font-body;
		line-height: 23px;
	}

	.masterbar__oauth-client {
		height: unset;

		.masterbar__oauth-client-main-nav {
			display: flex;
			flex-direction: row;
			justify-content: center;

			.masterbar__oauth-client-logo {
				padding: 14px 0;
				line-height: normal;
			}
		}
	}

	.masterbar.masterbar__oauth-client {
		border: 1px solid var(--studio-gray-5);
	}

	.signup__step.is-oauth-2-user {
		display: flex;
		flex-direction: column;
		align-items: center;

		.step-wrapper {
			background: var(--studio-white);
			padding-bottom: 0;
			max-width: 640px;
			border: 1px solid var(--studio-gray-5);
			width: 100%;

			.card.logged-out-form {
				border: none;
				box-shadow: none;
			}

			form {
				max-width: 375px;
				width: 100%;
			}
		}
	}

	.layout__content {
		padding-top: 90px;
	}

	.login__social-tos {
		font-size: $font-body-small;
		line-height: 24px;
	}

	.auth-form__social-buttons {
		margin-top: 16px;
	}

	.card.two-factor-authentication__verification-code-form,
	.card.two-factor-authentication__actions {
		border: none;
		box-shadow: none;
	}
}

// Adding some CSS to cover the --color-primary
// background color in the signup flow for Akismet/IntenseDebate.
.layout.dops.akismet,
.layout.dops.intensedebate {
	background: var(--color-neutral-0);
	min-height: 100%;

	// When completing the Akismet signup flow
	// ensure the text color is legible.
	.signup-processing__content {
		color: var(--color-text);
	}
}

.layout.dops.intensedebate .masterbar__oauth-client-logo img {
	max-height: 30px;
	margin-left: 10px;
}
