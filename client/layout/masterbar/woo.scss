@import url( https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap );
@import "@wordpress/base-styles/colors";
@import "@wordpress/base-styles/breakpoints";
@import "@wordpress/base-styles/mixins";
@import "@automattic/typography/styles/woo-commerce";

$breakpoint-mobile: 660px;

@mixin button-primary {
	background-color: var(--woo-purple-40);
	border-radius: 8px; /* stylelint-disable-line scales/radii */

	&:hover,
	&:focus {
		background-color: var(--woo-purple-60);
	}

	&:disabled {
		background-color: var(--studio-gray-5);
		color: var(--studio-gray-50);
	}
}

@mixin button-secondary {
	border: 2px solid var(--woo-purple-40);
	background-color: transparent;
	border-radius: 8px; /* stylelint-disable-line scales/radii */

	&:hover,
	&:focus {
		background-color: var(--woo-purple-70);
	}

	&:disabled {
		background-color: var(--studio-gray-5);
		color: var(--studio-gray-50);
	}
}

@mixin button-tertiary {
	background-color: transparent;
	color: var(--woo-purple-40);
	border-radius: 8px; /* stylelint-disable-line scales/radii */

	&:hover,
	&:focus {
		background-color: var(--woo-purple-0);
		color: var(--woo-purple-60);
	}

	&:disabled {
		background-color: transparent;
		color: var(--studio-gray-50);
	}
}

@mixin button-size-small {
	padding: 5.5px 16px;
	font-weight: 500;
	font-size: rem(14px);
	line-height: 21px;
}

@mixin button-size-base {
	padding: 12px 24px;
	font-weight: 500;
	font-size: rem(16px);
	line-height: 24px;
}

@mixin button-size-large {
	padding: 14.5px 24px;
	font-weight: 500;
	font-size: rem(18px);
	line-height: 27px;
}

@mixin link-woo {
	color: var(--woo-purple-40);
	text-decoration: underline;
	text-underline-offset: 4px;
	font-weight: 400;

	&:hover,
	&:focus {
		color: var(--woo-purple-60);
	}
}

.woo {
	--woo-purple-40: #966ccf;
	--woo-purple-50: #7f54b3;
	--woo-purple-60: #674399;
	--woo-purple-70: #533582;
	--woo-font-family-header: proxima-nova, sans-serif;
	$woo-label-color: #50575e;
	$woo-gray-100: #101517;
	$woo-gray-40: #787c82;
	$woo-font-size-small: $font-body-extra-small;
	$woo-font-size-base: $font-body-small;
	$woo-font-size-input: $font-body-small;
	$woo-inter-font:  "Inter", -apple-system, system-ui, sans-serif;
	$woo-radius-input: 4px;
	$woo-form-whitespace: 60px;
	$woo-form-whitespace-660: 20px;
	$woo-form-divider-border: 1px solid #e6e6e6;
	$gray-60: #50575e;
	$gray-50: #646970;
	$woo-sfpro-display-font:  "SF Pro Display", -apple-system, system-ui, sans-serif;
	$woo-sfpro-text-font:  "SF Pro Text", -apple-system, system-ui, sans-serif;

	input.form-text-input,
	textarea.form-text-input {
		&:focus,
		&:hover {
			border-color: var(--woo-purple-50);
			box-shadow: 0 0 0 2px var(--woo-purple-50);
		}
	}

	p {
		margin-bottom: 0;
		letter-spacing: 0;
	}

	.button {
		border-radius: 8px; /* stylelint-disable-line scales/radii */
		border: 2px solid var(--woo-purple-60);
		text-decoration: none;
		width: auto;
		padding: 10px 24px;
		line-height: 20px;
	}

	.button.is-primary,
	.login .button.is-primary,
	.magic-login .magic-login__form-action .button.is-primary:not([disabled]) {
		background-color: var(--woo-purple-60);
		border: none;
		color: #fff;
		width: 100%;

		&.is-busy {
			background-image: none;
		}

		&:hover {
			background-color: var(--woo-purple-40);
			color: #fff;
		}

		&:disabled {
			background-color: var(--studio-gray-0);
			color: var(--studio-gray-20);
		}
	}

	.login__form-user-exists-notice.calypso-notice {
		border-radius: 8px; // stylelint-disable-line scales/radii
		overflow: auto;
		padding: 16px 20px;
		background-color: #f6f7f7;

		.calypso-notice__icon-wrapper {
			background-color: #f6f7f7;
			color: #2c3338;
			width: auto;
			padding: 0;
			align-items: flex-start;
		}

		.calypso-notice__dismiss {
			padding: 0;
			background-color: #f6f7f7;
			color: #a7aaad;
		}

		.calypso-notice__content {
			padding: 0 16px;
			background-color: #f6f7f7;
			color: #2c3338;
		}

		.calypso-notice__text {
			color: #2c3338;
			font-size: rem(14px);
			font-style: normal;
			font-weight: 400;
			line-height: 20px; /* 142.857% */
			letter-spacing: -0.24px;


			a,
			a:visited {
				color: var(--woo-purple-60);
				font-size: rem(14px);
				font-style: normal;
				font-weight: 500;
				line-height: 20px;
				letter-spacing: -0.24px;
				text-decoration: none;
			}

			a:hover {
				color: var(--woo-purple-40);
			}
		}
	}

	.masterbar__progress-bar {
		background-color: transparent;
		height: 8px;
		top: 0;
		position: absolute;

		.progress-bar__progress {
			border-radius: 0;
			background-color: var(--color-primary);
		}
	}

	// masterbar styles
	.masterbar__woo {
		background: transparent;
		border-bottom: none;
		position: absolute;
		height: 72px;
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 28px 24px;

		.masterbar__woo-nav {
			display: flex;
			flex-direction: row;
			align-items: center;
			margin: 0;
			list-style-type: none;
			width: 100%;

			.masterbar__login-back-link {
				margin-top: -10px;
				font-weight: 500;

				@media (max-width: $break-mobile) {
					display: none;
				}
			}
		}
	}

	.masterbar__woo-nav-wrapper {
		width: 100%;
	}

	.masterbar__woo-nav-item button.masterbar__login-back-link {
		color: var(--studio-gray-100);
		display: flex;
		font-weight: 500;
		padding: 0;
	}

	.masterbar__woo-mobile-nav {
		display: none;
		flex-direction: row;
		align-items: center;
		margin: 0;
		list-style-type: none;
		width: 100%;
		position: fixed;
		bottom: 0;
		padding: 24px 0;
		box-shadow: inset 0 1px 0 #e2e4e7;
		background-color: #fff;
		z-index: 999;

		@media (max-width: $break-mobile) {
			display: flex;
			padding-inline: 24px;
		}

		.masterbar__login-back-link {
			@media (max-width: $break-mobile) {
				display: block;
			}
		}
	}

	&.is-section-signup .layout__content,
	.layout__content {
		@media (max-width: $break-mobile) {
			padding-top: 50px;
			padding-bottom: 80px;
		}
	}

	.layout__primary .main {
		padding-bottom: 0;
	}

	// login styles
	.login__form,
	.magic-login {
		.button {
			float: none;
			margin: 0;
		}
	}

	.form-password-input .form-password-input__toggle-visibility {
		top: 10px;
	}

	.form-input-validation {
		text-align: left;
	}


	.jetpack-connect__logged-in-form .formatted-header,
	.jetpack-connect__authorize-form .formatted-header,
	.signup-form__woo-wrapper {
		text-align: center;
		margin: 0 auto;
		line-height: 28px;

		h1,
		h3 {
			font-weight: 600;
			font-size: 2.75rem;
			line-height: 52px;

			@media (max-width: $break-mobile) {
				text-align: left;
			}
		}
	}

	.step-wrapper,
	.jetpack-connect__main-logo {
		box-sizing: border-box;
		padding: 60px 0 0;
		width: 100%;

		@media (max-width: $break-mobile) {
			padding-top: 30px;
		}
	}

	.login__social-connect-prompt-logo {
		height: 58px;
		width: 58px;

		&:first-child {
			width: 48px;
			height: 48px;
			margin-right: 3px;
		}

		// move apple icon slightly to the top so it looks more centered
		&.is-apple {
			position: relative;
			top: -3px;
		}
	}

	.login__social-connect-prompt,
	.signup-form,
	.login__two-factor-footer {
		padding-left: $woo-form-whitespace;
		padding-right: $woo-form-whitespace;
		@media (max-width: $break-mobile) {
			padding-left: $woo-form-whitespace-660;
			padding-right: $woo-form-whitespace-660;
		}
	}

	.continue-as-user {
		max-width: 472px;
		margin: 54px auto 0;
		height: unset;
		min-height: 238px;

		~ .card.auth-form__social.is-login {
			max-width: 472px;
		}

		.continue-as-user__gravatar-content {
			pointer-events: auto;
			display: grid;
			grid-template-columns: 56px 1fr;
			grid-template-areas:
				"gravatar username"
				"gravatar email";
			flex-direction: row;
			justify-items: flex-start;
			align-items: center;
			padding: 16px 24px;
			border: 1px solid #c3c4c7;
			border-radius: 4px;
			text-decoration: none;
			margin: 0 0 16px;

			.gravatar.continue-as-user__gravatar {
				width: 40px;
				height: 40px;
				grid-area: gravatar;
			}

			.continue-as-user__username {
				font-size: 1rem;
				font-weight: 600;
				line-height: 24px;
				color: var(--color-text);
				grid-area: username;
				margin-top: 0;
			}

			.continue-as-user__email {
				font-size: 1rem;
				line-height: 24px;
				font-weight: 400;
				color: $gray-60;
				grid-area: email;
				margin-bottom: 0;
				text-overflow: ellipsis;
				width: 100%;
				overflow: hidden;
				white-space: nowrap;
				text-align: left;
			}

		}

		.continue-as-user__user-info {
			.button {
				margin: 0;
			}
		}

		.continue-as-user__not-you {
			margin-bottom: 32px;
			position: initial;
		}
	}

	.logged-out-form {
		max-width: 100%;
		background: inherit;
		padding: 0 0 8px;
	}

	.login__form-password {
		margin-top: 8px;
	}

	.login__two-factor-footer {
		display: flex;
		flex-direction: column;
		align-items: center;
		font-weight: 400;

		p {
			color: $gray-60;
			line-height: 24px;
			margin-bottom: 8px;
			font-size: $font-body;
		}

		a {
			color: var(--woo-purple-70);
			text-decoration: none;
		}

		@media (max-width: $break-mobile) {
			align-items: flex-start;
		}
	}

	.app-promo.magic-link-app-promo {
		padding: 24px;
	}

	.login__lost-password-form-wrapper {
		// Loading lost password form module is very fast, so we don't need to show a visual placeholder, just need to have the space reserved. Otherwise we will see a flash of content.
		.async-load__placeholder {
			visibility: hidden;
			height: 185px;
			margin: 48px auto 0;
		}
	}

	.login .login__form-forgot-password {
		text-align: start;
		color: $gray-50;
		margin-bottom: 32px;
		font-weight: 400;
		margin-top: -14px;
	}

	.logged-out-form__footer {
		background: none;
		border: none;
		text-align: center;
		margin: 0;

		&.is-blended {
			padding-top: 16px;
		}
	}

	.logged-out-form__links {
		text-align: center;
	}

	.formatted-header__title {
		font-size: $font-title-medium;
		font-family: $sans;
		margin: 0 0 8px;
		padding: 0;

		@media (max-width: $break-mobile) {
			padding-left: $woo-form-whitespace-660;
			padding-right: $woo-form-whitespace-660;
		}
	}

	&.is-section-signup::before,
	&.is-section-signup .layout__primary::before {
		display: none;
	}

	.signup-form__terms-of-service-link {
		color: $gray-50;
		text-align: left;
		margin-bottom: 32px;

		a {
			color: inherit;
			font-size: inherit;
		}
	}

	.signup-form__woo-wrapper .gridicon {
		color: var(--color-primary);
	}

	&.is-woocommerce-core-profiler-flow {
		.wp-login__container,
		.step-wrapper {
			@media (max-width: $break-mobile) {
				padding-top: 40px;
			}
		}

		.signup-form {
			max-width: 405px;
			margin: 0 auto;
			padding-top: 24px;
		}

		&.feature-flag-woocommerce-core-profiler-passwordless-auth {
			.signup-form {
				max-width: 515px;
				margin: 48px auto;
			}
		}

		.login__form-userdata label.form-label {
			line-height: 16px;
		}

		.login__form .login__form-userdata input.form-text-input,
		.login__form .login__form-userdata input {
			margin-bottom: 16px;
		}

		.formatted-header .formatted-header__title {
			margin-bottom: 12px;
			font-size: $woo-font-title-large;
			line-height: 32px;
			font-weight: 500;

			@media (max-width: $break-mobile) {
				font-size: rem(28px);
			}
		}

		form.two-factor-authentication__verification-code-form-wrapper {
			@media (max-width: $break-mobile) {
				margin: 0;
				max-width: 660px;
			}
		}

		.formatted-header__subtitle {
			color: $gray-700;

			@media (max-width: $break-mobile) {
				width: auto;
			}
		}

		.jetpack-connect__action-disclaimer .button.is-primary,
		.form-button.is-primary {
			border-radius: 2px;
			height: 48px;
			padding: 10px 16px;
			font-weight: 500;
			background-color: var(--color-primary);
		}

		.two-factor-authentication__actions.card button.button {
			border-radius: 2px;
			height: 48px;
			padding: 10px 16px;
			font-weight: 500;
			background-color: transparent;
			color: var(--wp-admin-theme-color);
			border: 1px solid var(--wp-admin-theme-color);
			width: 100%;
		}

		.two-factor-authentication__verification-code-form.card {
			padding-top: 0;
			margin-bottom: 36px;

			.verification-code-form__help-text {
				color: $gray-700;
			}

			.security-key-form__help-text {
				color: $gray-700;
				margin-bottom: 24px;

				p strong {
					color: $gray-700;
					font-weight: 400;
				}
			}

			fieldset.form-fieldset label.form-label {
				font-weight: 500;
				text-transform: uppercase;
				font-size: rem(11px);
				color: $gray-900;
			}
		}

		div.login__two-factor-footer {
			margin-top: 16px;
			p {
				margin-bottom: 0;
				color: $woo-label-color;
				font-size: rem(14px);
				line-height: 24px;
				letter-spacing: -0.1px;

				a {
					color: var(--wp-admin-theme-color);
					font-size: rem(14px);
					line-height: 24px;
					letter-spacing: -0.1px;
				}
			}
		}

		.auth-form__social-buttons-tos {
			text-align: center;
			line-height: 16px;

			@media (max-width: $break-mobile) {
				text-align: start;
			}

			a {
				line-height: 16px;
			}
		}

		.jetpack-connect__logged-in-content {
			@media (max-width: $break-mobile) {
				padding-left: $woo-form-whitespace-660;
				padding-right: $woo-form-whitespace-660;
			}
		}

		.jetpack-connect__features_wrapper {
			@media (max-width: $break-mobile) {
				display: none !important;
			}
		}

		.auth-form__social-buttons {
			width: 100%;
			.auth-form__social-buttons-container {
				width: 100%;
			}
		}

		.wp-login__container .is-jetpack .auth-form__social,
		.signup-form .auth-form__social {
			button.social-buttons__button.button {
				width: 100%;
				border: 1px solid var(--studio-gray-50, #646970);
				border-radius: 2px;
				border-color: #ccc;
				padding: 4px 16px;
				display: flex;
				justify-content: stretch;
				align-items: center;
				align-self: center;
				margin: 0;
				svg {
					margin-right: auto;
					border: 0;
				}
				span {
					text-align: center;
					margin-left: -44px;
					margin-right: auto;
					font-weight: 400;
				}
				&:last-child {
					margin-bottom: 16px;
				}
			}
		}

		.form-input-validation {
			padding: 8px 0 11px;

			// Hide form validation icon
			svg {
				display: none;
			}
		}

		.login__lostpassword-subtitle a {
			line-height: inherit;
		}

		.auth-form__social-buttons-container {
			width: 100%;
		}

		.jetpack-connect__authorize-form .jetpack-connect__logged-in-content {
			.jetpack-connect__logged-in-bottom {
				@media (max-width: $break-mobile) {
					position: initial;
					max-width: 100%;
					padding-right: 0;
				}
			}
		}

		&.two-factor-auth-enabled {
			.two-factor-authentication__verification-code-form-wrapper {
				fieldset,
				button,
				.auth-form__separator,
				.two-factor-authentication__actions,
				.login__two-factor-footer {
					max-width: 327px;
				}
			}
		}
	}

	&.is-woo-passwordless {
		$max-width: 327px;
		$woo-font-size-input: $font-body;
		$woo-radius-input: 8px;

		* {
			font-family: $woo-inter-font;
		}

		h1,
		.signup-form__woo-wrapper,
		.wp-login__one-login-layout-heading-text {
			color: var(--studio-gray-100);
			font-family: var(--woo-font-family-header);
			font-feature-settings: "ss01" on, "salt" on;
			text-align: center;
			font-weight: 700;

			* {
				font-family: inherit;
				font-feature-settings: inherit;
			}
		}

		h1,
		h3:not(.wp-login__one-login-layout-heading-subtext) {
			color: var(--studio-gray-100);
			text-align: center;
			font-size: 2.25rem;
			font-weight: 700;
			line-height: 39.6px;
			letter-spacing: -0.72px;
		}

		// Remove autofill styles.
		input:-webkit-autofill,
		input:-webkit-autofill:hover,
		input:-webkit-autofill:focus,
		input:-webkit-autofill:active,
		input[data-com-onepassword-filled="light"] {
			-webkit-background-clip: text;
			-webkit-box-shadow: 0 0 0 1000px #fff inset;
			transition: background-color 5000s ease-in-out 0s;
		}

		.form-password-input {
			.form-text-input[type] {
				padding-right: 44px;
			}

			.form-password-input__toggle-visibility {
				top: 12px;
				right: 16px;
			}
		}

		.button.is-primary,
		.login .button.is-primary,
		.magic-login .magic-login__form-action .button.is-primary:not([disabled]) {
			background-color: var(--woo-purple-50);
			font-family: $woo-inter-font;
			font-size: 1rem;
			font-style: normal;
			font-weight: 500;
			line-height: 28px;

			&.is-busy {
				background-image: none;
			}

			&:hover {
				background-color: var(--woo-purple-70);
				color: #fff;
			}

			&:disabled {
				background-color: var(--studio-gray-0);
				color: var(--studio-gray-20);
			}
		}

		.formatted-header__title {
			margin-bottom: 12px;

			@media (max-width: $break-mobile) {
				padding: 0;
				max-width: $max-width;
				margin: 0 auto 16px;
			}
		}

		.verification-code-form__help-text,
		.security-key-form__help-text {
			font-size: $font-body-large;
			font-weight: 400;
			line-height: 27px;
			letter-spacing: -0.025px;
			color: var(--studio-gray-60);
			text-align: center;

			a {
				color: var(--woo-purple-50);
				font-size: rem(18px);
				font-style: normal;
				font-weight: 500;
				line-height: 27px;
				letter-spacing: -0.025px;

				&:hover {
					color: var(--woo-purple-40);
				}
			}

			@media (max-width: $break-mobile) {
				padding: 0;
				max-width: $max-width;
				margin: 0 auto;
				width: 100%;
				text-align: left;
			}
		}

		.login__form-forgot-password {
			margin-top: unset;
		}

		.login__body--continue-as-user {
			width: 540px;
			margin: 0 auto;
			padding: 0;
			display: flex;
			flex-direction: column;
		}

		.continue-as-user {
			margin: 0;
			max-width: initial;

			.continue-as-user__user-info {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				align-self: stretch;
				border-radius: 8px; // stylelint-disable-line scales/radii
				border: 1px solid #dcdcde;
				width: 100%;
				box-sizing: border-box;
				padding: 32px 24px 16px 24px;
				background: #fff;
			}

			~ .card.auth-form__social.is-login {
				margin: 0;
				padding: 0;
				max-width: initial;
			}

			.continue-as-user__gravatar-content {
				display: flex;
				flex-direction: column;
				margin: 0 0 8px;
				padding: 0;
				border: 0;
				border-radius: 0;
				width: 100%;
			}

			.gravatar.continue-as-user__gravatar {
				width: 80px;
				height: 80px;
				box-sizing: border-box;
			}

			.continue-as-user__username {
				font-style: normal;
				margin-top: 8px;

			}

			.continue-as-user__email {
				font-size: 0.875rem;
				line-height: 21px;
				color: $gray-50;
				text-align: center;
				margin: 0;
			}

			.continue-as-user__not-you {
				margin-bottom: 0;
				display: flex;
				height: 32px;
				padding: 4px 16px;
				justify-content: center;
				align-items: center;
				gap: 4px;
			}

			.continue-as-user__change-user-link {
				font-size: 0.875rem;
				font-weight: 500;
			}

			.continue-as-user__continue-button {
				margin-top: 24px;
			}
		}

		.magic-login {

			h1.magic-login__form-header {
				font-family: var(--woo-font-family-header);
				color: var(--studio-gray-100);
				text-align: center;
				font-size: 2.25rem;
				font-weight: 700;
				line-height: 39.6px;
				letter-spacing: -0.72px;
				margin: 0;

				@media (max-width: $break-mobile) {
					text-align: center;
					max-width: $max-width;
					margin: 0 auto;
				}
			}

			.magic-login__form p.magic-login__form-sub-header,
			.magic-login__form-text {
				color: var(--studio-gray-60);
				text-align: center;
				font-size: rem(18px);
				font-style: normal;
				font-weight: 400;
				line-height: 27px;
				letter-spacing: -0.025px;
				margin: 12px auto 48px;

				strong {
					font-weight: 600;
					display: inline-block;
				}
			}

			.magic-login__emails-list {
				li {
					border-radius: 8px; // stylelint-disable-line scales/radii
					border: 2px solid var(--studio-gray-10);
				}

				a {
					color: var(--studio-gray-100);
					text-decoration: none;
					font-size: 1rem;
					font-style: normal;
					font-weight: 500;
					line-height: 24px;
				}
			}
		}

		.two-factor-authentication__verification-code-form-wrapper {
			padding: 0;

			button,
			.form-fieldset,
			.auth-form__separator {
				max-width: $max-width !important;
			}

			.two-factor-authentication__verification-code-form {
				margin-bottom: 32px;
				padding: 0;
				display: flex;
				flex-direction: column;
				align-items: center;

				.form-fieldset {
					margin: 48px auto 8px;
					width: 100%;
				}

				.verification-code-form__help-text {
					@media screen and (max-width: 660px) {
						max-width: 450px;
					}

					@media (max-width: $break-mobile) {
						max-width: $max-width;
					}
				}

				.security-key-form__help-text,
				.security-key-form__add-wait-for-key {
					margin-bottom: 48px;

					p {
						max-width: 515px;
					}
					p:first-child {
						margin-bottom: 12px;
					}

					@media screen and (max-width: 660px) {
						text-align: center;
						max-width: 450px;
					}

					@media (max-width: $break-mobile) {
						text-align: left;
						max-width: $max-width;
					}
				}
			}

			.two-factor-authentication__small-print {
				max-width: 350px;
				color: $gray-50;
				text-align: center;
				font-style: normal;
				font-weight: 400;
				line-height: 18px;
			}

			.two-factor-authentication__actions.card {
				padding: 0;
				border: 0;
				margin: 32px auto;
				clip-path: none;
				display: flex;
				flex-direction: column;
				gap: 16px;

				.button {
					min-height: 48px;
					font-style: normal;
					font-weight: 500;
					color: var(--studio-gray-100);
					margin: 0 auto;
					max-width: $max-width !important;
				}
			}
		}

		.login__two-factor-footer {
			p {
				max-width: $max-width;
				text-align: center;
			}

			@media (max-width: $break-mobile) {
				align-items: center;
			}
		}
		// /log-in/jetpack/link styles
		.magic-login__main-content-woo-core-profiler {
			display: flex;
			align-items: center;
			flex-direction: column;
			max-width: 327px;
			margin: auto;
			h1.magic-login__form-header {
				font-size: rem(32px);
				margin-bottom: 15px;
			}

			p.email-sent {
				text-align: center;
				font-size: rem(16px);
				color: #757575;
				font-style: normal;
				font-weight: 400;
				line-height: 24px; /* 150% */
			}

			p.email-resend {
				text-align: center;
				font-size: rem(13px);
				color: #757575;
				font-style: normal;
				font-weight: 400;
				line-height: 20px; /* 153.846% */
				button {
					color: var(--wp-admin-theme-color);
					cursor: pointer;
					&:disabled {
						opacity: 0.5; // Reduce intensity
						cursor: not-allowed; // Optional: change the cursor to indicate it's disabled
					}
				}
			}

			.magic-login__emails-list {
				display: contents;
				ul {
					width: 100%;
					li {
						border-radius: 2px;
						border: 1px solid #a7aaad;
					}
				}
			}

			svg {
				margin: 48px 0 48px 0;
			}
		}

	}

	// Core Profiler + Passwordless
	&.is-woocommerce-core-profiler-flow.is-woo-passwordless {
		h3,
		h1.formatted-header__title,
		h1.magic-login__form-header {
			margin-bottom: 15px;
		}

		.login__form-forgot-password {
			font-size: rem(12px);
			text-underline-offset: 2px;
			margin-bottom: 24px;
		}

		.login__form-account-tip {
			display: none;
		}

		button.button.is-primary {
			background-color: var(--color-primary);
			font-family: inherit;
			border-radius: 2px;
			&:hover:not(:disabled) {
				background-color: var(--color-primary);
			}

			&:disabled {
				background-color: var(--studio-gray-0);
				color: var(--studio-gray-20);
			}
		}

		.signup-form {
			max-width: 327px;
			margin-top: 48px;
		}

		.jetpack-connect__main-logo {
			@media (max-width: $break-mobile) {
				padding-top: 0;
			}
		}

		.formatted-header__subtitle {
			color: #757575;
			text-align: center;
			font-size: 1rem;
			font-style: normal;
			font-weight: 400;
			line-height: 24px; /* 150% */
			a {
				font-weight: inherit;
				font-size: inherit;
				color: var(--wp-admin-theme-color);
				line-height: inherit;
				text-decoration: none;
				text-underline-offset: 0;

				&:hover {
					color: var(--wp-admin-theme-color);
				}
			}

			@media (max-width: $break-mobile) {
				width: 250px;
				padding-left: $woo-form-whitespace-660;
				padding-right: $woo-form-whitespace-660;
				text-align: start;
				br {
					display: none;
				}
			}
		}

		.verification-code-form__help-text,
		.security-key-form__help-text {
			font-size: 1rem;
		}

		.login form.two-factor-authentication__verification-code-form-wrapper {
			margin-top: 15px;
			margin-bottom: 0;
			.two-factor-authentication__actions {
				padding-bottom: 8px;
				margin-bottom: 0;
			}
		}

		.two-factor-authentication__verification-code-form-wrapper {
			button.is-primary {
				font-size: rem(14px);
			}

			.two-factor-authentication__actions.card {
				padding: 36px 0 40px;
				button {
					color: var(--wp-admin-theme-color);
				}

				@media screen and (max-width: $break-mobile) {
					margin-bottom: 40px;
				}
			}

			.security-key-form__help-text {
				p {
					line-height: 24px;

					&:first-child {
						margin-bottom: 0;
					}
				}
			}
		}

		.auth-form__social-buttons-tos {
			color: #757575;
			a {
				color: var(--wp-admin-theme-color);
				font-family: inherit;
				line-height: 18px;
				text-underline-offset: 2px;
			}
		}

		.main {
			.auth-form__social {
				.auth-form__social-buttons .social-buttons__button.button {
					border-radius: 2px;
					border: 1px solid var(--Gray-Gray-20, #a7aaad) !important;
					background: var(--black-white-white, #fff);
					span {
						font-size: 0.875rem;
						letter-spacing: 0.32px;
						font-family: $woo-sfpro-display-font;
						font-weight: 500;
						margin-left: 0;
					}
				}
			}
		}

		.login__form-action,
		.logged-out-form {
			button.is-primary {
				font-size: rem(14px);
			}
		}

		.wp-login__container {
			@media screen and (max-width: $break-mobile) {
				padding-top: 24px;
			}
		}

		.magic-login__main-content-woo-core-profiler {
			h1 {
				@media screen and (max-width: $break-mobile) {
					font-weight: 500;
				}
			}
			.magic-login__emails-list {
				ul li a p {
					font-size: rem(14px);
				}
			}
		}

		.login__two-factor-footer {
			@media screen and (max-width: $break-mobile) {
				max-width: 327px;
				padding-left: 0;
				padding-right: 0;
				margin: 0 auto 0 auto;
				align-items: baseline;
			}
		}
	}
}

.woo,
.layout.is-woocommerce-core-profiler-flow {
	--woo-purple-0: #F2EDFF;
	--woo-purple-40: #873EFF;
	--woo-purple-50: #720EEC;
	--woo-purple-60: #6108CE;
	--woo-purple-70: #5007AA;
	--woo-font-family-header: proxima-vara, 'Proxima Fallback', sans-serif;

	* {
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
		text-rendering: optimizeLegibility;
	}

	h1,
	h2,
	h3 {
		font-feature-settings: "ss04";
	}

	.masterbar__woo {
		.masterbar__woo-nav {
			.masterbar__login-back-link {
				margin-left: 15px;
				font-weight: 400;
			}
		}
	}

	.button.is-primary,
	.login .button.is-primary,
	.magic-login .magic-login__form-action .button.is-primary:not([disabled]) {
		@include button-primary;
	}

	.calypso-notice__text a,
	.calypso-notice__text a:visited {
		@include link-woo;
	}

	.auth-form__social-buttons-tos {
		color: var(--studio-gray-50) !important;

		a {
			text-underline-offset: 2px;
		}
	}

	.login__lost-password-no-account a {
		@include link-woo;
	}

	.login__two-factor-footer,
	.security-key-form__help-text,
	.formatted-header__subtitle {
		a {
			@include link-woo;
		}
	}

	// Woo DNA Flow
	.jetpack-connect__main {
		--studio-woocommerce-purple: var(--woo-purple-40);
		--studio-woocommerce-purple-60: var(--woo-purple-40);
		--color-accent: var(--woo-purple-40);

		.login__social-tos a {
			@include link-woo;
		}
	}
}
