@import "@wordpress/base-styles/breakpoints";
@import "@automattic/components/src/styles/typography";
@import "@wordpress/base-styles/variables";
@import "@wordpress/base-styles/colors";
@import "@wordpress/base-styles/mixins";
@import "colors";

// TODO: Audit and remove since there is no footer colophon anymore.
$colophon-height: 50px;

.jetpack-connect__main:not(.is-woocommerce):not(.is-automattic-for-agencies-flow),
.layout.is-section-jetpack-connect:not(.is-jetpack-mobile-flow):not(.is-woocommerce-core-profiler-flow):not(.is-automattic-for-agencies-flow) {
	@include jetpack-connect-colors();
}

.layout.is-woocommerce-core-profiler-flow {
	@include woocommerce-colors();
}

.is-automattic-for-agencies-flow,
.layout.is-automattic-for-agencies-flow {
	@include automattic-for-agencies-colors();

	.formatted-header__title {
		margin: 32px 0;
		font-size: 1.25rem;
	}

	.jetpack-connect__tos-link {
		font-size: 0.875rem;
	}
}

.jetpack-connect__main.main.is-automattic-for-agencies-flow {
	max-width: 426px;
}

.is-section-jetpack-connect,
.is-section-purchase-product {
	.layout__content {
		position: static;
		// Adjust the padding as we no longer
		// show the masterbar.
		padding-top: 48px;

		& > .banner {
			max-width: 600px;
		}
	}

	&.layout {
		position: relative;
		min-height: calc(100% - #{$colophon-height});
	}

	// Hide the masterbar for real
	.masterbar {
		display: none;
	}

	// Move the back button up top
	.back-button {
		top: 0;
	}
}

.jetpack-connect__main.main:not(.is-woocommerce-core-profiler-flow) {
	max-width: 400px;

	.formatted-header {
		margin-bottom: 16px;
	}

	&.is-mobile-app-flow {
		.logged-out-form__links {
			display: none;
		}

		.jetpack-connect__back-button {
			display: none;
		}
	}
}

.jetpack-connect__step {
	margin: 0 auto;
	max-width: 600px;
	text-align: initial;

	input[type="radio"]:focus {
		box-shadow: 0 0 0 2px var(--studio-jetpack-green-20);
	}

	.site-topic__content {
		padding-bottom: 2px;

		.form-fieldset {
			position: relative;
		}

		input[type="text"] {
			border-color: var(--studio-jetpack-green-50);
		}

		@include breakpoint-deprecated( "<660px" ) {
			.button {
				font-size: $font-body;
				padding-top: 12px;
				padding-bottom: 14px;
			}
		}
	}
}

.jetpack-connect__main-logo {
	text-align: center;

	@include breakpoint-deprecated( "<660px" ) {
		margin-top: 20px;
	}
}
.jetpack-connect__main-logo.add-bottom-margin {
	margin-bottom: 35px;
}

.jetpack-connect__compact-logo {
	display: flex;
	position: absolute;
	top: 24px;
	left: 24px;

	align-items: center;
	column-gap: 16px;

	.jetpack-connect__back-button {
		--wp-components-color-accent: var(--color-accent-dark);

		margin-top: 0;

		&:focus {
			outline: none;
			box-shadow: none;
		}
	}
}

.jetpack-connect__main.is-wide {
	max-width: 100%;
	text-align: center;
}

.jetpack-connect__main-error {
	.logged-out-form__links {
		margin-top: 15px;
		text-align: center;
	}
}

.jetpack-connect__site-url-entry-container,
.purchase-product__site-url-entry-container {
	margin: 10vh auto 0; // match spacing of onboarding flow (.jetpack-connect__authorize-form-wrapper--onboarding)
	max-width: 400px;

	.formatted-header {
		text-align: center;

		.formatted-header__title {
			// To avoid wrapping of "self-hosted" in the title due
			// to the behavior of text-wrap:balance with hyphens.
			text-wrap: pretty;
		}
	}
}

.jetpack-connect__wp-admin-dialog.dialog.card {
	max-width: 400px;

	.jetpack-connect__install-wp-admin {
		width: 400px;
		height: 294px;
	}
}

.jetpack-connect__site-url-input-container,
.purchase-product__site-url-input-container,
.example-components__site-url-input-container {
	.jetpack-connect__site-address-container,
	.jetpack-connect__password-container,
	.example-components__site-address-container {
		position: relative;

		.gridicons-user,
		.jetpack-connect__site-address-icon {
			position: absolute;
			top: 8px;
			left: 8px;
			color: var(--color-neutral-10);
		}

		.form-text-input {
			padding-left: 40px;
		}

		.spinner {
			position: absolute;
			right: 8px;
			top: 10px;
		}

		.sites-dropdown__wrapper {
			width: 100%;
			margin-top: 20px;
		}
	}

	.button {
		width: 100%;
		margin-top: 16px;
		word-wrap: nowrap;
	}
}

.jetpack-connect__site-address {
	padding: 0 0 20px; // this is 20px because the icon element has a -4px margin for alignment
	margin-bottom: 24px;
	border-bottom: 1px solid var(--color-border-subtle);
	display: flex;
}

.jetpack-connect__globe {
	width: 32px;
	height: 28px;
	margin-top: -4px;
	background-color: var(--color-neutral);
	margin-right: 8px;
	padding-top: 4px;
	text-align: center;

	.gridicons-globe {
		fill: var(--color-neutral-0);
	}
}

.jetpack-connect__authorize-form {
	.jetpack-connect__authorize-form-header {
		text-align: center;
	}

	.jetpack-connect__authorize-form-header--left-aligned {
		padding-bottom: 48px;

		h1 {
			margin-bottom: 16px;
			@include heading-2x-large;
		}

		p {
			@include body-large;
			margin-bottom: 0;
		}
	}
}

.jetpack-connect__authorize-form-wrapper--onboarding {
	margin-top: 10vh;
	min-width: 450px;

	@media screen and ( max-width: $break-mobile ) {
		min-width: 250px;
		padding: 0 8px;
	}
}

.jetpack-connect__logged-in-user-card {
	display: flex;
	border-radius: 4px;
	border: 1px solid var(--studio-gray-5);

	.jetpack-connect__user-card-gravatar.gravatar {
		border-radius: 4px 0 0 4px;
		margin: 0;
	}
}

.jetpack-connect__switch-account-link {
	@include body-medium;
	margin: 12px 0 0;
	padding: 0;
	color: var(--studio-green-60);
}

.jetpack-connect__benefits {
	padding: 32px 0;

	.jetpack-connect__benefits-title {
		@include heading-large;

		padding-bottom: 16px;
	}

	ul {
		margin: 0;
		padding: 0;
	}

	li {
		@include heading-medium;

		list-style: none;
		display: flex;
		align-items: center;
		gap: 6px;

		& + li {
			margin-top: 10px;
		}

		svg {
			display: block;
		}
	}
}

.jetpack-connect__user-card-text {
	padding: 10px 12px 10px 16px;

	.jetpack-connect__logged-in-user-text-name {
		@include heading-large;
	}

	.jetpack-connect__logged-in-user-text-email {
		@include body-small;
	}
}

.card.logged-out-form__footer {
	&.jetpack-connect__action--onboarding {
		margin: 0;
		padding: 0;

		button {
			width: 100%;
			padding: 12px 16px;
			min-height: 48px;
			font-weight: 500;
			font-size: 16px;
			line-height: 24px;
			border: 1px solid var(--jp-green-60, #007117);
			border-radius: var(--jp-button-radius, 4px);
			box-sizing: border-box;
			color: var(--jp-white, #fff);
			background: var(--jp-green-60, #007117);
			justify-content: center;

			&:hover,
			&:focus {
				background: var(--jp-green-70, #00801a);
				color: var(--jp-white, #fff);
			}

			&:focus {
				border-color: var(--jp-white, #FFF);
				outline: 2px solid var(--jp-green-60, #007117);
			}

			&:disabled {
				background: var(--jp-gray, #dcdcde);
				border-color: var(--jp-gray, #dcdcde);
				color: var(--jp-gray-50, #646970);
			}
		}

		.jetpack-connect__action--onboarding-disclaimer {
			p {
				padding-top: 20px;
				color: var(--jp-gray-70, #757575);
				text-align: start;
			}
		}
	}
}

.jetpack-connect__logged-in-form {
	.jetpack-connect__logged-in-form-user-text,
	.jetpack-connect__activate-product-text {
		text-align: center;
	}

	.jetpack-connect__activate-product-text {
		/* stylelint-disable-next-line scales/font-sizes */
		font-size: 0.9rem;
	}

	.gravatar {
		display: block;
		margin: 0 auto 8px;
	}

	.button {
		width: 100%;
	}
}

.jetpack-connect__back-button {
	margin-top: 16px;
}

.jetpack-connect__install {
	margin-bottom: 16px;
}

.jetpack-connect__install-steps {
	display: flex;
	justify-content: center;
	flex-direction: column;
	flex-wrap: wrap;

	@include breakpoint-deprecated( ">660px" ) {
		flex-direction: row;
	}
}

.jetpack-connect__install-step {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	min-width: 320px;

	@include breakpoint-deprecated( ">480px" ) {
		max-width: 360px;
	}

	@include breakpoint-deprecated( ">660px" ) {
		margin: 0 8px 16px;
	}
}

.jetpack-connect__install-step-title {
	font-size: $font-title-small;
}

.jetpack-connect__install-step-text {
	color: var(--color-neutral-light);
	font-size: $font-body-extra-small;
	margin: 8px 0 16px;
	flex-grow: 2;
}

.example-components__main {
	cursor: pointer;
	width: 100%;

	&:hover > div {
		box-shadow: 0 0 0 1px var(--color-neutral-light), 0 2px 4px var(--color-neutral-10);
	}
}

.example-components__browser-chrome {
	padding: 8px;
	background-color: var(--color-neutral-0);
	border-radius: 8px 8px 0 0; /* stylelint-disable-line scales/radii */
}

.example-components__browser-chrome-dots {
	line-height: 0;
	margin-bottom: 8px;
}

.example-components__browser-chrome-dot {
	display: inline-block;
	margin-right: 8px;
	background-color: var(--color-neutral-20);
	width: 8px;
	height: 8px;
	border-radius: 50%;
}

.example-components__content {
	position: relative;
	margin: 0;
	line-height: 0;
	box-shadow:
		0 0 0 1px color-mix(in srgb, var(--color-neutral-10) 50%, transparent),
		0 1px 2px var(--color-neutral-0);
}

.example-components__install-plugin-header {
	width: 100%;
	padding-bottom: 12%;
	background-color: var(--studio-jetpack-green-30);
}

.example-components__install-plugin-body {
	width: 100%;
	padding-bottom: 38%;
}

.example-components__install-plugin-footer {
	position: absolute;
	right: 0;
	bottom: 0;
	left: 0;
	padding: 9px 16px;
	text-align: right;
	background-color: var(--color-neutral-0);
}

.example-components__install-plugin-footer-button {
	display: inline-block;
	font-size: $font-body-extra-small;
	padding: 7px 14px;
	line-height: 1;
	color: var(--color-text-inverted);
	background-color: var(--color-wp-admin-button-background); // wp-admin button blue
	border: 1px solid var(--color-wp-admin-button-border);
	border-radius: 2px;
}

.example-components__activate-jetpack {
	background-color: var(--color-neutral-0);
}

.example-components__content-wp-admin-masterbar {
	width: 100%;
	padding-bottom: 6%;
	background-color: var(--color-neutral-90);
}

.example-components__content-wp-admin-sidebar {
	display: inline-block;
	width: 15%;
	padding-bottom: 44%;
	line-height: 0;
	background-color: var(--color-neutral-90);
}

.example-components__content-wp-admin-main {
	display: inline-block;
	vertical-align: top;
	width: 85%;
	line-height: 1.5;
}

.example-components__connect-jetpack {
	background-color: var(--color-neutral-0);

	.example-components__content-wp-admin-plugin-name {
		font-size: $font-body-small;
		margin-bottom: 8px;
	}

	.example-components__content-wp-admin-connect-banner {
		margin: 10px;
		padding: 10px 10px 12px;
		background-color: var(--color-surface);
	}

	.example-components__content-wp-admin-connect-button {
		display: inline-block;
		font-size: $font-body-extra-small;
		padding: 7px 14px;
		line-height: 1;
		color: var(--color-text-inverted);
		border-radius: 2px;
		background-color: var(--studio-jetpack-green-30);
		border: 1px solid var(--studio-jetpack-green-40);
		border-width: 1px 1px 2px;
	}
}

.example-components__content-wp-admin-plugin-card {
	margin: 10px;
	padding: 12px 10px;
	background-color: var(--color-surface);
}

.example-components__content-wp-admin-plugin-name {
	font-size: $font-body-extra-small;
}

.example-components__content-wp-admin-plugin-activate-link {
	font-size: $font-body-extra-small;
	color: var(--studio-blue-50);
}

.example-components__content-wp-admin-activate-view {
	margin: 10px;
}

.example-components__content-wp-admin-activate-link {
	font-size: $font-body-extra-small;
	display: inline-block;
	padding: 7px 14px;
	line-height: 1;
	color: var(--color-text-inverted);
	background-color: var(--color-wp-admin-button-background); // wp-admin button blue
	border: 1px solid var(--color-wp-admin-button-border);
	border-radius: 2px;
}

.example-components__site-url-input-container
.example-components__site-address-container
.example-components__browser-chrome-url {
	height: 40px;
	font-size: $font-body-extra-small;
	color: var(--color-neutral-light);
}

.jetpack-connect__sso-user-profile {
	margin-bottom: 16px;
}

.jetpack-connect__sso-user-profile .gravatar {
	display: block;
	margin: 0 auto 16px;
}

.jetpack-connect__sso-log-in-as {
	font-family: $sans;
	font-size: $font-title-small;
	text-align: center;
}

.jetpack-connect__sso-user-email {
	color: var(--color-neutral-20);
	font-weight: 400;
	text-align: center;
}

.jetpack-connect__sso-actions .button {
	display: block;
	text-align: center;
	width: 100%;
}

.jetpack-connect__logged-in-form-loading {
	text-align: center;

	span {
		animation: loading-fade 1.6s ease-in-out infinite;
		display: block;
	}

	.spinner {
		display: inline-block;
		margin-top: 8px;
	}
}

.jetpack-connect__tos-link {
	font-size: $font-body-extra-small;
	margin: 0 0 16px;
	text-align: center;
}

.is-support-session {
	.jetpack-connect__sso,
	.jetpack-connect__authorize-form {
		.jetpack-connect__tos-link {
			display: none;
		}
	}
}

.jetpack-connect__tos-link-text {
	white-space: nowrap;
}

.jetpack-connect__connect-button-card {
	background: var(--color-neutral-0);
	border-top: 1px solid var(--color-neutral-0);
	box-shadow: none;
	margin: 16px -16px -16px;
	padding: 16px;

	@include breakpoint-deprecated( ">480px" ) {
		margin: 24px -24px -24px;
		padding: 24px;
	}

	.button.is-primary {
		margin: 0;
	}
}

.jetpack-connect__site.card {
	padding: 0;
}

.jetpack-connect__site.card,
.jetpack-connect__logged-in-card.card {
	margin: 0 auto 16px;
}

.jetpack-connect__sso-terms-dialog {
	max-height: 70vh;
	overflow-y: auto;
}

.jetpack-connect__sso-shared-details-table {
	border-collapse: separate;
}

.jetpack-connect__sso-shared-detail-row {
	margin-bottom: 16px;

	&:last-child {
		margin-bottom: 0;
	}
}

.jetpack-connect__sso-shared-detail-label {
	font-weight: 600;
}

.jetpack-connect__sso-shared-detail-value {
	padding-left: 16px;
}

.jetpack-connect__sso-shared-detail-label,
.jetpack-connect__sso-shared-detail-value {
	padding-bottom: 8px;
}

@include breakpoint-deprecated( "<480px" ) {
	.jetpack-connect__sso-shared-detail-label,
	.jetpack-connect__sso-shared-detail-value {
		display: block;
	}

	.jetpack-connect__sso-shared-detail-label {
		padding-bottom: 0;
	}

	.jetpack-connect__sso-shared-detail-value {
		padding-left: 0;
	}
}

.jetpack-connect__sso-placeholder {
	@include placeholder( --color-neutral-10 );
	background-color: transparent;
}

.jetpack-connect__sso-placeholder a {
	color: transparent;
}

.jetpack-connect__sso {

	.formatted-header__title {
		margin-bottom: 5px;
	}

	.card.jetpack-connect__site {
		max-width: 100%;
	}

	.logged-out-form__link-item {
		font-size: unset !important;
	}
}

.jetpack-connect__sso .email-verification-gate .calypso-notice {
	margin-bottom: 0;
}

.jetpack-connect__sso .email-verification-gate .calypso-notice__text {
	word-wrap: break-word;
	word-break: break-word;
}

.jetpack-connect__error-details {
	margin-bottom: 16px;
}

.jetpack-connect__credentials-submit {
	transition: none;

	&.is-error,
	&[type="password"] {
		margin-bottom: 0;
	}

	&.is-primary {
		background-color: var(--studio-jetpack-green-30);
		border-color: var(--studio-jetpack-green-50);
	}
}

.jetpack-connect__password-container {
	position: relative;
	margin-bottom: 22px;
	margin-top: 22px;

	.spinner {
		z-index: z-index("root", ".jetpack-connect__creds-form-footer .jetpack-connect__creds-form-spinner");
	}
}

.jetpack-connect__creds-form-footer {
	.button {
		margin-top: 0;
	}
}

.jetpack-connect__password-form {
	position: relative;

	.gridicons-lock {
		position: absolute;
		z-index: z-index("root", ".jetpack-connect__password-form .gridicon");
		left: 8px;
		top: 7px;
		fill: var(--color-neutral-10);
	}
}

.jetpack-connect__password-form-input {
	&[type="password"] {
		padding-left: 40px;
	}
}

.jetpack-connect__creds-form-spinner {
	margin-top: 0;
	margin-bottom: -20px;
}

.jetpack-connect__navigation {
	text-align: center;
}

.jetpack-connect__plan-info {
	text-align: center;
	max-width: 500px;
	margin-top: 40px;
	padding-top: 40px;
	padding-bottom: 40px;
}

.jetpack-connect__plan-info-illustration {
	max-width: 200px;
}

.jetpack-connect__plan-info-buttons {
	.button + .button {
		margin-left: 10px;
	}
}

.jetpack-connect__connect-button {
	text-align: center;
}

/**
 * Onboarding styles
 **/
.layout.is-section-jetpack-connect:not(.is-jetpack-mobile-flow):not(.is-woocommerce-core-profiler-flow),
.layout.is-section-purchase-product {
	&.layout {
		position: relative;
		min-height: calc(100% - #{$colophon-height});
	}

	.site__title::after,
	.site__domain::after {
		right: 3px;
	}

	.site-type__option-label {
		color: var(--color-text);
	}

	.jetpack-connect__plan-info {
		.formatted-header {
			color: var(--color-text);
		}

		.formatted-header__subtitle {
			color: var(--color-text-subtle);
		}
	}

	.logged-out-form {
		border-radius: 0;
		box-shadow: 0 0 0 1px var(--color-border-subtle);
	}

	.jetpack-connect__site-address-container input:focus,
	.jetpack-connect__password-container input:focus,
	.logged-out-form input:focus {
		border-color: var(--color-accent);
		box-shadow: 0 0 0 2px var(--color-accent-light);
	}

	.jetpack-connect__tos-link a,
	.signup-form__terms-of-service-link a,
	.form-input-validation a,
	.faq a {
		color: var(--color-accent-dark);

		&:hover,
		&:focus {
			color: var(--color-accent);
		}
	}

	.plan-features__table-item,
	.plan-features--signup .plan-features__row:last-of-type .plan-features__table-item {
		border: none;
	}

	.plan-features__item-checkmark {
		fill: var(--color-accent-dark);
	}

	.plan-features__item-title a {
		color: var(--color-accent-dark);

		&:hover,
		&:focus {
			color: var(--color-accent);
		}
	}

	.auth-form__social p {
		color: var(--color-neutral-60);
	}

	.auth-form__social,
	.login__form-social {
		padding-bottom: 0;
		margin-top: 16px;
		p {
			font-size: $font-body-extra-small;
		}

		.auth-form__social-buttons-tos {
			color: var(--studio-gray-60);
			margin-bottom: 16px;
			a {
				text-decoration: underline;
				color: var(--studio-gray-60);
			}
		}

		.card {
			padding: 0;
			box-shadow: none;
		}
	}

	.logged-out-form__footer,
	.login__form-footer {
		.button.is-primary {
			box-shadow: none;
		}
	}

	.payment-logo.is-paypal {
		width: 64px;
		background-color: var(--color-surface);
		background-size: 84%;
		background-position: 50% 45%;
		border-radius: 1px; /* stylelint-disable-line scales/radii */
	}

	.payment-logo.is-apple-pay {
		height: 22px;
		background-size: 34px auto;
		width: 34px;
		margin-top: 0;
	}

	.jetpack-connect__main-error .empty-content {
		padding-bottom: 24px;
	}

	.site-topic__content .form-fieldset {
		position: relative;
	}
}

.layout.is-section-jetpack-connect,
.layout.is-section-purchase-product {
	/** WooCommerce Onboarding Styles **/
	&.is-woocommerce-core-profiler-flow {
		background-color: var(--color-woocommerce-onboarding-background);

		.jetpack-connect__main-logo {
			text-align: center;
			border-bottom: 1px solid var(--color-neutral-50);
			position: absolute;
			width: 100%;
			left: 0;
			top: 0;
			height: 56px;

			svg > g {
				transform: translateX(25%);
			}

			@include breakpoint-deprecated( "<660px" ) {
				margin-top: 0;
			}
		}

		.jetpack-header {
			margin: 0;
			text-align: center;
			margin-right: auto;
			margin-left: auto;
			padding-left: 0;
			display: block;
			height: 56px;
			border-bottom: 1px solid var(--color-woocommerce-header-border);
			background: var(--color-surface);

			svg {
				margin-top: 15px;
			}
		}

		.formatted-header__subtitle {
			color: var(--color-primary-10);
			margin-top: 1em;
			font-size: $font-body;
			line-height: 24px;
			font-weight: 400;
		}

		.jetpack-connect__authorize-form {
			margin-top: 32px;
		}

		.jetpack-logo__icon-circle {
			fill: var(--color-neutral-80);
		}

		.jetpack-logo__icon-triangle {
			fill: var(--color-neutral-0);
		}

		.formatted-header__title {
			color: var(--color-neutral-80);
		}

		.signup-form__submit {
			border: 0;
			box-shadow: none;
		}

		.jetpack-header__partner-logo-plus svg {
			stroke: var(--color-neutral-80);
		}

		.jetpack-header__partner-logo-plus .gridicon {
			color: var(--color-neutral-80);
		}

		.jetpack-connect__main,
		.jetpack-connect__site.card,
		.logged-out-form,
		.jetpack-connect__logged-in-card.card {
			max-width: 476px;
		}

		.jetpack-connect__main {
			margin-left: auto;
			margin-right: auto;
		}

		.signup-form__terms-of-service-link,
		.signup-form__terms-of-service-link a {
			text-align: left;
			color: var(--color-neutral-60);
		}

		.signup-form__terms-of-service-link a {
			text-decoration: underline;
		}

		.logged-out-form__links {
			max-width: 400px;
		}

		.logged-out-form__link-item {
			text-align: center;
			text-decoration: underline;
			color: var(--color-neutral-60);
			font-size: $font-body-small;
		}

		.logged-out-form__footer,
		.login__form-footer {
			text-align: center;

			.button {
				max-width: 310px;
				margin-right: auto;
				margin-left: auto;
				height: 48px;
			}
		}

		.login__form-footer {
			margin-top: 24px;
		}

		.login__form {
			padding-top: 14px;
			border-radius: 2px;
			@include elevation( 2dp );
		}

		.login__form-change-username {
			display: none;
		}

		.jetpack-connect__logged-in-card button {
			border: 0;
			box-shadow: none;
		}

		.jetpack-connect__notices-container {
			margin-top: 20px;
		}

		.jetpack-connect__action-disclaimer {
			padding-top: 0;

			.jetpack-connect__tos-link,
			.jetpack-connect__tos-link a {
				color: var(--color-neutral-60);
				font-size: $font-body-extra-small;
			}
		}

		.signup-form__woocommerce .card {
			padding-bottom: 0;
			padding-top: 8px;
		}


		.signup-form__woocommerce-inputs-wrapper {
			padding: 12px 0;
			display: flex;
			flex-direction: column;
			gap: 14px;
		}

		.jetpack-connect__magic-link-card {
			text-align: center;
			border-radius: 2px;
			@include elevation( 1dp );
			padding: 32px;

			p {
				font-size: $font-body;
				line-height: 24px;
				color: var(--color-neutral-60);
				margin-bottom: 0;
			}

			img {
				margin: 24px;
			}

			.jetpack-connect__magic-link-resend {
				font-size: $font-body-small;

				button {
					cursor: pointer;
					text-decoration: underline;
					color: var(--color-neutral-60);
				}
			}
		}
	}
}

.jetpack-connect__features_wrapper {
	display: flex;
	flex-direction: row;
	gap: 18px;
	margin-top: 32px;

	.jetpack-connect__features {
		list-style: none;
		margin: 0;
		flex: 1;

		li {
			display: flex;
			margin-bottom: 8px;
			font-size: 0.75rem;
			color: $gray-800;
			align-items: center;
		}

		svg {
			fill: #008a20;
			margin-right: 8px;
			min-width: 20px;
		}
	}
}

.layout.is-section-jetpack-connect.is-woocommerce-core-profiler-flow {
	.jetpack-connect__main {
		max-width: 615px;
	}

	&.feature-flag-woocommerce-core-profiler-passwordless-auth {
		.jetpack-connect__tos-link {
			a.jetpack-connect__sso-actions-modal-link {
				color: var(--wp-admin-theme-color);
				text-decoration: underline;
				text-underline-offset: 2px;
			}
		}
	}

	.is-woo-passwordless,
	.feature-flag-woocommerce-core-profiler-passwordless-auth {
		.site-icon {
			border-radius: 4px;
		}

		.jetpack-connect__logged-in-card {
			padding: 32px 24px;
			align-items: center;
			border: 1px solid $gray-300;
		}

		a.logged-out-form__link-item {
			margin: 0;
			font-size: 0.875rem;
			font-weight: 400;
			line-height: 24px;
		}

		img.gravatar {
			width: 80px;
			height: 80px;
		}

		.jetpack-connect__logged-in-form-user {
			flex-direction: column;
		}

		.jetpack-connect__logged-in-form-user-text {
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-bottom: 13.5px;

			strong {
				font-size: 1rem;
				font-weight: 500;
				line-height: 24px;
				letter-spacing: -0.008px;
				display: block;
			}
		}

		.jetpack-connect__tos-link {
			margin: 24px auto;
			text-align: center;
			a {
				line-height: inherit;
			}
		}
	}

	.jetpack-connect__site.card {
		border: 1px solid $gray-300;
		border-radius: 2px;
		background: #fff;
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 16px 24px;
		gap: 16px;
		max-width: 405px;
		margin: 48px auto 16px;

		@include breakpoint-deprecated( "<660px" ) {
			max-width: 100%;
			margin: 40px 20px 16px;
		}

		.site__content {
			text-decoration: none;
			padding: 0;
		}

		.site-icon {
			border-radius: 50%;
			margin-right: 16px;
			height: 40px !important;
			width: 40px !important;
			background-color: #f0f0f1;

			svg {
				fill: $gray-600;
			}
		}

		.site__title {
			color: var(--studio-gray-100);
			font-size: 1rem;
			font-weight: 500;
		}

		.site__domain {
			font-size: 0.875rem;
			line-height: 24px;
			color: $gray-700;
		}
	}

	.jetpack-connect__logged-in-card {
		display: flex;
		flex-direction: column;
		padding: 16px 24px;
		background: #fff;
		border: 1px solid $gray-300;
		border-radius: 2px;
		max-width: 405px;
		margin: 0;

		@include breakpoint-deprecated( "<660px" ) {
			max-width: 100%;
		}

		.jetpack-connect__logged-in-form-user {
			display: flex;
			gap: 16px;
			align-items: center;
		}

		.gravatar {
			margin: 0;
		}

		.jetpack-connect__logged-in-form-user-text {
			color: var(--studio-gray-100);
			font-size: 1rem;
			font-weight: 500;
			text-align: left;

			small {
				font-size: 0.875rem;
				line-height: 24px;
				font-weight: 400;
				color: $gray-700;
			}
		}

		.logged-out-form__link-item {
			margin: 8px 0 0 56px;
			padding: 0;
			text-align: left;
			text-decoration: none;
			color: var(--wp-admin-theme-color);
			font-size: 0.875rem;
			line-height: 18px;
		}
	}

	.jetpack-connect__logged-in-content {
		margin: 0 auto;
		max-width: 405px;

		@include breakpoint-deprecated( "<660px" ) {
			max-width: 100%;
		}
	}

	.jetpack-connect__action-disclaimer {
		padding: 0;
		margin: 32px 0 0 0;

		.button {
			font-weight: 500;
		}
	}

	.jetpack-connect__tos-link {
		margin: 40px auto 70px;
		letter-spacing: -0.08px;
		line-height: 18px;
		color: $gray-700;
		font-size: 0.75rem;
		max-width: 400px;

		@include breakpoint-deprecated( "<660px" ) {
			margin: 12px auto 20px;
		}

		a {
			color: $gray-700;
			text-decoration: none;
			font-size: 0.75rem;
		}
	}

	.jetpack-connect__jetpack-logo-wrapper {
		display: flex;
		justify-content: center;
		align-items: center;

		svg {
			margin-right: 6px;
		}

		span {
			font-size: 0.75rem;
			color: #1e1e1e;
		}
	}

	.jetpack-connect__logged-in-bottom {
		.spinner__outer,
		.spinner__inner {
			border-top-color: #fff;
			border-right-color: transparent;
		}

		@include breakpoint-deprecated( "<660px" ) {
			position: absolute;
			bottom: 24px;
			padding-right: 20px;
			width: fill-available;
		}
	}

	.jetpack-connect__woo-core-profiler-notice {
		width: 262px;
		height: 50px;
		position: fixed;
		left: 36px;
		bottom: 32px;
		z-index: 999;
		margin-bottom: 0;

		@include breakpoint-deprecated( "<660px" ) {
			left: 0;
			bottom: 20px;
			width: calc(100% - 40px);
			margin: 0 20px;
		}

		.calypso-notice__icon-wrapper {
			background: inherit;
			padding: 0 3px 0 11px;
			width: fit-content;

			.calypso-notice__icon {
				color: var(--studio-green-30);
			}
		}

		.calypso-notice__content {
			font-size: 0.8125rem; /* stylelint-disable-line scales/font-sizes */
			line-height: 16px;
			padding: 17px 8px;
		}
	}

	// Sign up
	.jetpack-connect__authorize-form {
		.jetpack-connect__site.card {
			.site-icon {
				background-color: color-mix(in srgb, var(--wp-admin-theme-color) 15%, transparent);

				svg {
					fill: var(--wp-admin-theme-color);
				}
			}
		}
	}

	.signup-form__terms-of-service-link {
		margin-bottom: 12px;
	}

	.auth-form__social {
		padding-bottom: 0;
		margin-bottom: 48px;
	}
}

body.is-section-jetpack-connect {
	background-color: var(--color-surface);
}

// ProductCard overrides

.is-section-jetpack-connect .product-card {
	.product-card__header-primary {
		@include breakpoint-deprecated( "<660px" ) {
			justify-content: center;
		}
	}

	.product-card__title,
	.product-card__title:not(.is-purchased) {
		font-size: $font-title-small;
		color: var(--color-neutral-70);
	}

	.plan-price,
	.plan-price *,
	.product-card__billing-timeframe,
	.product-card__option .product-card__price-group .product-card__billing-timeframe {
		color: var(--color-neutral-70);
		font-style: normal;
		vertical-align: baseline;

		@include breakpoint-deprecated( ">660px" ) {
			font-weight: 600;
		}
	}

	.plan-price.is-original,
	.plan-price.is-original * {
		color: var(--color-neutral-light);
	}

	.product-card__billing-timeframe {
		font-size: $font-body-small;
	}

	.product-card__header {
		padding: 12px 0;

		@include breakpoint-deprecated( "<660px" ) {
			padding: 16px 0;
		}

		.plan-price,
		.plan-price * {
			font-size: $font-body;
			line-height: 21px;
		}
	}

	.product-card__option-name {
		text-align: left;
	}
}

.layout.is-section-jetpack-connect .upsell .upsell__header .formatted-header {
	color: inherit;
}

// The new Plans grid uses as primary color a darker version Jetpack's green.
.is-section-jetpack-connect .selector__main {
	--color-accent: var(--studio-jetpack-green-40);
	--color-primary: var(--studio-jetpack-green-40);
}
