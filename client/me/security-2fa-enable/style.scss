@import "@wordpress/base-styles/breakpoints";

.security-2fa-enable__code-block {
	margin-bottom: 16px;
}

.security-2fa-enable__qr-code {
	flex-direction: column;
	display: flex;
	align-items: center;

	&.is-placeholder {
		@include placeholder( --color-neutral-10 );
	}
}

.security-2fa-enable__app-options {
	font-size: $font-body-small;
	font-style: italic;
	margin-top: 10px;
}

.security-2fa-enable__one-time-code-container {
	display: flex;
	align-items: center;
	margin: 6px 0;
	position: relative;
}

.security-2fa-enable__one-time-code {
	font-weight: 600;
	font-size: $font-body-extra-small;
	margin-right: 8px;
}

.security-2fa-enable__clipboard-button {
	color: var(--color-text-subtle);

	&:hover {
		color: var(--color-primary);
	}
}

.security-2fa-enable__toggle {
	cursor: pointer;
	display: block;
	color: var(--color-link);
	font-size: 1rem;
}

.security-2fa-enable .calypso-notice {
	margin: 5px 0 0;
}

.security-2fa-enable__buttons-bar {
	margin-top: 16px;
	margin-left: 16px;
	display: flex;
	justify-content: flex-end;

	.button {
		margin-right: 0;
	}

	.security-2fa-enable__cancel {
		margin-right: 10px;
	}

	.security-2fa-enable__resend {
		margin-right: 10px;
	}
}

.security-2fa-enable__qr-code-block {
	display: flex;
	justify-content: space-between;

	@media (max-width: $break-xlarge) {
		flex-direction: column;
	}
}

.security-2fa-enable__steps {
	margin-left: 16px;

	li {
		padding-bottom: 12px;
	}
}

.security-2fa-enable__copy-description {
	margin-top: 8px;
	margin-bottom: 0;
	font-size: $font-body-small;
}
