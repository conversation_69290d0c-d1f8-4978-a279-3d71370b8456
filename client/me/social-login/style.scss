.social-login__header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
	box-sizing: border-box;
}
.social-login__header-info {
	display: flex;
	align-items: center;
	flex: 2 1;
	margin-right: 5px;

	h3 {
		text-transform: capitalize;
		margin-right: 3px;
	}

	p {
		margin-bottom: 0;
	}

	@include breakpoint-deprecated( "<960px" ) {
		p {
			display: none;
		}
	}
}
.social-login__header-icon {
	height: 30px;
	width: 30px;
	margin-left: 0;
	margin-right: 12px;

	svg {
		width: 100%;
		height: 100%;
	}
}
.social-login__header-action {
	display: flex;
	align-items: center;
	flex: 1 1;
	justify-content: flex-end;
}
.social-login__header-action__error {
	.popover__inner {
		padding: 10px;
		max-width: 200px;
	}
}
.social-login__description {
	text-wrap: balance;
}
