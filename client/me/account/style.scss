@import "@automattic/typography/styles/variables";
@import "@wordpress/base-styles/breakpoints";

.account__username-form-toggle-enter {
	opacity: 0.01;
	transition: opacity 0.5s ease-in;
}

.account__username-form-toggle-enter-active {
	opacity: 1;
}

.account__username-form-toggle-exit {
	opacity: 1;
	transition: opacity 0.01s ease-in;
}

.account__username-form-toggle-exit-active {
	opacity: 0.01;
}

.account__settings-form select {
	width: 100%;
}

.account__settings {
	.form-fieldset {
		margin-bottom: 30px;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.components-radio-control {
		label {
			font-size: $font-body-small;
		}
	}
}

.form-setting-explanation {
	.account-email-field__enable-input.components-button.is-link {
		color: inherit;
		font-style: inherit;
	}
}

.account__link-destination {
	.components-base-control__field {
		align-items: flex-start;
		margin-top: 0.5em;
	}

	.components-toggle-control__label {
		color: var(--color-text-subtle);

		strong {
			display: block;
		}
	}

	.account__link-destination-label-extra {
		font-style: italic;
		margin-top: 0.1em;
	}

	.inline-support-link {
		margin-left: 4px;
	}
}

.account .form-input-validation {
	padding-bottom: 0;
}

.account__confirm-username-dialog.dialog {
	max-width: 600px;

	.form-label {
		font-size: $font-title-small;
		margin-bottom: 16px;
	}

	p {
		font-size: $font-body;
	}

	a {
		color: inherit;
		text-decoration: underline;
	}
}

main.account {
	.email-verification-banner {
		margin-bottom: 16px;
		margin-top: 0;

		@media (min-width: $break-small) {
			margin-top: 24px;
			margin-bottom: 24px;
		}
	}

	&:has(.email-verification-banner) .navigation-header {
		@media (min-width: $break-small) {
			padding-bottom: 0;
		}
	}
}
