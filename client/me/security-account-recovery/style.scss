.security-account-recovery__text {
	margin-bottom: 0;
}

.security-account-recovery-contact__detail .form-phone-input {
	display: flex;
	flex-direction: column;

	@include breakpoint-deprecated( ">480px" ) {
		flex-direction: row;
	}
}

.security-account-recovery-contact__detail .form-phone-input__country {
	padding-right: 0;

	@include breakpoint-deprecated( ">480px" ) {
		padding-right: 10px;
		margin-bottom: 0;
	}
}

.security-account-recovery-contact__detail .form-country-select {
	width: 100%;

	@include breakpoint-deprecated( ">480px" ) {
		min-width: 0;
		width: 100%;
		max-width: 200px;
	}
}

.security-account-recovery-contact__detail .form-phone-input__phone-number {
	margin-bottom: 0;

	@include breakpoint-deprecated( ">480px" ) {
		flex: 2 0;
	}
}

.security-account-recovery-contact .calypso-notice {
	margin-top: 8px;
}

.security-account-recovery-contact__placeholder-heading,
.security-account-recovery-contact__placeholder-text {
	animation: loading-fade 1.6s ease-in-out infinite;
	background-color: var(--color-neutral-0);
	line-height: 1;
	margin-bottom: 0.5em;
}

.security-account-recovery-contact__placeholder-heading {
	line-height: 2.4;
	margin-bottom: 1em;
	@include breakpoint-deprecated( ">480px" ) {
		width: 45%;
	}
}

.security-account-recovery-contact__header {
	display: flex;
	flex-direction: row;
}

.security-account-recovery-contact__header-action .form-button {
	margin: 0;
}

.security-account-recovery-contact__header-info {
	flex: 2;
}

.security-account-recovery-contact__header-subtitle {
	display: block;
	font-style: italic;
	color: var(--color-text-subtle);
}

.security-account-recovery-contact__detail {
	margin-top: 8px;
}

.security-account-recovery__remove {
	color: var(--color-text-subtle);
	float: right;
	cursor: pointer;
	padding: 9px 0;

	&:hover {
		color: var(--color-neutral-50);
	}

	.gridicon {
		vertical-align: bottom;
		margin-right: 2px;
		width: 16px;
		height: 16px;
	}
}

.security-account-recovery__validation-notice {
	margin-top: 4px;
	margin-bottom: 12px;
}

.security-account-recovery__recovery-phone-validation-buttons {
	margin-top: 12px;
}

.security-account-recovery__recovery-phone-validation-label {
	margin-top: 12px;
}

.recovery-phone-edit {
	align-items: center;
	display: flex;

	&__information {
		flex: 1;
	}

	&__actions {
		display: flex;
		flex-direction: row;
		gap: 10px;
	}
}

.security-account-recovery__recovery-sms-number {
	.security-account-recovery-contact:not(:only-child) {
		&::after {
			background-color: var(--color-border-subtle);
			content: '';
			display: block;
			height: 1px;
			margin: 16px 0;
			width: 100%;
		}
	}
}
