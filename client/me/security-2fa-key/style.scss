.security-2fa-key {
	margin: 16px 0;
}

.security-2fa-key__add-wait-for-key {
	text-align: center;

	.spinner {
		margin-bottom: 1em;
	}

	p {
		margin-bottom: 0.5em;
	}
}

.security-2fa-key__add-wait-for-key-heading {
	font-weight: 600;
}

.security-2fa-key__add-button-container {
	display: flex;
	justify-content: flex-end;
	margin-top: 2em;
}

.security-2fa-key__list {
	list-style-type: none;
	margin: 0;
}

.security-2fa-key__item {
	display: flex;
	align-items: center;
}

.security-2fa-key__item-information {
	flex-grow: 1;
	flex-shrink: 0;
}

.security-2fa-key__item-subtitle {
	color: var(--muriel-gray-400);
	font-size: 0.9em;
	margin-bottom: 0;
}

.security-2fa-key__delete-key {
	flex-grow: 0;
	flex-shrink: 0;
}
