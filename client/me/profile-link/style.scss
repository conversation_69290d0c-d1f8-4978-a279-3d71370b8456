.profile-link {
	margin-bottom: 15px;
	position: relative;
	padding-left: 62px;
	height: 50px;

	&.is-placeholder {
		.profile-link__image-link,
		.profile-link__title,
		.profile-link__url,
		.profile-link__remove {
			color: transparent;
			background-color: var(--color-neutral-0);
			animation: loading-fade 1.6s ease-in-out infinite;
		}

		.profile-link__title {
			height: 18px;
			margin-bottom: 2px;
		}

		.profile-link__url {
			height: 12px;
		}

	}

	.profile-link__remove {
		float: none;
		position: absolute;
		top: 0;
		right: 0;
	}
}

.profile-link__image-link {
	display: block;
	height: 32px;
	width: 50px;
	position: absolute;
	top: 0;
	left: 0;
	border: 1px solid var(--color-neutral-0);
}

.profile-link__link {
	text-decoration: none;
}

.profile-link__title {
	color: var(--color-neutral-70);
	float: left;
	font-size: $font-body-small;
	font-weight: 400;
	line-height: 1.4;
	overflow: hidden;
	white-space: nowrap;
	width: 100%;
	text-decoration: none;

	&::after {
		@include long-content-fade( $size: 45% );
	}
}

.profile-link__url {
	font-size: $font-body-extra-small;
	line-height: 1.4;
	color: var(--color-neutral-light);
	float: left;
	clear: left;
}
