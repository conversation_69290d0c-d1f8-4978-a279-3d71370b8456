@import "@automattic/typography/styles/variables";
@import "@wordpress/base-styles/breakpoints";

.profile__settings .edit-gravatar {
	text-align: center;

	@include breakpoint-deprecated(">960px") {
		float: left;
		margin-right: 40px;
		margin-bottom: 20px;
	}
}

@media (min-width: 961px) {
	.me-profile-settings .edit-gravatar {
		margin-bottom: 0;
	}
}

.profile__settings .form-fieldset:nth-child(1),
.profile__settings .form-fieldset:nth-child(2) {
	@include breakpoint-deprecated(">960px") {
		clear: right;
	}
}

.profile__settings .profile__public-url {
	font-size: $font-body-small;
}

.profile__is_dev_account-fieldset-is-loading {
	.components-form-toggle {
		> * {
			visibility: hidden;
		}
		.components-form-toggle__track {
			visibility: visible;
			border: none;
			@include placeholder( --color-neutral-10 );
		}
	}
	.components-toggle-control__label {
		@include placeholder();
		a {
			color: inherit;
		}
	}
}

.profile__gravatar-profile-disclosure {
	display: flex;
	flex-direction: column-reverse;
	justify-content: space-between;
	margin-bottom: 1.5em;
	font-size: $font-body-small;
	line-height: 20px;
	word-break: break-word;
	gap: 5px;

	.profile__gravatar-profile-title {
		font-weight: 600;
	}

	.profile__gravatar-profile-description {
		margin: 5px 0 0;
	}

	@include breakpoint-deprecated( '>960px' ) {
		flex-direction: row;
		gap: 2em;
	}
}

.profile__submit-button-wrapper {
	margin-bottom: 0;
}

.profile-links__list {
	.profile-link:last-of-type {
		margin-bottom: 0;
	}
}

main.profile {
	.email-verification-banner {
		margin-bottom: 16px;
		margin-top: 0;

		@media (min-width: $break-small) {
			margin-top: 24px;
			margin-bottom: 24px;
		}
	}

	&:has(.email-verification-banner) .navigation-header {
		@media (min-width: $break-small) {
			padding-bottom: 0;
		}
	}
}