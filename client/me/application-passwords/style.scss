@import "@wordpress/base-styles/breakpoints";
@import "@wordpress/base-styles/mixins";

.application-passwords {
	margin-bottom: 16px;
}

.application-passwords__list {
	list-style-type: none;
	margin: 0;
}

.application-passwords__add-new {
	margin-bottom: 1em;
}

.application-passwords__add-new-card {
	display: none;
	&.is-visible {
		display: block;
	}
}

.application-passwords__new-password {
	margin-bottom: 1em;

	.form-buttons-bar {
		text-align: right;
	}
}

.application-passwords__new-password-display {
	align-items: center;
	background: var(--color-neutral-0);
	border: 1px solid var(--color-border-subtle);
	display: flex;
	font-size: 150%;
	font-weight: 600;
	justify-content: space-between;
	letter-spacing: 2px;
	margin: 10px auto;
	padding: 5px;
	text-align: center;
	max-width: 70%;

	@include break-mobile {
		font-size: 200%;
	}

	.application-passwords__new-password-content {
		flex-grow: 1;
		flex-shrink: 1;
	}

	.application-passwords__new-password-copy {
		flex-grow: 0;
		flex-shrink: 0;
	}
}

.application-passwords__new-password-message {
	color: var(--color-neutral-40);
	font-size: 0.9em;
}

.application-passwords__new-password-help {
	text-align: center;
}

.application-passwords__explanation {
	margin: 0;
}
