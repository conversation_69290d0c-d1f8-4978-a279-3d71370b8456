// booking status
export const CONCIERGE_STATUS_BOOKED = 'booked';
export const CONCIERGE_STATUS_BOOKING = 'booking';
export const CONCIERGE_STATUS_BOOKING_ERROR = 'booking_error';

// cancelling status
export const CONCIERGE_STATUS_CANCELLED = 'cancelled';
export const CONCIERGE_STATUS_CANCELLING = 'cancelling';
export const CONCIERGE_STATUS_CANCELLING_ERROR = 'cancelling_error';

// error codes
export const CONCIERGE_ERROR_NO_AVAILABLE_STAFF = 'rest_concierge_no_available_staff';
export const CONCIERGE_ERROR_ALREADY_HAS_APPOINTMENT = 'rest_concierge_already_has_appointment';

// schedule IDs
export const CONCIERGE_WPCOM_BUSINESS_ID = 1;
export const CONCIERGE_WPCOM_SESSION_PRODUCT_ID = 2;

// /me/purchases page banner content type
export const CONCIER<PERSON>_HAS_UPCOMING_APPOINTMENT = 'has-upcoming-appointment';
export const CONCIER<PERSON>_HAS_AVAILABLE_SESSION = 'has-available-session';
