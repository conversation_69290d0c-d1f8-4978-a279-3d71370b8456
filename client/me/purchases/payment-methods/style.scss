$card_width: $font-body * 3.75; // roughly 60px wide
@mixin payment-details-col-start( $reset: false ) {
	// Handles the logo column spacing
	@if $reset == false {
		margin-inline-start: calc( #{$card-width} + #{$font-body} );
	} @else {
		margin-inline-start: 0;
	}
}

.payment-method-list__payment-methods {
	margin-top: 12px;

	> div {
		width: 100%;
	}
}

.payment-method-list__loader {
	display: flex;
}

.payment-method-list__loading-placeholder-card {
	width: 60px;
	margin-right: 16px;
}

.payment-method-list__loading-placeholder-details {
	flex-grow: 1;
}

.payment-method {
	display: grid;
	grid-template-areas:
		'payment-method-details'
		'payment-method-billing-information'
		'payment-method-backup'
		'payment-method-delete';
	grid-template-columns: 1fr;
	align-items: center;
	width: 100%;
	gap: 0.75rem 1rem;

	@include breakpoint-deprecated( '>480px' ) {
		grid-template-areas:
			'payment-method-details payment-method-backup'
			'payment-method-billing-information payment-method-delete';
		grid-template-columns: 2fr 1fr;
	}

	// Payment Method Details (Row 1)
	&-details {
		display: flex;
		align-items: center;
		grid-area: payment-method-details;
		gap: $font-body;
		width: 100%;

		&__image {
			width: 60px;
			height: 38px;
			flex-shrink: 0;
		}

		&__details {
			display: flex;
			flex-wrap: wrap;
			flex-grow: 1;
			gap: 0.25rem 0.5rem;
			align-items: center;
			min-width: 0; // Prevents overflow issues
		}

		&__name,
		&__vpa {
			color: var( --color-neutral-50 );
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			flex-basis: 100%; // Ensures full-width name/VPA line
			font-size: $font-body;
			gap: 0.5rem;
		}

		&__number,
		&__expiration-date {
			color: var( --color-neutral-80 );
			white-space: nowrap;
			font-size: $font-body-small;
			display: inline-flex;
			min-width: 0;
		}

		&__expiration-notice {
			display: flex;
			align-items: center;
			color: var( --color-warning );
			font-size: $font-body-small;

			.gridicon {
				margin-right: 2px;
			}
		}
	}

	// Billing Info (Row 2)
	&-tax-info {
		@include payment-details-col-start;
		font-size: $font-body-small;
		grid-area: payment-method-billing-information;

		@include breakpoint-deprecated( '>480px' ) {
			width: auto;
		}
	}

	// Backup Toggle & Delete Button (Share common styles)
	&-backup-toggle,
	&-delete {
		@include payment-details-col-start;

		@include breakpoint-deprecated( '>480px' ) {
			@include payment-details-col-start( true );
			justify-self: flex-end;

			&__button {
				text-align: end;
			}
		}
	}

	&-backup-toggle {
		grid-area: payment-method-backup;

		// Ensure checkbox, label, and icon align properly
		.components-flex {
			display: flex;
			align-items: center;
			column-gap: 0.5rem;
			min-width: 0; // Ensures text wraps naturally if needed
		}

		// Ensure the text takes up available space
		.components-checkbox-control__label {
			display: flex;
			align-items: center;
			flex-grow: 1;
			white-space: normal;
			column-gap: 0.25rem;
		}

		// Prevent checkbox and icon from shrinking
		.components-base-control__field,
		.components-checkbox-control__input-container,
		.inline-support-link {
			margin: 0;
			flex-shrink: 0;
		}
	}

	&-delete {
		grid-area: payment-method-delete;

		&__button {
			text-decoration: underline;
			padding: 0;
		}
	}
}

.payment-method-delete-dialog {
	--max-width: 660px;

	&__explanation {
		display: flex;
		gap: 12px;
		align-items: flex-start;
	}

	&__header {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	&__affected-subscriptions-wrapper {
		margin-top: 12px;
		display: flex;
		flex-direction: column;
		gap: 12px;
	}


	&__affected-subscriptions-table {
		border-collapse: collapse;
		table-layout: fixed;
		width: 100%;
		thead tr {
			border-bottom: 1px solid #ddd ;
		}
		th, td {
			padding: 12px 4px;
		}
		th:first-child {
			width: 70%;

		}
	}
	&__affected-subscription-details-product {
		display: flex;
		flex-direction: column;
		gap: 2px;
		padding-right: 12px;
		text-wrap: nowrap;
		overflow-x: auto;
		scrollbar-width: thin;
		scrollbar-gutter: stable;
	}

	&__affected-subscription-details-renew-date {
		vertical-align: middle;
		text-wrap: nowrap;
	}


	&__warning {
		display: flex;
		gap: 8px;
		.gridicon {
			fill: var( --color-warning );
		}
		border: 1px solid var(--color-warning);
		padding: 8px;
		border-radius: 4px;

		p {
			color: var( --color-warning );
			font-size: $font-body-small;
			margin: 0;
		}
	}
}

.payment-method__wrapper {
	margin-bottom: 0;

	&--jetpack-cloud {
		.components-checkbox-control__input[type='checkbox']:checked {
			background-color: var( --studio-jetpack-green-50 );
		}
	}
}
