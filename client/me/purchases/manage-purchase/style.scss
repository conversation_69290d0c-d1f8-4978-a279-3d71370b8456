.manage-purchase > button.card,
.manage-purchase > a.card,
.manage-purchase > div > a.card,
.manage-purchase > div > button.card{
	--icon-size: 16px;

	padding-inline-start: calc(var(--icon-size) * 2.5);
	padding-inline-end: var(--icon-size);

	@include breakpoint-deprecated( ">660px" ) {
		--icon-size: 24px;
	}

	color: var(--color-text-subtle);

	.card__icon {
		position: absolute;
		left: var(--icon-size);
		top: 0;
		width: var(--icon-size);
		height: 100%;
		fill: currentcolor;
	}

	.card__link-indicator {
		right: var(--icon-size);
	}
}

.manage-purchase__info {
	font-size: $font-body-small;
	padding: 0;

	@include breakpoint-deprecated( ">660px" ) {
		font-size: $font-body-small;
		padding: 0;
	}

	&.is-expired {
		background: var(--color-neutral-0);

		.manage-purchase__header,
		.manage-purchase__content,
		.manage-purchase__detail-label,
		.manage-purchase__detail,
		.manage-purchase__detail .user {
			opacity: 0.7;
		}
	}

	&.is-expired + .calypso-notice {
		margin-top: -10px;

		@include breakpoint-deprecated( ">480px" ) {
			margin-top: -16px;
		}
	}

	&.is-placeholder {
		.manage-purchase__plan-icon,
		.manage-purchase__title,
		.manage-purchase__subtitle,
		.manage-purchase__description,
		.manage-purchase__settings-link,
		.manage-purchase__detail-label,
		.manage-purchase__detail {
			@include placeholder( --color-neutral-10 );

			display: block;
		}

		.manage-purchase__plan-icon {
			height: 56px;
			border-radius: 50%;
		}

		.manage-purchase__header {
			border-bottom: 2px solid var(--color-neutral-0);
		}

		.manage-purchase__subtitle,
		.manage-purchase__title {
			margin-bottom: 3px;
			margin-left: 64px;
		}

		.manage-purchase__detail-label {
			margin-bottom: 3px;
		}

		.manage-purchase__settings-link {
			width: 30%;
		}

		.manage-purchase__subtitle {
			width: 40%;
		}

		.manage-purchase__title {
			width: 60%;
		}

		@include breakpoint-deprecated( ">660px" ) {
			.manage-purchase__description,
			.manage-purchase__detail,
			.manage-purchase__detail-label {
				height: 40px;
			}
		}

		@include breakpoint-deprecated( "<660px" ) {
			.manage-purchase__detail {
				width: 25%;
			}

			.manage-purchase__detail-label {
				width: 70%;
			}
		}
	}
}

.manage-purchase__description.is-placeholder {
	@include placeholder( --color-neutral-10 );
}

.manage-purchase__header,
.manage-purchase__content {
	padding: 16px 24px;
	word-wrap: break-word;
	overflow-wrap: break-word;

	@include breakpoint-deprecated( "<660px" ) {
		padding: 16px;
		flex-direction: column;
	}
}

.manage-purchase__content-domain-description {
	margin-bottom: 4px;
}

.manage-purchase__header {
	border-bottom: 2px solid var(--color-accent);
	display: flex;
	gap: .75rem;

	&-content {
		flex: 1 1 auto;
		min-width: 0;
	}

	.is-personal & {
		border-bottom: 2px solid var(--color-plan-personal);
	}

	.is-premium & {
		border-bottom: 2px solid var(--color-plan-premium);
	}

	.is-business & {
		border-bottom: 2px solid var(--color-plan-business);
	}

	.is-jetpack-product & {
		border-bottom: 2px solid var(--studio-jetpack-green-30);
	}
}

.manage-purchase__plan-icon {
	max-width: 56px;
	width: 56px;

	.gridicon {
		box-sizing: border-box;
		padding: 4px;
		border-radius: 50%;
		background: var(--color-accent);
		fill: var(--color-text-inverted);
	}

	.gridicons-my-sites {
		background: unset;
		fill: var(--color-wordpress-com);
		margin-left: -3px;
		margin-top: -3px;
		padding: 0;
		transform: scale(1.1);
	}

	.gridicons-themes {
		/* stylelint-disable-next-line  scales/radii */
		border-radius: 8px; /* stylelint-disable-line scales/radii */
	}

	.hundred-year-plan-logo {
		filter: invert(1);
		padding-top: 6px;
	}
}

.manage-purchase__title {
	color: var(--color-text);
	display: block;
	font-size: $font-title-small;
	font-weight: 400;
	clear: none;

	@include breakpoint-deprecated( ">660px" ) {
		font-size: $font-title-small;
	}
}

.manage-purchase__price .plan-price {
	clear: none;
}

.manage-purchase__contact-partner {
	font-weight: 600;
	display: block;
}

.manage-purchase__description {
	color: var(--color-text-subtle);
	display: block;

	.plan-features__targeted-description-heading {
		display: inline;
	}
}

.manage-purchase__refund-text {
	display: block;
	font-size: $font-body-extra-small;
	font-weight: 400;
}

.manage-purchase__settings-link {
	display: block;
	margin-top: 16px;
}

.manage-purchase__meta {
	display: flex;
	margin: 0;
	list-style: none;
	overflow: auto;
	padding: 0 24px;
	border-top: 1px solid var(--color-neutral-0);

	@include breakpoint-deprecated( "<660px" ) {
		flex-direction: column;
		padding: 0 16px 16px;
	}

	li {
		color: var(--color-neutral-60);
		font-size: $font-body-small;

		@include breakpoint-deprecated( "<660px" ) {
			clear: both;
			overflow: auto;
			margin-top: 15px;
		}

		@include breakpoint-deprecated( ">660px" ) {
			box-sizing: border-box;
			flex: 1 1 auto;
			font-size: $font-body-small;
			padding: 15px 16px 12px;
			min-height: 130px;

			&:first-child {
				padding-left: 0;
			}

			&:last-child {
				padding-right: 0;
			}

			&:first-child:nth-last-child(4),
			&:first-child:nth-last-child(4) ~ li {
				max-width: 25%;
			}
		}

		+ li {
			@include breakpoint-deprecated( ">660px" ) {
				border-left: 1px solid var(--color-neutral-0);
			}
		}

		> a {
			color: var(--color-neutral-60);
			display: block;
			text-decoration: none;
		}
	}
}

.manage-purchase__detail-label {
	font-style: normal;
	font-weight: 600;

	@include breakpoint-deprecated( "<660px" ) {
		flex-direction: row;
		float: left;
	}

	@include breakpoint-deprecated( ">660px" ) {
		color: var(--color-text-subtle);
		display: block;
		font-family: $sans;
		font-size: $font-body-extra-small;
		font-weight: 400;
		margin: 0 0 12px;
	}
}

.manage-purchase__detail {
	display: block;
	text-align: right;

	@include breakpoint-deprecated( ">660px" ) {
		text-align: left;
	}

	& > button.info-popover {
		position: relative;
		top: 3px;
	}

	.components-toggle-control {
		display: inline-block;

		.components-form-toggle {
			margin-right: initial;
		}
	}

	.manage-purchase__payment-method {
		display: flex;
		justify-content: flex-end;
		margin-bottom: 6px;

		@include breakpoint-deprecated( ">660px" ) {
			justify-content: flex-start;
		}
	}

	.manage-purchase__no-payment-method {
		display: flex;
		align-items: center;
		gap: 4px;

		svg {
			flex-shrink: 0;
			fill: var(--color-warning-30);
		}
	}

	.payment-logo {
		margin-right: 5px;
		border-radius: 2px;
	}

	a {
		display: block;
		cursor: pointer;
	}

	.manage-purchase__backup-payment-method-notice a {
		display: inline;
	}

	&.is-expiring .manage-purchase__detail-date-span {
		color: var(--studio-red);
	}
}

.manage-purchase__renewal-text {
	display: inline-block;
}

.manage-purchase__detail .user__name {
	margin-right: 0;
}

.manage-purchase__auto-renew-toggle .components-base-control__field {
	margin-bottom: 6px;
}

.manage-purchase__expiring-credit-card-notice.calypso-notice,
.manage-purchase__purchase-expiring-notice.calypso-notice {
	margin-bottom: 10px;

	@include breakpoint-deprecated( ">660px" ) {
		margin-bottom: 16px;
	}
}

.manage-purchase__renew-upgrade-buttons {
	display: flex; /* Ensures button expands */
	flex-direction: column;
	flex-shrink: 0;
	gap: .75rem;

	> button {
		flex-grow: 1; /* Ensures button expands based on its text */
		white-space: nowrap;
	}

	@include breakpoint-deprecated( ">480px" ) {
		margin-inline-end: auto;
		flex-direction: row;
	}

	@include breakpoint-deprecated( ">660px" ) {
		align-self: flex-start;
		margin-inline-start: auto;
	}
}

.manage-purchase__footnotes {
	color: var(--color-text-subtle);
	border-top: solid 1px var(--color-neutral-0);
	margin: 0 auto;
	padding: 16px;
	background: var(--color-surface);

	@include breakpoint-deprecated( ">480px" ) {
		padding: 16px 24px;
	}
}

.manage-purchase__detail-date-span {
	white-space: nowrap;
}

.manage-purchase__savings-badge {
	margin-left: 12px;
}

.manage-purchase__license-clipboard-container {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	margin-bottom: 0;

	@include breakpoint-deprecated( ">480px" ) {
		flex-wrap: nowrap;
	}

	@include breakpoint-deprecated( "<660px" ) {
		padding: 16px;
		box-shadow: none;
	}

	strong {
		color: var(--color-neutral-60);
	}
}

.manage-purchase__license-clipboard {
	display: flex;
	margin-top: 16px;

	input.form-text-input.manage-purchase__license-clipboard-input {
		font-size: $font-body-small;
		margin-right: 16px;
		background-color: var(--studio-gray-0);
		border: none;

		@include breakpoint-deprecated( ">480px" ) {
			margin: 0 16px;
		}
	}

	.button {
		min-width: 70px;
	}

	@include breakpoint-deprecated( ">480px" ) {
		margin-top: 0;
	}

	&.loading {
		@include placeholder( --color-neutral-20 );
		padding: 0.5rem;
		margin: 0 16px;
		width: 200px;
	}
}

.manage-purchase__license-clipboard-link {
	margin-block-start: 8px;

	@include breakpoint-deprecated(">480px") {
		margin-block-start: 0;
		margin-inline-start: 16px;
	}
}

.manage-purchase__auto-renew-text button.is-link {
	color: var(--color-link);
	text-decoration: underline;
	line-height: inherit;
}

.manage-purchase__downgrade-products {
	margin-top: 16px;
	margin-bottom: 16px;
	& > a.card,
	& > button.card {
		margin-top: 0;
		margin-bottom: 0;
	}
}
