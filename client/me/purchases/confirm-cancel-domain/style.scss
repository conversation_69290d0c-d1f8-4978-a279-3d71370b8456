.confirm-cancel-domain {
	// the following is a hack so the dropdown selector is not cropped when it overflows its container
	margin-bottom: 300px;

	h3,
	h4 {
		&:first-of-type {
			margin-top: 10px;
		}
	}
}

.confirm-cancel-domain__card {
	padding: 32px;
	padding-top: 12px;

	.purchases__cancel-domain & {
		box-shadow: none;
		padding: 0;
	}
}

.confirm-cancel-domain__help-message,
.confirm-cancel-domain__confirm-container {
	margin-top: 20px;

	.form-label {
		margin-top: 0;
		margin-bottom: 1.5em;
	}
}

.confirm-cancel-domain__reason-details {
	height: 136px;
}

.confirm-cancel-domain__reasons-dropdown {
	width: 100%;
}
