// View Receipt
.billing-history__app-overview {
	align-items: center;
	display: flex;
	flex-wrap: wrap;
	min-height: 65px;
	padding: 10px 20px;
	position: relative;

	@include breakpoint-deprecated( ">480px" ) {
		flex-wrap: nowrap;
		padding: 10px 40px;
	}

	img {
		width: 65px;
		height: 65px;
	}

	h2 {
		padding: 0 10px;

		@include breakpoint-deprecated( ">480px" ) {
			padding: 0 20px;
		}

		small {
			display: block;
		}
	}

	.billing-history__transaction-date {
		color: var(--color-neutral);
		font-size: $font-body-small;
		padding: 20px 0 0;

		@include breakpoint-deprecated( ">480px" ) {
			padding: 5px 0 0;
			position: absolute;
			top: 10px;
			right: 40px;
		}
	}
}

.billing-history__billing-details div[contenteditable] {
	min-height: 1.2em;
}

textarea.billing-history__billing-details-editable {
	min-height: 20px;
}

.billing-history__billing-details-description {
	display: block;
	font-size: $font-body-small;
	font-style: italic;
}

.billing-history__billing-details-editable {
	border: 1px solid var(--color-neutral-20);
	margin-top: 5px;
	font-size: $font-body-small;
}

.billing-history__receipt-details {
	background: var(--color-neutral-0);
	list-style: none;
	padding: 20px 40px;
	margin: 10px 0 0;
	overflow: auto;

	@include breakpoint-deprecated( "<480px" ) {
		padding: 20px;
	}

	li {
		color: var(--color-neutral-70);
		font-size: $font-body-small;
		margin: 0 0 15px;
		padding: 0;

		&:last-child {
			margin: 0;
		}

		strong,
		.form-label {
			color: var(--color-neutral-50);
			display: block;
			font-size: $font-body-extra-small;
			font-weight: 600;
			margin: 0 5px 0 0;
			text-transform: uppercase;
		}

		.receipt__monospace {
			font-family: Courier, "Courier New", monospace;
			text-transform: uppercase;
		}

		.billing-history__billing-details-readonly {
			display:none;
		}

		.receipt__vat-vendor-details-number {
			display: block;
			margin-top: 8px;

			strong {
				display: inline;
				margin: 0;
			}
		}

		.receipt__vat-vendor-details-description {
			display: block;
			font-size: $font-body-small;
			font-style: italic;
			margin-bottom: 8px;

			a,
			.receipt__email-button {
				color: var(--color-link);
				font-style: italic;
				text-decoration: underline;
			}
		}
	}
}

.billing-history__receipt {
	padding: 30px 40px;

	@include breakpoint-deprecated( "<480px" ) {
		padding: 30px 20px;
	}

	h4 {
		font-size: $font-title-small;
	}

	.billing-history__receipt-loading {
		color: var(--color-neutral-light);
	}

	.billing-history__receipt-line-items {
		margin: 0;
		padding: 0;
		width: 100%;

		th {
			border-bottom: 2px solid var(--color-border-subtle);
			color: var(--color-text-subtle);
			font-size: $font-body-extra-small;
			font-weight: 400;
			text-transform: uppercase;
			width: auto;
		}

		td,
		th {
			padding: 10px 0;
		}

		.billing-history__receipt-desc {
			width: 75%;
			text-align: left;
		}

		.billing-history__receipt-amount {
			text-align: right;
			vertical-align: middle;
		}

		.billing-history__receipt-item-name {
			small {
				color: var(--color-text-subtle);
				font-size: $font-body-small;
				margin-left: 5px;
				text-transform: lowercase;
			}
			em {
				color: var(--color-text-subtle);
				display: block;
				font-size: $font-body-small;
			}
		}

		tbody tr {
			td {
				color: var(--color-neutral);
			}

			&:last-child td {
				border: none;
			}
		}

		tfoot {
			font-weight: 400;
			text-align: right;
			vertical-align: bottom;

			td {
				border-top: 2px solid var(--color-border-subtle);
				padding-bottom: 0;
			}
		}
	}
}

.billing-history__receipt tr td.billing-history__receipt-item-discounts {
	padding: 4px 0;
}

ul.billing-history__receipt-item-discounts-list {
	list-style-type: none;
	margin: 0 0 0 30px;
	font-size: $font-body-small;
}

li.billing-history__receipt-item-discount {
	display: flex;
	justify-content: space-between;
}

li.billing-history__receipt-item-discount--different-term {
	display: block;
}

.billing-history__receipt-links {
	padding: 0 40px 10px;
}

.billing-history__receipt-card.card.is-compact {
	padding: 16px 0;
}

.billing-history__receipt-card.is-placeholder {
	.billing-history__app-overview {
		display: flex;
		flex-wrap: nowrap;
		margin-bottom: 16px;

		.billing-history__placeholder-image,
		.billing-history__placeholder-title {
			@include placeholder();
			height: 65px;
		}

		.billing-history__placeholder-image {
			flex-shrink: 0;
			width: 65px;
			margin-right: 26px;
		}

		.billing-history__placeholder-title {
			flex-shrink: 1;
			width: 100%;
		}
	}

	.billing-history__receipt-links {
		padding: 0 40px 10px;

		.billing-history__placeholder-link {
			@include placeholder();
			margin-bottom: 12px;

			&:last-child {
				margin-bottom: 0;
			}
		}
	}
}

@media print {
	.is-section-me,
	.is-section-billing,
	.is-section-site-purchases,
	.is-section-purchases {
		#secondary,
		.masterbar,
		.billing-history__receipt-links,
		.header-cake.card,
		.billing-history__billing-details-description,
		.receipt__no-print {
			display: none !important;
		}

		.layout__primary .main
		{
			height: auto !important;
		}

		.billing-history__receipt-details {
			.billing-history__billing-details-readonly {
				display: block;
				white-space: pre-wrap
			}
		}
	}

	body {
		font-family: $sans;
	}
}


#purchases {
	.navigation-header__main {
		align-items: center;
		flex-wrap: wrap;
		gap: .65rem;
	}
}
