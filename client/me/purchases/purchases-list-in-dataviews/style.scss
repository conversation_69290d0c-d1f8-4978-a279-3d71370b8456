/* Purchase listing
================================================== */
#purchases-list {
	padding: 8px 0 0;

	.dataviews__view-actions {
		margin-top: 0;
		padding-left: 24px;
		padding-right: 24px;
	}

	.dataviews-wrapper .dataviews-view-table__row th:last-child,
	.dataviews-wrapper .dataviews-view-table__row td:last-child {
		padding-right: 24px;
	}

	.dataviews-wrapper .dataviews-view-table__row .dataviews-view-table__actions-column {
		text-align: right;
		position: sticky;
		right: 0;
		background-color: var( --studio-white );

		::after {
			background-color: var( --color-border-subtle );
			bottom: 0;
			content: "";
			display: block;
			left: 0;
			position: absolute;
			top: 0;
			width: 1px;
		}

		@include breakpoint-deprecated( ">480px" ) {
			position: relative;
			background-color: transparent;

			::after {
				display: none;
			}
		}
	}

	.dataviews-view-table__row {
		background: var( --studio-white );
		opacity: 1;

		&:hover {
			background-color: var( --color-neutral-0 );
		}
	}

	.dataviews-view-table,
	.dataviews-view-table tr {
		max-width: 1040px;
		padding: 16px 0;
	}

	.dataviews-view-table tr.dataviews-view-table__row th,
	.dataviews-view-table tr.dataviews-view-table__row td {
		white-space: break-spaces;
	}

	.dataviews-view-table tr td:first-child,
	.dataviews-view-table tr th:first-child {
		padding-left: 24px;
	}

	.dataviews-view-table__cell-content-wrapper.dataviews-column-primary__media {
		display: none;

		@include breakpoint-deprecated( ">480px" ) {
			display: flex;
		}
	}

	.purchase-item__information {
		margin-right: 16px;
		max-width: 250px;
		flex: auto;

		@include breakpoint-deprecated( ">480px" ) {
			max-width: 400px;
		}

		.purchase-item__title {
			font-size: 16px;
			overflow: hidden;
		}

		.purchase-item__purchase-type,
		.purchase-item__purchase-type a.purchase-item__link {
			overflow: hidden;
		}
	}

	.dataviews-view-table__cell-content-wrapper .purchase-item__status {
		margin-right: 16px;
		max-width: 250px;
	}

	.dataviews-view-table__cell-content-wrapper .purchase-item__payment-method {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		min-width: 175px;

		.purchase-item__payment-method-card {
			margin-right: 8px;
			width: 40px;
		}

		.purchase-item__paypal {
			width: 65px;
		}
	}
}
