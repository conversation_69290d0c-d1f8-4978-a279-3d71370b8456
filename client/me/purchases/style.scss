/* Layout
================================================== */
.layout__primary .main {
	& div.section-nav {
		@media ( max-width: 480px ) {
			&.is-open {
				& > .section-nav__panel {
					padding: 0;

					& > .section-nav-group {
						margin-top: 0;
					}
				}
			}
		}
	}
}

.purchases-layout__wrapper {
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex-wrap: wrap;

	@include breakpoint-deprecated( ">960px" ) {
		flex-wrap: nowrap;
	}
}

.purchases-layout__site {
	flex: 0 0 52px;
}

.purchases-layout__information {
	margin-right: 16px;
	flex: 1 1 250px;

	@include breakpoint-deprecated( ">960px" ) {
		flex: 1 1 462px;
	}
}

.purchases-layout__status {
	margin: 16px 16px 0 0;
	flex: 1 1 200px;

	@include breakpoint-deprecated( ">480px" ) {
		margin: 16px 16px 0 52px;
	}

	@include breakpoint-deprecated( ">960px" ) {
		flex: 0 1 300px;
		margin: 0 16px 0 0;
	}

	.subscriptions__list & {
		margin: 16px 16px 0 0;

		@include breakpoint-deprecated( ">960px" ) {
			margin: 0 16px 0 0;
		}
	}
}

.purchases-layout__payment-method {
	display: none;

	@include breakpoint-deprecated( ">960px" ) {
		display: flex;
		align-items: center;
		flex: 0 0 200px;
		flex-wrap: wrap;
	}
}

/* Purchase listing
================================================== */
.purchases-list-header.card {
	padding-right: 48px;
	font-size: 0.875rem;

	.purchases-layout__status {
		display: none;

		@include breakpoint-deprecated( ">960px" ) {
			display: block;
			margin-top: 0;
		}
	}
}

.purchase-item--disconnected {
	&.card.is-compact {
		padding-right: 48px;
	}

	&:hover {
		background: var(--color-surface);
	}
}

.purchase-item__placeholder {
	display: block;
	height: 40px;

	@include placeholder;
}

.purchase-item__site,
.membership-item {
	.site-icon {
		border-radius: 2px;
	}

	.site-icon,
	.site-icon__img {
		border-radius: 2px;
		width: 36px;
		height: 36px;
	}
}

.purchase-item,
.membership-item {
	&:hover {
		background: var(--color-neutral-0);
	}

	&:focus {
		z-index: 1;
	}
}

.purchase-item__disconnected-icon {
	display: flex;
	width: 36px;
	height: 36px;
	border-radius: 2px;
	background: var(--color-error-50);
	align-items: center;
	justify-content: center;

	.gridicon {
		fill: var(--color-surface);
	}
}

.purchase-item__static-icon {
	width: 36px;
	height: 36px;
	border-radius: 2px;
	overflow: hidden;
}

.purchase-item__title {
	color: var(--color-neutral-80);
	overflow: hidden;
	.owner-info__pop-over {
		vertical-align: middle;
	}
}

.purchase-item__purchase-type,
.purchase-item__site-title {
	font-size: $font-body-small;
	color: var(--color-neutral-50);
	line-height: 1.4;
}

.purchase-item__link,
a.purchase-item__link {
	&.purchase-item__link--error {
		color: var(--color-error-50);
	}

	&:hover {
		cursor: pointer;
	}

	&:focus {
		outline: thin dotted;
	}
}

.purchase-item__status {
	font-size: $font-body-small;
	color: var(--color-neutral-80);
	line-height: 1.3;

	.purchase-item__is-error,
	.purchase-item__is-warning {
		color: var(--color-error-50);
		position: relative;
		display: inline-block;

		@include breakpoint-deprecated( ">960px" ) {
			&::after {
				content: "";
				display: block;
				width: 8px;
				height: 8px;
				position: absolute;
				top: calc(50% - 4px);
				left: -16px;
				background: var(--color-error-50);
				border-radius: 100%;
			}
		}
	}

	.purchase-item__is-warning {
		color: var(--color-warning-50);

		@include breakpoint-deprecated( ">960px" ) {
			&::after {
				background: var(--color-warning-50);
			}
		}
	}
}

.purchase-item__date,
.purchase-item__site-name {
	display: inline-block;
}

.purchase-item__payment-method {
	font-size: $font-body-small;
	color: var(--color-neutral-80);
}

.purchase-item__payment-method-card {
	width: 40px;
	margin-right: 8px;
}

.purchase-item__paypal {
	width: 65px;
}

.purchase-item__backup-payment-method-notice {
	margin: 8px;
	display: flex;
}

.purchase-item__backup-payment-method-notice button {
	display: flex;
}

.purchase-item__no-payment-method {
	display: flex;
	align-items: center;
	gap: 4px;

	svg {
		flex-shrink: 0;

		path {
			fill: var(--color-warning-30);
		}
	}
}
