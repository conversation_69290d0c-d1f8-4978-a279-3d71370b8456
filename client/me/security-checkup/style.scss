.security-checkup__title-card {
	h4 {
		font-size: $font-title-small;
	}
}

.security-checkup__nav-item {
	&.card.is-compact {
		padding-right: 24px;
	}

	span {
		display: flex;
		align-items: center;
		> .security-checkup__nav-item-icon {
			fill: var(--color-neutral-90);
			height: 32px;
			margin-right: 16px;
			min-width: 32px;

			&.material-icon-check_circle {
				fill: var(--color-success);
			}
			&.material-icon-pending,
			&.material-icon-error, {
				fill: var(--color-warning-30);
			}
		}
		div {
			color: var(--color-neutral-90);
			small {
				color: var(--color-neutral-50);
				font-size: $font-body-small;
			}
		}
	}
}

.security-checkup {
	.security-checkup__info {
		margin-top: 16px;
	}
	.vertical-nav {
		margin-top: 0;
	}
}
