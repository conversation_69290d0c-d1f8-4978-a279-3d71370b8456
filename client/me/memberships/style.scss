.memberships__header {
	padding: 11px 24px;
	display: flex;

	.memberships__icon {
		background: var(--color-neutral-10);
		display: inline-block;
		height: 32px;
		width: 32px;
		line-height: 32px;
		font-size: 2rem;
		margin-right: 12px;
		text-align: center;
		vertical-align: middle;
		position: relative;
		overflow: hidden;
		align-self: flex-start;
		flex: 0 0 auto;

		.gridicon {
			color: var(--color-surface);
		}
	}

	.memberships__info {
		width: 0;
		flex: 1 0 auto;
	}

	.memberships__title,
	.memberships__domain {
		overflow: hidden;
		white-space: nowrap;
	}

	.memberships__title {
		color: var(--color-text);
		display: block;
		font-size: 0.875rem;
		font-weight: 400;
		line-height: 1.3;
	}

	.memberships__domain {
		color: var(--color-text-subtle);
		display: block;
		max-width: 95%;
		font-size: 0.75rem;
		line-height: 1.4;
		margin-top: 2px;
	}
}
