.memberships__subscription-meta {
	padding: 0;
	font-size: $font-body-small;
}

.memberships__subscription-inner-meta {
	display: flex;
	margin: 0;
	list-style: none;
	overflow: auto;
	padding: 0 24px;
	border-top: 1px solid var(--color-neutral-0);

	@include breakpoint-deprecated( "<660px" ) {
		flex-direction: column;
		padding: 0 16px 16px;
	}

	li {
		color: var(--color-neutral-60);
		font-size: $font-body-small;

		@include breakpoint-deprecated( "<660px" ) {
			clear: both;
			overflow: auto;
			margin-top: 15px;
		}

		@include breakpoint-deprecated( ">660px" ) {
			box-sizing: border-box;
			flex: 1 1 auto;
			font-size: $font-body-small;
			text-align: center;
			padding: 15px 8px 12px;

			&:first-child {
				padding-left: 0;
			}
			&:last-child {
				padding-right: 0;
			}
		}

		+ li {
			@include breakpoint-deprecated( ">660px" ) {
				border-left: 1px solid var(--color-neutral-0);
			}
		}

		> a {
			color: var(--color-neutral-60);
			display: block;
			text-decoration: none;
		}
	}
}

.memberships__subscription-inner-detail-label {
	font-style: normal;
	font-weight: 600;

	@include breakpoint-deprecated( "<660px" ) {
		flex-direction: row;
		float: left;
	}

	@include breakpoint-deprecated( ">660px" ) {
		color: var(--color-text-subtle);
		display: block;
		font-family: $sans;
		font-size: $font-body-extra-small;
		font-weight: 400;
		margin: 0 0 12px;
	}
}

.memberships__subscription-inner-detail {
	@include breakpoint-deprecated( "<660px" ) {
		display: block;
		float: right;
		text-align: right;
	}

	.payment-logo {
		margin-right: 5px;
	}

	a {
		display: block;
		cursor: pointer;
	}
}

.memberships__subscription-header {
	padding: 16px 24px;
}

.memberships__subscription-title {
	color: var(--color-text);
	display: block;
	font-size: 1.25rem;
	font-weight: 400;
	clear: none;
}

.memberships__subscription-price {
	margin: 0;
	font-size: 1.5rem;
	line-height: 1.5;
	color: var(--color-neutral-70);
}
