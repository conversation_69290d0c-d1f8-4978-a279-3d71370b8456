.security-2fa-sms-settings .calypso-notice {
	margin: 5px 0 0;
}

.security-2fa-sms-settings__fieldset-container .form-phone-input {
	display: flex;
	flex-direction: column;

	@include breakpoint-deprecated( ">480px" ) {
		flex-direction: row;
	}
}

.security-2fa-sms-settings__fieldset-container .form-phone-input__country {
	flex: 1;
	padding-right: 0;

	@include breakpoint-deprecated( ">480px" ) {
		padding-right: 20px;
		margin-bottom: 5px;
	}
}

.security-2fa-sms-settings__fieldset-container .form-country-select {
	width: 100%;
	overflow: ellipsis;
}

.security-2fa-sms-settings__fieldset-container .form-phone-input__phone-number {
	flex: 2;
	margin-bottom: 5px;
}

.security-2fa-sms-settings__buttons {
	margin-top: 10px;
	display: flex;
	justify-content: flex-end;

	.button {
		margin-right: 0;
	}

	.security-2fa-sms-settings__cancel-button {
		margin-right: 10px;
	}
}
