@import "@wordpress/base-styles/colors";

/* @wordpress/components FormToggle overrides ----------------------------------------- */
.components-form-toggle {
	input[type="checkbox"] {
		cursor: pointer;

	}
}

/* @wordpress/components Button overrides --------------------------------------------- */
.components-button {
	justify-content: center;
}

/* Default */
.components-button.is-primary:not(.is-destructive) {
	// Background color, focus ring color
	--wp-components-color-accent: var(--color-accent);
	// Text color
	--wp-components-color-accent-inverted: var(--color-text-inverted);
	// Hover background color
	--wp-components-color-accent-darker-10: var(--color-accent-60);
	// Active and busy states background color
	--wp-components-color-accent-darker-20: var(--color-accent-60);
}

.components-button.is-secondary:not(.is-destructive) {
	// Text color, border color, focus ring color, hover background color tint
	--wp-components-color-accent: var(--color-accent);
	// Hover text color, border color
	--wp-components-color-accent-darker-20: var(--color-accent-60);
}

.components-button.is-tertiary:not(.is-destructive) {
	// Text color, focus ring color, active background color tint
	--wp-components-color-accent: var(--color-accent);
	// Hover text color
	--wp-components-color-accent-darker-20: var(--color-accent-60);
}

.components-button.is-link:not(.is-destructive) {
	// Text color, focus ring color
	--wp-components-color-accent: var(--color-accent);
}

/*
 * Jetpack
 * --------------------------------------------------------------------------------
 * Note: The increased specificity here overrides the is-link button styles
 * added by Stepper and other components. It is not always the desired behavior.
 * In login, we want to use the Jetpack green color for buttons, but not for links.
 * We therefore apply :not(.is-link) to avoid overriding the is-link styles.
 * --------------------------------------------------------------------------------
 */
.is-jetpack-login:not(.is-wccom-oauth-flow):not(.is-woocommerce-core-profiler-flow) {
	.components-button:not(.is-link):not(.is-destructive) {
		--wp-components-color-accent: var(--studio-jetpack-green-50);
		--wp-components-color-accent-darker-10: var(--studio-jetpack-green-60);
		--wp-components-color-accent-darker-20: var(--studio-jetpack-green-60);
	}
}

.jetpack-cloud {
	.components-button:not(.is-destructive) {
		--wp-components-color-accent: var(--studio-jetpack-green-50);
		--wp-components-color-accent-darker-10: var(--studio-jetpack-green-60);
		--wp-components-color-accent-darker-20: var(--studio-jetpack-green-60);
	}
}

/* Gravatar & WP Job Manager */
.is-grav-powered-client {
	.components-button:not(.is-destructive) {
		--color-gravatar-darker: color-mix(in srgb, var(--color-gravatar), #000 13%);

		--wp-components-color-accent: var(--color-gravatar);
		--wp-components-color-accent-darker-10: var(--color-gravatar-darker);
		--wp-components-color-accent-darker-20: var(--color-gravatar-darker);
	}

	&.is-wp-job-manager {
		.components-button:not(.is-destructive) {
			--color-wp-job-manager-darker: color-mix(in srgb, var(--color-wp-job-manager), #000 10%);

			--wp-components-color-accent: var(--color-wp-job-manager);
			--wp-components-color-accent-darker-10: var(--color-wp-job-manager-darker);
			--wp-components-color-accent-darker-20: var(--color-wp-job-manager-darker);
		}
	}
}

/* Akismet */
.is-akismet .login,
.is-akismet .magic-login,
.is-akismet .signup-form,
.is-akismet .auth-form__social {
	.components-button:not(.is-destructive) {
		--wp-components-color-accent: var(--color-akismet);
		--wp-components-color-accent-darker-10: var(--color-akismet);
		--wp-components-color-accent-darker-20: var(--color-akismet);
	}
}

/* Crowdsignal */
.signup.is-crowdsignal {
	.components-button:not(.is-destructive) {
		&.is-primary {
			--wp-components-color-accent: var(--color-primary);
			--wp-components-color-accent-darker-10: var(--color-primary-dark);
			--wp-components-color-accent-darker-20: var(--color-primary-dark);
		}
	}
}

/* Woo */
.woo.is-woo-passwordless {
	.components-button:not(.is-link):not(.is-destructive) {
		--wp-components-color-accent: var(--woo-purple-40);
		--wp-components-color-accent-darker-10: var(--woo-purple-60);
		--wp-components-color-accent-darker-20: var(--woo-purple-60);

		&:focus:not(:disabled, [aria-disabled="true"]) {
			--wp-components-color-accent: var(--woo-purple-60);
		}

		&.is-primary {
			&:disabled,
			&[aria-disabled="true"] {
				--wp-components-color-accent: var(--studio-gray-5);

				color: var(--studio-gray-50);

				.components-spinner {
					--wp-components-color-accent: var(--woo-purple-40);
				}
			}
		}
	}
}

/* Blaze Pro */
.blaze-pro,
.is-blaze-pro {
	.components-button:not(.is-destructive):not(.is-link) {
		--color-blaze-pro-darker: color-mix(in srgb, var(--color-blaze-pro), #000 13%);

		--wp-components-color-accent: var(--color-blaze-pro);
		--wp-components-color-accent-inverted: var(--studio-black);
		--wp-components-color-accent-darker-10: var(--color-blaze-pro-darker);
		--wp-components-color-accent-darker-20: var(--color-blaze-pro-darker);

		font-weight: 600;
	}

	.components-button.is-tertiary {
		--wp-components-color-accent: var(--studio-gray-60);
		--wp-components-color-accent-darker-20: var(--studio-gray-80);
	}
}

/* @wordpress/components ButtonGroup overrides ---------------------------------------- */
.components-button-group {
	.components-button {
		&,
		&.is-primary {
			box-shadow: inset 0 0 0 1px var(--color-accent-60);
		}
	}
}

/* @wordpress/components RadioControl overrides --------------------------------------- */
.components-radio-control {
	&__input[type="radio"] {
		padding: 6px;
		cursor: pointer;

		&:checked {
			border-color: var(--color-accent);
			background-color: var(--color-accent);
		}
	}
}
