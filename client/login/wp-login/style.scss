@import "../../jetpack-connect/colors";
@import "../../assets/stylesheets/shared/mixins/elevation";
@import "@automattic/onboarding/styles/mixins";
@import "@automattic/typography/styles/fonts";
@import "@wordpress/base-styles/breakpoints";
@import "@wordpress/base-styles/colors";
@import "@wordpress/base-styles/mixins";
@import "@wordpress/base-styles/variables";

$image-height: 47px;

.layout.is-section-login {
	position: relative;
	min-height: 100%; // Needed for any screen not yet using a StepContainerV2 wireframe (e.g. magic login).

	.layout__content {
		position: static;
	}

	&.has-no-masterbar {
		.layout__content {
			// Adjust the padding as we no longer
			// show the masterbar.
			padding-top: 48px;
		}

		// Hide the masterbar for real
		.masterbar {
			display: none;
		}
	}

	/*
	* Start: Jetpack font styles for login page
	*
	* If we're on a Woo page, we don't want to apply the Jetpack font styles, as Woo brings in its own font styles.
	*/
	&.is-jetpack-login:not(.is-woo-passwordless) {
		.wp-login__one-login-layout-heading-text {
			font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif
		}
	}
	/* End: Jetpack font styles for login page */

	/* Start: Jetpack Cloud font styles for login page */
	&.jetpack-cloud {
		.wp-login__one-login-layout-heading-text {
			font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif
		}
	}
	/* End: Jetpack Cloud font styles for login page */
}

.wp-login__main {
	width: 100%;
}

.wp-login__site-return-link {
	overflow: hidden;
	position: relative;
	white-space: nowrap;

	&::after {
		@include long-content-fade( $color: var( --color-neutral-0 ) );
	}
}

.layout.is-jetpack-login:not(.is-wccom-oauth-flow):not(.is-woocommerce-core-profiler-flow) {
	@include jetpack-connect-colors();
}

.layout.is-wccom-oauth-flow,
.layout.is-woocommerce-core-profiler-flow {
	@include woocommerce-colors();
}

.layout.is-jetpack-login:not(.is-woocommerce-core-profiler-flow) {

	.login__form input,
	.logged-out-form input {
		&:not(.form-text-input-core-styles) {
			&:focus {
				border-color: var(--color-accent);
				box-shadow: 0 0 0 2px var(--color-accent-light);
			}
		}
	}

	.translator-invite__content a {
		border: none;
	}

	.translator-invite__content {
		color: var(--color-neutral-50);
	}

	.wp-login__site-return-link::after {
		background: linear-gradient(to right, color-mix(in srgb, var(--color-surface-backdrop) 0%, transparent), var(--color-surface-backdrop) 90%);
	}
}

.layout.is-wccom-oauth-flow {
	background-color: var(--color-woocommerce-onboarding-background);

	.wp-login__main.main {
		max-width: 476px;
	}

	.login__form {
		@include elevation(2dp);
		padding-bottom: 0;
		padding-top: 8px;
	}

	.login__form input:focus,
	.logged-out-form input:focus {
		border: 0;
		box-shadow: none;
	}

	.login__form-change-username {
		color: var(--color-neutral-60);
	}

	.auth-form__social {
		box-shadow: none;
		padding-top: 0;
		margin-right: auto;
		margin-left: auto;
		width: 300px;
	}

	.auth-form__social-buttons {
		.social-buttons__button {
			border: 1px solid var(--studio-wordpress-blue);
			color: var(--studio-wordpress-blue);
			box-shadow: none;
		}
	}

	.wp-login__site-return-link::after {
		background: none;
	}

	.login__form-footer {
		text-align: center;

		.button {
			max-width: 310px;
			height: 48px;

			&.is-primary {
				border: 0;
			}
		}
	}
}

.wp-login__header-navigation {
	display: none;
}

.layout:not(.is-grav-powered-client) {
	background-color: var( --color-main-background, #fcfcfc );

	// Specificity bump.
	.layout__content.layout__content {
		padding: 0;
	}

	.wp-login__container .card {
		box-shadow: none;
		background-color: #fdfdfd;
	}

	.login .button.is-primary {
		// Note: Matches primary button to `button.signup-form__submit`
		background-color: var(--studio-wordpress-blue, #3858e9);
		color: var(--color-text-inverted);
		box-shadow: none;
		border: 0 var(--studio-wordpress-blue-60);
		font-weight: 500;
		letter-spacing: 0.32px;
		line-height: 17px;
		min-width: 100%;
		text-align: center;
		margin: 13px auto;
		float: none;

		&:hover,
		&:focus {
			background-color: var(--studio-wordpress-blue-60);
			border-color: var(--studio-wordpress-blue-60);
			color: var(--color-text-inverted);
		}

		&[disabled],
		&:disabled,
		&.disabled {
			background-color: var(--studio-gray-20);
			color: var(--color-surface);
		}

		@include break-mobile {
			min-width: 170px;
		}

		&.is-busy {
			background-image: linear-gradient(-45deg, var(--studio-gray-10) 28%, var(--studio-gray-20) 28%, var(--studio-gray-20) 72%, var(--studio-gray-10) 72%);
		}
	}

	.login__form-action .components-button.is-primary {
		margin: 6px auto 13px;
	}

	.login form {

		// We shouldn't regularly use .components-* classnames. The following is
		// an exception to opt-out from some style overrides. Ideally, all
		// input instances would come from `@wordpress/components` and the
		// following overrides could be safely deleted.
		input:not(.components-text-control__input, .form-text-input-core-styles),
		button:not(.components-button) {
			/* Note: Same as `button.signup-form__submit, .signup-form__input.form-text-input` */
			height: 40px; // Matches .components-button height __next40pxDefaultSize
			border: 1px solid var(--studio-gray-10);
			font-size: $font-body;

			&[disabled],
			&:disabled,
			&.disabled {
				background-color: #fdfdfd;
			}
		}

		.login__form-userdata input {
			margin: 0 0 20px;
		}

		.login__form-userdata input:last-of-type {
			/* Note: reduces space between terms and last input box */
			margin: 0 0 10px;
		}

		.login__form-userdata {
			button:not(.login__form-change-username, .form-password-input__toggle) {
				min-width: 194px;
				height: unset;
				border: 0;
				text-align: start;
			}
		}
	}

	.wp-login__main {
		max-width: 540px;
	}

	.wp-login__main.is-social-first {
		max-width: none;

		.wp-login__header-navigation {
			align-items: center;
			display: flex;
			height: 60px;
			inset-inline-start: 72px;
			inset-inline-end: 24px;
			position: absolute;
			padding: 0;
			right: 16px;
			top: 9px;
			margin: 0;
			left: 11px;

			@include break-small {
				border: none;
				inset-inline-start: 72px;
				inset-inline-end: 24px;
			}

			a {
				color: var(--studio-gray-90, #1d2327);
				font-size: $font-body-small;
				font-style: normal;
				font-weight: 500;
				line-height: 20px;
				margin-left: auto;
				text-decoration: underline;
				text-underline-offset: 5px;
			}
		}
	}

	&:not(.is-mobile) .layout__primary {
		margin-top: 0;

		/* START - Note: Patches below needed to continue using the current Divider.
		We can remove when we refactor the Divider itself (or rethink current usage), and leave the default card styles intact.
		Ideally, the Divider used in Login should be consolidated with the inline Separator's styles used in Signup (see `.auth-form__separator`) */

		form {
			.card.login__form {
				padding-left: 0;
				padding-right: 0;
			}
		}

		/* END - Note */
	}

	.two-factor-authentication__verification-code-form > p,
	.login__social-tos {
		color: var(--studio-gray-50);
	}

	.two-factor-authentication__verification-code-form > p {
		text-align: left;
	}

	.login__social-tos {
		margin-top: 24px;
		text-align: left;

		a {
			text-decoration: underline;
		}
	}

	.two-factor-authentication__verification-code-form.card {
		padding-top: 0;
		padding-bottom: 0;

		button.button {
			margin: 0 0 4px;
		}

		button.button.is-primary {
			font-size: $font-body-small;
		}
	}
}

/* stylelint-disable scales/font-weights, scales/radii */
.layout.is-section-login.is-grav-powered-client {
	padding-bottom: $image-height;
	min-height: calc(100% - #{$image-height});
	background-color: #fff;

	&.is-wp-job-manager {
		background-color: #efefef;

		.login a,
		.login__form-change-username {
			color: var(--color-wp-job-manager);
		}

		.login {
			border-radius: 2px;
		}

		.form-button.is-primary {
			&,
			&:disabled {
				background-color: var(--color-wp-job-manager);
			}

			&:hover:not(&:disabled),
			&:focus {
				background-color: darken(#2404eb, 10%);
			}
		}
	}

	.wp-login__main .locale-suggestions .locale-suggestions__notice.is-light {
		&,
		.calypso-notice__icon-wrapper {
			background-color: var(--studio-gray-0);
		}
	}

	.layout__content {
		padding: 0;
	}

	.wp-login__main {
		max-width: 480px;
	}

	.login,
	.form-text-input {
		font-family: "SF Pro Text", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	}

	.login {
		background-color: #fff;
		border-radius: 8px;
		padding: 20px;
		color: #101517;
		font-size: 14px;
		line-height: 1.5;

		a:not(.calypso-notice.is-error a) {
			color: var(--color-gravatar);
		}

		button {
			font-family: inherit;
		}
	}

	.grav-powered-login__form-header-wrapper {
		text-align: left;
	}

	.grav-powered-login__form-header {
		font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
		font-size: 32px;
		font-weight: 900;
		line-height: 1.2;
		text-align: left;
		margin: 24px 0;
	}

	.grav-powered-login__header-subtitle,
	.verification-code-form__help-text,
	.two-factor-authentication__info {
		margin-bottom: 24px;
	}

	.verification-code-form__help-text {
		color: inherit;
	}

	.card {
		background: transparent;

		&.two-factor-authentication__push-notification-approval,
		&.two-factor-authentication__actions {
			box-shadow: none;
		}
	}

	form {
		max-width: 100%;

		> .card {
			padding: 0;
			border: 0;
			box-shadow: none;
		}
	}

	.form-fieldset {
		margin-bottom: 0;
	}

	label.form-label {
		color: inherit;
		margin-bottom: 10px;
	}

	.two-factor-authentication__verification-code-form .form-label {
		display: none;
	}

	.login__form-change-username {
		color: var(--color-gravatar);
	}

	.login__form-password {
		margin-top: 15px;
	}

	.form-text-input,
	.login__form-userdata input:last-of-type.form-text-input {
		padding: 14px 16px;
		font-size: 16px;
		margin: 0;
		border-radius: 2px;
		height: auto;
		line-height: 1.5;

		&[type="password"] {
			padding-right: 44px;
		}
	}

	.form-password-input__toggle {
		top: 15px;
		right: 16px;
	}

	.form-input-validation {
		margin-bottom: 0;
		padding-top: 15px;
		padding-bottom: 0;
	}

	.login__form-terms {
		color: rgba(#00101c, 0.6);
		margin: 24px 0 0;
		font-size: 0.875rem;
		text-align: left;
	}

	button.form-button.is-primary,
	.two-factor-authentication__actions .button {
		min-height: 50px;
		margin: 0;
		padding: 10px 16px;
		font-size: 16px;
		font-weight: 600;

		&:disabled {
			opacity: 0.3;
		}
	}

	button.form-button.is-primary {
		margin-top: 16px;
		margin-bottom: 0;
		border: 0;

		&,
		&:disabled {
			background-color: var(--color-gravatar);
		}

		&:hover:not(&:disabled),
		&:focus {
			background-color: darken(#1d4fc4, 10%);
		}
	}

	.components-button.is-primary {
		min-height: 50px;
		margin-top: 16px;
		font-size: 16px;
		font-weight: 600;
	}

	.two-factor-authentication__actions {
		display: flex;
		flex-direction: column;
		margin-top: 24px;
		padding: 0;
		gap: 16px;
		background: transparent;

		&::after {
			display: none;
		}

		.button {
			color: var(--color-gravatar);
			border: 1px solid #1d4fc44d;
			box-shadow: none;

			&:hover:not(&:disabled),
			&:focus {
				color: darken(#1d4fc4, 13%);
				border-color: darken(#1d4fc44d, 13%);
			}
		}
	}

	div.two-factor-authentication__small-print {
		font-size: inherit;
		margin-top: 24px;
		padding: 0;
	}

	.auth-form__separator {
		margin-top: 24px;
	}

	.auth-form__separator-text {
		font-size: 15px;
		text-transform: lowercase;
		color: inherit;
	}

	.auth-form__social {
		background: transparent;
		margin: 24px 0 0;
		padding: 0;
		width: fit-content;
		clip-path: unset;
	}

	.social-buttons__button {
		box-shadow: none;
		padding: unset;
		height: auto;

		> svg {
			box-shadow: inset 0 0 0 $border-width $gray-300;
			border-radius: 50%;
			margin: 0;
			width: 20px;
			height: 20px;
			margin-right: 12px;
			padding: 12px;
		}

		> span {
			margin-left: unset;
		}

		&:hover {
			background: unset;
			box-shadow: none;

			> svg {
				/* Matches hover state of .components-button */
				background: color-mix(in srgb, var(--wp-components-color-accent) 4%, transparent);
			}
		}

		&:focus {
			box-shadow: none;

			> svg {
				/* Matches focus state of .components-button */
				box-shadow: 0 0 0 currentColor inset, 0 0 0 var(--wp-admin-border-width-focus) var(--wp-components-color-accent);
			}
		}
	}

	.grav-powered-login__divider {
		background-color: #e7e9ea;
		width: 80px;
		margin: 24px 0;
	}

	.grav-powered-login__footer {
		display: flex;
		flex-direction: column;
		font-size: 16px;
		line-height: 1;
		gap: 24px;

		a,
		button {
			font-size: inherit;
			width: fit-content;
		}

		button:focus {
			outline: dotted;
		}
	}

	@include break-small {
		background-color: #efefef;

		.wp-login__main:not(:has(.locale-suggestions)) .login,
		.grav-powered-magic-login:not(:has(.locale-suggestions)) .grav-powered-magic-login__content {
			margin-top: 50px;
		}

		.login {
			padding: 40px 56px;
		}

		.grav-powered-login__form-header {
			font-size: 40px;
		}

		.grav-powered-login__footer {
			gap: 16px;
		}
	}
}
/* stylelint-enable scales/font-weights, scales/radii */

.wp-login__p2-logo {
	position: absolute;
	top: 24px;
	left: 24px;
}

:root {
	--login-form-column-max-width: 327px;
}
