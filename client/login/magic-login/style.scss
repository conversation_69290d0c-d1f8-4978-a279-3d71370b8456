@import "@automattic/onboarding/styles/mixins";
@import "@wordpress/base-styles/breakpoints";
@import "@wordpress/base-styles/mixins";
@import "@automattic/calypso-color-schemes";
@import "@automattic/typography/styles/variables";

@media (max-height: 450px) {
	.magic-login__handle-link,
	.magic-login__check-email,
	.magic-login__link-expired {
		img {
			margin-top: -45px;
			max-height: 180px;
		}
		.empty-content__title {
			font-size: 90%;
		}
		.empty-content__line {
			margin-bottom: 2.8rem;
		}
	}
}

.layout.is-jetpack-login:not(.woo) {
	&, .empty-content__title {
		font-family: "SF Pro Text", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	}

	.empty-content__title {
		color: var(--studio-black);
		font-size: 2rem;
		font-weight: 500;
		line-height: 40px;
		margin-bottom: 1rem;
	}

	.magic-login {

		.magic-login__form-form {
			box-shadow: none;
		}

		.magic-login__form-header {
			font-family: "SF Pro Text", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
		}

		.magic-login__successfully-jetpack {
			padding: 0 24px 0 24px;
		}

		&__loading-spinner--jetpack {
			color: var(--studio-jetpack-green-40, #069e08 );
			height: 48px;
			left: 50%;
			margin-top: 40px;
			transform: translateX(-50%);
			width: 48px;
		}
	}

	.magic-login__link-expired {
		padding-inline: 1rem;
		padding-top: 0;
		margin: 0 auto;
	}

	.empty-content__action.is-primary {
		background-color: var(--studio-jetpack-green-60);
		border-color: var(--studio-jetpack-green-60);
	}
}
.magic-login__successfully-jetpack-actions {
	color: var(--studio-jetpack-grey-50, #646970 );
	font-size: 14px;
	font-weight: 400;
	line-height: 20px;
	word-wrap: break-word;

	text-align: center;

	margin-top: 45px;
	margin-bottom: 45px;

	.magic-login__resend-button {
		font-size: 100%;
		color: var(--studio-jetpack-green-60, #007117 );

		&:hover {
			color: var(--studio-jetpack-green-60, #007117 );
		}
	}

	.magic-login__log-in-link {
		color: var(--studio-jetpack-green-60, #007117 );
	}

	> p {
		margin-bottom: 4px;
	}
}

.layout.is-wpcom-magic-login {
	.magic-login__form-header-icon {
		text-align: center;
		img {
			border-radius: 4px;
		}
	}
	.magic-login .magic-login__form-header {
		margin-bottom: 0;
		/* stylelint-disable-next-line declaration-property-unit-allowed-list */
		line-height: 120%;
		@include break-mobile {
			font-size: $font-title-large;
		}
	}

	.logged-out-form {
		.form-label {
			font-weight: 400;
		}
	}

	.magic-login__form-action {
		margin-top: 16px;
	}
}

.magic-login__link-expired {
	max-width: 450px;
	text-align: start;

	.empty-content__line {
		font-size: $font-body;
		margin-bottom: 2.8rem;
		font-weight: 400;
		color: var(--studio-gray-100)
	}
	.empty-content__action {
		width: 100%;
		margin: 0 0 1rem;
		border-radius: 4px;
		font-size: 15px;
		font-weight: 500;
		line-height: 20px;
		padding: 0.75rem 1.5rem;
	}
}

.layout:not(.is-grav-powered-client) .magic-login__handle-link,
.layout:not(.is-grav-powered-client) .magic-login__link-expired {
	.empty-content__title {
		font-family: $brand-serif;
		font-size: rem(42px);
		line-height: 1.2;
		margin-bottom: 15px;
	}

	.button.is-primary:not([disabled]) {
		background-color: #007cba;
		border-color: #007cba;
	}
}

.magic-login__handle-link.jetpack {
	.jetpack-logo {
		margin: 35px 0;
	}
}

.magic-login__request-link {
	max-width: 400px;
}

.magic-login__form {
	margin-bottom: 0;
	padding-top: 4px;

	.magic-login__form-text {
		text-align: center;
		strong {
			font-weight: 700;
			display: flex;
			flex-direction: column;
		}
	}

	p {
		margin-bottom: 0;
	}

	.logged-out-form {
		border-radius: 0;
		box-shadow: 0 0 0 1px var(--color-border-subtle);
		margin: 0 auto;
		max-width: 400px;

		p {
			margin-bottom: 20px;
		}
	}

	.magic-login__form-header-notice {
		margin-top: 16px;
	}
}

.magic-login__form-action {
	margin-top: 20px;

	button {
		width: 100%;
	}

	.form-button:last-child {
		margin-right: 0;
	}
}

.magic-login__footer {
	margin: 4px 0 48px 0;
	text-align: center;

	a {
		border-bottom: 1px solid #c8d7e1;
		color: var(--studio-gray-90);
		display: block;
		font-size: $font-body-small;
		font-weight: 600;
		text-align: center;
		text-decoration: underline;

		&:hover {
			color: var(--color-primary);
		}
		&:last-of-type {
			border-bottom: none;
		}

		body.is-section-signup & {
			color: var(--color-text-inverted);
		}
	}
	.gridicon {
		vertical-align: text-bottom;
	}

	p {
		font-size: $font-body-small;
		// Matches magic link email
		a {
			display: inline;
			padding: 0;
			line-height: 20px;
			padding-left: 4px;
		}
	}
}

.magic-login__check-email-image {
	display: block;
	height: 132px;
	margin: 0 auto 1.5em;
}

.magic-login__check-email-image.jetpack {
	height: 240px;
}

.magic-login:not(.is-grav-powered-client) {
	.magic-login__form-header {
		@include onboarding-heading-text-mobile;

		@include break-mobile {
			@include onboarding-heading-text;
		}
	}

	.logged-out-form {
		.form-label {
			color: var(--studio-gray-60);
		}
	}

	.card {
		background: transparent;
		box-shadow: none;
	}

	.magic-login__form-action .button.is-primary:not([disabled]) {
		background-color: #3858e9;
		border-color: #3858e9;
		color: var(--studio-white);
	}
}
.is-section-login .app-promo.magic-link-app-promo {
	margin-top: 3em;
	box-shadow: 0 0 0 1px var(--color-border-subtle) inset;
	border-radius: 16px; /* stylelint-disable-line scales/radii */
	font-weight: 400;
	letter-spacing: -0.4px;

	.app-promo__title {
		margin: 16px 0;
	}

	.app-promo__icon {
		width: auto;
	}
	@include break-mobile {
		.app-promo__icon {
			height: 40px;
			width: auto;
		}
	}

	.card-heading {
		font-family: $brand-serif;
	}

	.app-promo__qr-code {
		display: flex;
		margin: 0;

		.app-promo__qr-code-canvas {
			flex: 0 0 100px;
			order: 1;
		}

		.get-apps__card-text {
			padding: 0 20px 0 0;
		}
	}
}

.magic-login__tos {
	font-size: $font-body-extra-small;
	margin: 20px 0 20px;
	text-align: left;

	a {
		@include break-mobile {
			white-space: pre;
		}
	}
}

.magic-login__a4a-logo {
	display: block;
	margin: 0 auto;
}

.card.magic-login__verify-code-form {
	--magic-login-error-color: #cc1818;
	padding: 0;
	box-shadow: none;
	border: none;
}

.magic-login__verify-code-field-container {
	display: flex;
	gap: 16px;
	justify-content: space-between;
	margin-top: 45px;
	margin-bottom: 32px;

	.form-text-input.magic-login__verify-code-character-field {
		max-width: 61px;
		height: 64px;
		text-align: center;
		padding: 0;
		border: 1px solid var(--color-neutral-5, #dcdcdc);
		border-radius: 4px;
		@include heading-2x-large;

		&:first-child {
			margin-inline-start: 0;
		}

		&:last-child {
			margin-inline-end: 0;
		}

		&:focus {
			border: 2px solid var(--studio-jetpack-green-50, #008710);
			box-shadow: none;
		}
	}

}

.magic-login__verify-code-form--error {
	.magic-login__verify-code-field-container {
		margin-bottom: 12px;

		.form-text-input.magic-login__verify-code-character-field {
			border-color: var(--magic-login-error-color);
			background-color: color-mix(in srgb, var(--magic-login-error-color) 5%, var(--studio-white, #fff) 95%);
		}
	}

	.magic-login__verify-code-error-message {
		@include body-large;
		color: var(--magic-login-error-color);
		margin-bottom: 32px;
	}
}
