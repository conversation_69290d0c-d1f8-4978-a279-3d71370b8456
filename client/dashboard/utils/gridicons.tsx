import {
	audio,
	background,
	backup,
	brush,
	caution,
	check,
	cloud,
	cog,
	comment,
	commentAuthorAvatar,
	connection,
	customPostType,
	error,
	homeButton,
	image,
	layout,
	lock,
	menu,
	pages,
	people,
	postContent,
	plugins,
	receipt,
	swatch,
	trash,
	update,
	video,
	wordpress,
} from '@wordpress/icons';

const icons: Record< string, React.ReactElement > = {
	audio,
	checkmark: check,
	cart: receipt,
	cloud,
	cog,
	comment,
	'custom-post-type': customPostType,
	history: backup,
	image,
	layout,
	lock,
	menu,
	'multiple-users': people,
	'my-sites': wordpress,
	notice: caution,
	posts: postContent,
	pages,
	plans: connection,
	plugins,
	science: swatch,
	spam: error,
	status: homeButton,
	sync: update,
	themes: brush,
	trash,
	user: commentAuthorAvatar,
	video,
};

export function gridiconToWordPressIcon( slug: string ): React.ReactElement {
	return icons[ slug ] ?? background;
}
