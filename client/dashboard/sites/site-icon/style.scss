@import '@wordpress/base-styles/colors';
@import '@wordpress/base-styles/variables';

.site-icon,
.site-letter {
	border-radius: $radius-medium;

	&.is-small {
		border-radius: $radius-small;
	}
}

.site-icon {
	display: block;
	outline: 1px solid rgba($black, 0.04);
	outline-offset: -1px;
	background: $white;
}

.site-letter {
	align-items: center;
	background: linear-gradient(0deg, rgba($white, 0.32) 0%, rgba($white, 0.32) 100%), var(--wp-admin-theme-color);
	color: $white;
	display: flex;
	flex-shrink: 0;
	font-size: 24px;
	height: 48px;
	justify-content: center;
	overflow: hidden;
	width: 48px;
	box-sizing: border-box;
	text-transform: uppercase;
}
