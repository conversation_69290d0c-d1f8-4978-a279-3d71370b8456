import { SVG, Path } from '@wordpress/primitives';

export const production = (
	<SVG width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
		<Path
			d="M6.07129 8C6.07129 6.89543 6.96672 6 8.07129 6L16.0713 6C17.1759 6 18.0713 6.89543 18.0713 8V16C18.0713 17.1046 17.1759 18 16.0713 18H8.07129C6.96672 18 6.07129 17.1046 6.07129 16L6.07129 8Z"
			fill="#069E08"
		/>
		<Path
			d="M4.07129 16L4.07129 8C4.07129 5.79086 5.86215 4 8.07129 4V6C6.96672 6 6.07129 6.89543 6.07129 8L6.07129 16L6.08203 16.2041C6.18429 17.2128 7.03564 18 8.07129 18H16.0713L16.2754 17.9893C17.2169 17.8938 17.9651 17.1457 18.0605 16.2041L18.0713 16V8C18.0713 6.96435 17.2841 6.113 16.2754 6.01074L16.0713 6V4C18.2804 4 20.0713 5.79086 20.0713 8V16C20.0713 18.2091 18.2804 20 16.0713 20H8.07129C5.93125 20 4.1834 18.3194 4.07617 16.2061L4.07129 16ZM16.0713 4V6L8.07129 6V4L16.0713 4Z"
			fill="#069E08"
			fillOpacity="0.35"
		/>
	</SVG>
);

export const staging = (
	<SVG width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
		<Path
			d="M10.5858 5.63418C11.3668 4.85314 12.6332 4.85314 13.4142 5.63418L18.364 10.5839C19.145 11.365 19.145 12.6313 18.364 13.4124L13.4142 18.3621C12.6332 19.1432 11.3668 19.1432 10.5858 18.3621L5.63604 13.4124C4.85499 12.6313 4.85499 11.365 5.63604 10.5839L10.5858 5.63418Z"
			fill="#FFB514"
		/>
		<Path
			d="M4.22183 9.16978L9.17157 4.22003C10.7337 2.65793 13.2663 2.65793 14.8284 4.22003L13.4142 5.63424C12.6332 4.85319 11.3668 4.85319 10.5858 5.63424L5.63604 10.584L5.49931 10.7359C4.85838 11.5215 4.90372 12.6801 5.63604 13.4124L10.5858 18.3622L10.7377 18.4989C11.471 19.0972 12.529 19.0972 13.2623 18.4989L13.4142 18.3622L18.364 13.4124C19.0963 12.6801 19.1416 11.5215 18.5007 10.7359L18.364 10.584L19.7782 9.16978C21.3403 10.7319 21.3403 13.2645 19.7782 14.8266L14.8284 19.7764C13.2663 21.3385 10.7337 21.3385 9.17157 19.7764L4.22183 14.8266C2.65973 13.2645 2.65973 10.7319 4.22183 9.16978ZM19.7782 9.16978L18.364 10.584L13.4142 5.63424L14.8284 4.22003L19.7782 9.16978Z"
			fill="#FFB514"
			fillOpacity="0.35"
		/>
	</SVG>
);

export const upsell = (
	<SVG width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/SVG">
		<Path
			d="M18.9397 9.87999L15.4197 6.06999L15.3597 6.00999C15.2897 5.93999 15.1997 5.89999 15.0997 5.89999H8.87973C8.77973 5.89999 8.68973 5.93999 8.61973 6.00999L5.05973 9.87999C4.93973 10.01 4.93973 10.21 5.05973 10.34L11.5397 17.86C11.6497 17.99 11.8197 18.07 11.9997 18.07C12.1797 18.07 12.3397 17.99 12.4597 17.86L18.9397 10.34C19.0597 10.21 19.0497 10.01 18.9397 9.87999ZM15.4097 7.53999L17.3297 9.63999H15.1697L15.4097 7.53999ZM14.4297 6.83999L14.1097 9.63999H10.2897L9.64973 6.83999H14.4297ZM8.68973 7.42999L9.19973 9.63999H6.66973L8.68973 7.42999ZM6.61973 10.6H9.42973L10.8397 15.49L6.61973 10.6ZM12.0397 15.87L10.5297 10.6H13.8597L12.0397 15.87ZM14.9697 10.6H17.3797L13.3697 15.24L14.9697 10.6Z"
			fill="currentColor"
		/>
	</SVG>
);

export const launch = (
	<SVG xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
		<Path
			d="M11.9993 9C12.8277 9 13.4993 9.67157 13.4993 10.5C13.4993 11.3284 12.8277 12 11.9993 12C11.171 11.9998 10.4993 11.3283 10.4993 10.5C10.4993 9.67168 11.171 9.00018 11.9993 9Z"
			fill="currentColor"
		/>
		<Path
			fillRule="evenodd"
			clipRule="evenodd"
			d="M11.9993 3.5C13.161 3.5 13.8588 4.38147 14.1848 4.8916C14.5677 5.49076 14.8657 6.24625 15.1028 6.98828C15.5838 8.49382 15.9315 10.3245 16.1555 11.7266C16.1684 11.8072 16.1783 11.8881 16.1887 11.9688L16.8137 12.624C16.9802 12.7984 17.123 12.9944 17.2385 13.2061L19.0754 16.5742C19.6099 17.5543 18.6898 18.6898 17.6203 18.3701L15.011 17.5889L14.0451 19.4688C13.7195 20.1017 13.0666 20.5 12.3547 20.5H11.6438C10.932 20.4999 10.2789 20.1017 9.95335 19.4688L8.98656 17.5889L6.37816 18.3701C5.30893 18.6895 4.38884 17.5541 4.92308 16.5742L6.75999 13.2061C6.87545 12.9944 7.01836 12.7984 7.1848 12.624L7.80882 11.9688C7.81926 11.8881 7.83011 11.8073 7.843 11.7266C8.06698 10.3245 8.41472 8.49382 8.89574 6.98828C9.13281 6.24629 9.43087 5.49076 9.8137 4.8916C10.1396 4.38154 10.8376 3.50017 11.9993 3.5ZM11.2873 18.7832C11.3473 18.8998 11.4606 18.9782 11.5881 18.9961L11.6438 19H12.3547C12.4859 19 12.6076 18.9354 12.6819 18.8301L12.7112 18.7832L13.4133 17.417H10.5852L11.2873 18.7832ZM6.5305 16.7578L8.343 16.2158C8.11176 15.6152 7.94339 14.9899 7.84398 14.3506L6.5305 16.7578ZM16.1545 14.3516C16.0551 14.9906 15.8867 15.6155 15.6555 16.2158L17.467 16.7578L16.1545 14.3516ZM11.9993 5C10.6581 5.0007 9.76617 9.19798 9.32445 11.9629C9.10976 13.3068 9.31218 14.6696 9.84007 15.917H14.1584C14.6485 14.7591 14.8584 13.5023 14.7141 12.2529L14.6741 11.9629C14.2323 9.19775 13.3405 5 11.9993 5Z"
			fill="currentColor"
		/>
	</SVG>
);
