<svg
  viewBox="0 0 60 38"
  width="60"
  height="38"
  xmlns="http://www.w3.org/2000/svg"
>
  <defs>
    <!-- Define the clipping path -->
    <clipPath id="paths">
      <!-- Adjust the coordinates of the paths to fit the viewBox -->
      <rect
        xmlns="http://www.w3.org/2000/svg"
        x="0.5"
        y="0.5"
        width="59"
        height="37"
        rx="3"
        fill="none"
      />
    </clipPath>
    <!-- Adjusted gradients to fit the viewBox -->
    <radialGradient
      id="cbb"
      cx="-5"
      cy="70"
      gradientUnits="userSpaceOnUse"
      r="140"
    >
      <stop offset=".09" stop-color="#009245" />
      <stop offset=".23" stop-color="#049552" stop-opacity=".89" />
      <stop offset=".52" stop-color="#0d9e74" stop-opacity=".59" />
      <stop offset=".91" stop-color="#1bacab" stop-opacity=".12" />
      <stop offset="1" stop-color="#1fb0b8" stop-opacity="0" />
    </radialGradient>
    <radialGradient
      id="cbc"
      cx="-5"
      cy="70"
      gradientUnits="userSpaceOnUse"
      r="140"
    >
      <stop offset=".15" stop-color="#1fb0b8" stop-opacity="0" />
      <stop offset=".35" stop-color="#1c7491" stop-opacity=".4" />
      <stop offset=".56" stop-color="#1a4471" stop-opacity=".73" />
      <stop offset=".74" stop-color="#18265e" stop-opacity=".93" />
      <stop offset=".87" stop-color="#181b57" />
    </radialGradient>
  </defs>

  <g clip-path="url(#paths)">
    <!-- Background paths adjusted to fit the viewBox -->
    <path d="M0 0h60v38H0z" fill="#29abe2" />
    <path d="M0 0h60v38H0z" fill="url(#cbb)" />
    <path d="M0 0h60v38H0z" fill="url(#cbc)" />
  </g>
  <!-- Adjusted position and scaling for the foreground paths -->
  <g fill="#fff" transform="scale(1.8) translate(3, 1.5)">
    <path
      d="M14.39 3.86h7.07a2.47 2.47 0 0 1 2.47 2.47 2.47 2.47 0 0 1-2.47 2.47h-7.07V3.86zM14.39 9.36h7.07a2.47 2.47 0 0 1 2.47 2.47 2.47 2.47 0 0 1-2.47 2.47h-7.07V9.36zM8.23 9.36V8.8h5.69a5.51 5.51 0 0 0-5.69-4.94 5.47 5.47 0 0 0-5.69 5.22 5.47 5.47 0 0 0 5.69 5.22 5.51 5.51 0 0 0 5.69-4.94z"
    />
  </g>
</svg>
