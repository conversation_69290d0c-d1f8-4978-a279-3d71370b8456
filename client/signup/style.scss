@import "@automattic/onboarding/styles/mixins";
@import "@wordpress/base-styles/breakpoints";
@import "@wordpress/base-styles/mixins";

body.is-section-signup {
	background: var( --color-main-background, #fcfcfc );

	.layout:not(.dops) .wpcom-site__logo {
		/* fill: var(--color-neutral-10); */
		/* color: var(--color-neutral-10); */
		/* opacity: 1; */
		fill: var(--color-primary-dark);
		color: var(--color-primary-dark);
		opacity: 0.3;

		path {
			fill: var(--color-neutral-10);
			opacity: 1;
		}
	}

	/**
	* Removed the navigation back button step from mobile viewports.
	* This is done because the there is usally already a back button displayed to the user (<PERSON>rowser back button)
	* and the current back button is redundent.
	*/
	.signup__step .plans.plans-step .step-wrapper__navigation {
		@include breakpoint-deprecated("<660px") {
			display: none;
		}
	}

	.layout {

		// this is awkward but we need to remove the padding from the layout__content
		// to account for the unified top-bar used in Login and User Steps (Signup)
		// while preserving the content padding for non-user steps
		.layout__content {
			padding: 0;

			.signup__steps > .signup__step > .signup__step:not(.is-oauth2-user):not(.is-user-social) {
				padding-top: 48px;
			}
		}

		.locale-suggestions {
			margin: 0;
		}

		.locale-suggestions__notice {
			&.calypso-notice.is-compact.is-light.is-info {
				margin: 0;
			}
		}

		.signup-form {
			max-width: 400px;
			width: 100%;
			margin: 0 auto;
			padding: 0;
			display: flex;
			flex-direction: column;

			input[type="email"].form-text-input {
				margin-bottom: 0;
			}
		}

		.card.logged-out-form {
			box-shadow: none;
			background-color: initial;
			padding: 0;
			.form-fieldset {
				margin-bottom: 0;
			}
		}

		.card.auth-form__social {
			margin: 0;
			padding-top: 0;
			padding-bottom: 0;
			box-shadow: none;
			background-color: initial;
		}

		.auth-form__separator {
			margin: 0 auto;
			max-width: 360px;
			width: 100%;

			.auth-form__separator-text {
				padding: 0 24px;
			}

			&::before,
			&::after {
				position: absolute;
				border-inline-start: 0;
				border-block-start: 1px solid #e6e6e6;
				inset-block-start: 50%;
				inset-inline-start: 0;
				width: 42%;
			}

			&::after {
				left: 58%;
			}
		}

		.signup-form.is-horizontal {
			gap: 1em;
		}

	}

	// Hide the masterbar for realz
	&.has-no-masterbar .masterbar {
		display: none;
	}

	.layout__secondary {
		background: var(--color-primary);
		border: 0;
	}
}

// Notice the :not(.dops) selector. I've added this to try and
// avoid stepping on the toes of our oauth users, like Crowdsignal.
body.is-section-signup .layout:not(.dops) {
	// If there's an error lets make it look a little better
	// on the primary colored background.
	.empty-content {
		.empty-content__illustration {
			background: var(--color-surface);
			padding-bottom: 16px;
			margin-bottom: 24px;
			border-radius: 4px;
			@include elevation(3dp);
		}

		.empty-content__title {
			color: var(--color-text-inverted);
		}
	}

	//Masterbar is hidden but still has height
	//which is how sticky panel offset is calculated.
	//Setting height to zero removes the offset
	//so the sticky panel sticks to the top.
	&.has-no-masterbar .masterbar {
		height: 0;
	}

	// This allows us to position the search suggestions
	// relative to their fieldset.
	.form-fieldset {
		position: relative;
	}

	.suggestions {
		position: absolute;
		top: 42px;
		left: 0;
		right: 0;
		max-height: 350px;
		overflow: auto;
		@include elevation(2dp);
	}

	@include breakpoint-deprecated("<660px") {
		.layout:not(.is-horizontal-layout) button:not(.is-compact):not(.is-primary) {
			font-size: $font-body;
			padding-top: 12px;
			padding-bottom: 14px;
		}
	}
}

.is-section-signup .layout__content,
.is-section-signup .layout__primary {
	overflow: visible;
}

.layout.is-wccom-oauth-flow {
	@import "jetpack-connect/colors";
	@include woocommerce-colors();
	background-color: var(--color-woocommerce-onboarding-background);
	height: 100%;

	.layout__content {
		padding-top: 48px;
	}

	.masterbar {
		display: none;
	}

	.formatted-header {
		max-width: 476px;
		margin-right: auto;
		margin-left: auto;

		.formatted-header__subtitle {
			color: var(--color-primary-10);
			margin-top: 1em;
			font-size: $font-body;
			line-height: 24px;
			font-weight: 400;
		}
	}

	.logged-out-form {
		max-width: 476px;
	}

	.logged-out-form__link-item {
		text-align: center;
		text-decoration: underline;
		color: var(--studio-gray-60);
		font-size: $font-body-small;
	}

	.signup-form__terms-of-service-link {
		text-align: left;
		color: var(--studio-gray-60);
	}

	.signup-form__terms-of-service-link a,
	.auth-form__social-buttons-tos a {
		text-decoration: underline;
	}

	.auth-form__social {
		padding-bottom: 0;
		margin-top: 16px;

		p {
			font-size: $font-body-extra-small;
			color: var(--studio-gray-60);
		}

		.social-buttons__button {
			border: 1px solid var(--studio-wordpress-blue);
			color: var(--studio-wordpress-blue);
			box-shadow: none;
			max-width: 250px;
			height: 48px;
		}
	}

	.logged-out-form__footer {
		text-align: center;

		.button.is-primary {
			border: 0;
			box-shadow: none;
			max-width: 310px;
			margin-right: auto;
			margin-left: auto;
			height: 48px;
		}
	}
}

body.is-section-stepper,
body.is-section-signup .layout:not(.dops):not(.is-wccom-oauth-flow) {
	$gray-100: #101517;
	$gray-60: #50575e;
	$gray-50: #646970;
	$breakpoint-mobile: 660px;

	--color-accent: var(--studio-wordpress-blue-50);
	--color-accent-60: var(--studio-wordpress-blue-60);

	.signup-header {
		.wordpress-logo {
			position: absolute;
			inset-inline-start: 20px;
			inset-block-start: 20px;
			fill: var(--color-text);
			transform-origin: 0 0;
		}

		.signup-header__right {
			inset-block-start: 22px;
			inset-inline-end: 20px;

			.flow-progress-indicator {
				font-weight: 500;
				font-size: 0.875rem;
				color: var(--studio-gray-30);
			}
		}

		@include break-small {
			.wordpress-logo {
				inset-inline-start: 24px;
			}

			.signup-header__right {
				inset-inline-end: 24px;
			}
		}
	}

	.step-wrapper__navigation {
		.navigation-link.button.is-borderless {
			color: $gray-100;

			svg {
				width: 20px;
				height: 20px;
				top: 5px;
				margin-right: 2px;
				fill: $gray-100;
			}
		}

		@include break-small {
			inset-inline-start: 72px;
			inset-inline-end: 24px;
		}
	}

	.signup:not(.is-onboarding-2023-pricing-grid) .step-wrapper:not(.is-horizontal-layout),
	// Stepper's header.
	.step-container:not(.is-horizontal-layout) {
		// Stepper's header.
		.step-container__header,
		.step-wrapper__header {
			margin: 24px 20px;

			@include break-large {
				margin: 48px 0 36px;
			}
		}
	}

	.step-wrapper__header-button {
		@include breakpoint-deprecated("<660px") {
			margin-top: 16px;
			margin-bottom: -16px;
		}
	}

	.formatted-header {
		margin: 0;

		.formatted-header__title {
			@include onboarding-font-recoleta;
			color: $gray-100;
			margin-top: 0;
			font-size: 2.25rem;

			@include break-xlarge {
				font-size: 2.75rem;
			}
		}

		.formatted-header__subtitle {
			color: $gray-60;
			font-size: 1rem;
			margin-bottom: 0;
		}

		.formatted-header__title,
		.formatted-header__subtitle {
			@include break-xlarge {
				padding: 0;
			}
		}
	}

	.signup__step.is-user,
	.signup__step.is-user-social {
		.button.signup-form__submit[disabled],
		.button.signup-form__submit:disabled,
		.button.signup-form__submit.disabled {
			background-color: var(--studio-gray-20);
			color: var(--color-surface);
		}

		.button.signup-form__submit {
			border-radius: 4px;
			max-width: 408px;
			height: 44px;
			box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
			font-weight: 500;
			letter-spacing: 0.32px;
			line-height: 17px;

			&.newsletter-signup-form {
				background-color: var(--color-primary);
			}
		}

		.signup-form__terms-of-service-link {
			text-align: start;
			font-size: 0.875rem;
			margin-bottom: 32px;
		}

		.form-password-input .form-password-input__toggle-visibility {
			right: 17px;
		}
	}
	// ::::: Onboarding_PM_Passwordless_Experiment ::::::
	// This selector is part of an experiment which is supposed to be cleaned up
	// https://github.com/Automattic/wp-calypso/pull/83886
	.is-onboarding-pm .signup__step.is-user.is-passwordless-experiment,
	.signup__step.is-user-social {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		min-height: calc(100vh - 96px);

		.step-wrapper {
			width: 100%;
			margin-bottom: 48px;

			@media screen and (max-width: $breakpoint-mobile) {
				margin-bottom: 0;
			}
		}

		.locale-suggestions {
			width: 100%;
		}

		.step-wrapper__header {
			margin-bottom: 3rem !important;
		}

		a.step-wrapper__navigation-link {
			color: #1d2327;
			font-family: "SF Pro Text", sans-serif;
			font-size: 0.875rem;
			font-style: normal;
			font-weight: 500;
			line-height: 20px;
			text-underline-offset: 5px;

			&:hover {
				color: var(--studio-blue-50, #0675c4);
			}

			&:focus {
				outline: 1px dashed var(--studio-blue-50, #0675c4);
				outline-offset: 3px;
				box-shadow: none;
				border-radius: 0;
			}
		}
	}

	// ::::: Onboarding_PM_Passwordless_Experiment ::::::
	// This selector is part of an experiment which is supposed to be cleaned up
	// https://github.com/Automattic/wp-calypso/pull/83886
	.is-onboarding-pm .signup__step.is-user.is-passwordless-experiment {
		.social-buttons__button {
			border: 0;
			> svg {
				border: none;
				padding: 0;
				border-radius: 0;

			}
		}
	}

	.signup__step.is-mailbox-domain,
	.signup__step.is-emails,
	.signup__step.is-mailbox {
		.is-wide-layout {
			max-width: 1280px;
		}
	}

	.signup__step.is-difm-design-setup-site {
		.step-wrapper__header {
			@include breakpoint-deprecated("<660px") {
				margin-left: 0;
				margin-right: 0;
			}
		}

		.design-picker__preview .step-wrapper__header {
			margin-top: 40px;

			.formatted-header__title {
				font-size: 2rem;
			}

			@include break-mobile {
				margin-top: 12px;
			}
		}
	}

	button.is-borderless {
		box-shadow: none;
		border: 0;

		&:focus {
			border-color: var(--color-primary);
			box-shadow: 0 0 0 2px var(--color-primary-light);
		}
	}
}

body.is-section-signup .is-gravatar {
	height: 100%;
	background-color: #e7e9ea;

	@include break-small {
		.layout__content {
			padding-top: 80px;
		}
	}
}

:root {
	--signup-form-column-max-width: 408px;
}

@include break-mobile {
	:root {
		--signup-form-column-max-width: 600px;
	}
}

@include break-small {
	:root {
		--signup-form-column-max-width: 408px;
	}
}

@include break-medium {
	:root {
		--signup-form-column-max-width: 350px;
	}
}

@include break-large {
	:root {
		--signup-form-column-max-width: 408px;
	}
}
