.woocommerce {
	@import 'app/dashboard/style';
	@import 'app/dashboard/widgets/inventory-widget/style';
	@import 'app/dashboard/setup/style';
	@import 'app/order/style';
	@import 'app/order/order-created/style';
	@import 'app/order/order-customer/style';
	@import 'app/order/order-details/style';
	@import 'app/order/order-fulfillment/style';
	@import 'app/order/order-activity-log/style';
	@import 'app/order/order-payment/style';
	@import 'app/orders/style';
	@import 'app/settings/payments/style';
	@import 'app/settings/email/mailchimp/style';
	@import 'app/settings/email/email-settings/style';
	@import 'app/products/product-form.scss';
	@import 'app/products/products-list.scss';
	@import 'app/product-categories/style';
	@import 'app/promotions/style';
	@import 'app/reviews/style';
	@import 'app/settings/shipping/style';
	@import 'app/settings/taxes/style';
	@import 'app/store-stats/store-stats-chart/style';
	@import 'app/store-stats/store-stats-orders-chart/style';
	@import 'app/store-stats/store-stats-module/style';
	@import 'app/store-stats/store-stats-referrer-widget/style';
	@import 'app/store-stats/store-stats-referrer-widget-base/style';
	@import 'app/store-stats/store-stats-referrer-conv-widget/style';
	@import 'app/store-stats/referrers/style';
	@import 'app/store-stats/referrers/chart/style';
	@import 'app/store-stats/store-stats-widget-list/style';
	@import 'app/store-stats/style';

	@import 'components/action-header/style';
	@import 'components/address-view/style';
	@import 'components/bulk-select/style';
	@import 'components/compact-tinymce/style';
	@import 'components/d3/base/style';
	@import 'components/d3/horizontal-bar/style';
	@import 'components/d3/sparkline/style';
	@import 'components/dashboard-widget/style';
	@import 'components/delta/style';
	@import 'components/extended-header/style';
	@import 'components/filtered-list/style';
	@import 'components/form-click-to-edit-input/style';
	@import 'components/form-dimensions-input/style';
	@import 'components/image-thumb/style';
	@import 'components/list/style';
	@import 'components/location-flag/style';
	@import 'components/order-status/style';
	@import 'components/price-input/style';
	@import 'components/product-image-uploader/style';
	@import 'components/product-reviews-widget/style';
	@import 'components/reading-widget/style';
	@import 'components/share-widget/style';
	@import 'components/store-address/style';
	@import 'components/table/style';
	@import 'components/woocommerce-colophon/style';

	@import 'app/dashboard/widgets/stats-widget/style';

	@import 'woocommerce-services/style';

	.main {
		padding-top: 35px;

		@include breakpoint( '<660px' ) {
			padding-top: 72px;
		}
	}

	.woocommerce__placeholder {
		padding-top: 32px;

		@include breakpoint( '<660px' ) {
			padding-top: 16px;
		}
	}

	.woocommerce__placeholder-card {
		@include placeholder();
		height: 70vh;
	}

	@include breakpoint( '>660px' ) {
		&.store-stats {
			.stats-navigation .section-nav {
				margin-top: 0;
			}
		}
	}
}

.is-section-woocommerce {
	// overwrite notice styles due to sticky bar
	.global-notices {
		z-index: z-index( 'root', '.is-section-woocommerce .global-notices' );

		@include breakpoint( '>660px' ) {
			top: 115px;
		}
	}

	.content-404 {
		margin: 35px 200px 0 0;
		max-width: initial;

		@media( max-width: 660px ) {
			margin: 0;
		}
	}
}

.store-sidebar__sidebar {
	display: block;

	.store-sidebar__ground-control {
		align-items: center;
		background-color: var( --color-white );
		display: flex;
		width: 100%;
	}

	.store-sidebar__ground-control-back {
		flex: 0 0 auto;
		margin-left: 7px;
	}

	.store-sidebar__ground-control-site {
		display: inline-block;
		flex-grow: 1;
		max-height: 60px;

		.site__content {
			padding: 12px;
			padding-left: 8px;
		}
	}

	@include breakpoint( '>660px' ) {
		margin-top: 47px;
		padding-top: 4px;

		.store-sidebar__ground-control-site {
			display: none;
		}

		.store-sidebar__ground-control {
			padding-left: 7px;
			position: fixed;
			width: 100%;
			top: 47px;
			left: 0;
			min-height: 46px;
			z-index: z-index( 'root', '.sticky-panel.is-sticky .sticky-panel__content' );
			background: var( --color-white );
			border-bottom: 1px solid var( --color-neutral-100 );
		}
	}

	@include breakpoint( '<660px' ) {
		.site__title {
			margin-top: 8px;
		}
		.site__domain {
			display: none;
		}
	}

	.sidebar__menu {
		margin-top: 0;

		@include breakpoint( '<660px' ) {
			border-top: 1px solid var( --sidebar-border-color );
		}

		@include breakpoint( '>660px' ) {
			margin-top: -4px;
		}

		ul {
			li:first-child {
				border-top-width: 0;
			}
		}
	}

	.sidebar__menu li {
		.count {
			margin: 13px 8px;
			line-height: 18px;
			border: 0;
			background-color: var( --color-primary );
			color: var( --color-white );
			min-width: 20px;
		}

		&.selected {
			.count,
			&:hover .count {
				color: var( --color-neutral-700 );
				background-color: var( --color-white );
			}
		}

		&:hover .count {
			background-color: var( --color-primary );
			color: var( --color-white );
		}
	}
}

.is-section-woocommerce.focus-sidebar {
	@include breakpoint( '<660px' ) {
		.products__form {
			display: none;
		}
	}
}
