.price-input {
	&.has-reset .form-text-input {
		padding-right: 14px;
		border-right-width: 0;
		z-index: 9;

		&:focus {
			border-right-color: var( --color-primary );
			border-right-width: 1px;
		}
	}

	.form-text-input-with-affixes__suffix {
		background: transparent;
		position: relative;
		min-width: 12px;

		.button {
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			padding: 0;
			width: 100%;

			.gridicon {
				top: 0;
				margin: 0 auto;
			}
		}

		.gridicon {
			margin: 0;
		}
	}
}
