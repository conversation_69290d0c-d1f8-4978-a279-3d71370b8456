.form-dimensions-input {
	display: flex;
	justify-content: flex-start;
	flex-wrap: wrap;

	&.no-wrap {
		flex-wrap: nowrap;

		.form-dimensions-input__length input,
		.form-dimensions-input__width input {
			border-bottom-width: 1px;
		}
	}

	@include breakpoint( '>480px' ) {
		flex-wrap: nowrap;

		.text-control-with-affixes {
			flex-grow: 2;
			flex-direction: row;
		}
	}

	.form-dimensions-input__length input,
	.form-dimensions-input__width input {
		border-bottom-width: 0;
		@include breakpoint( '>480px' ) {
			border-bottom-width: 1px;
		}
	}

	.form-dimensions-input__width input,
	.form-dimensions-input__height input {
		@include breakpoint( '>480px' ) {
			margin-left: -1px;
		}
	}

	.form-dimensions-input__length input,
	.form-dimensions-input__width input,
	.form-dimensions-input__height input {
		@include breakpoint( '>480px' ) {
			width: 75px;
			flex-grow: 0;
		}

		&:hover,
		&:focus {
			transform: scale( 1 );
		}

		&:focus + .form-dimensions-input__width input:hover,
		&:focus + .form-dimensions-input__height .text-control-with-affixes input:hover {
			transform: none;
		}
	}

	.text-control-with-affixes__suffix {
		flex-grow: 0;
		background: inherit;
	}

}
