.share-widget__container {
	@include breakpoint( '>660px' ) {
		padding-top: 48px;
	}

	ul.share-widget__services {
		list-style-type: none;
		margin: 0 auto;
		max-width: 480px;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;

		li.share-widget__service {
			margin: 0 4px;

			a {
				transition: transform ease-in-out 0.15s;
				display: block;

				&:hover {
					transform: translateY( -4px );
				}
			}
		}
	}

	img {
		max-width: 600px;
	}
}

.share-widget__service .social-logo {
	&.facebook {
		color: var( --color-facebook );
	}

	&.twitter {
		color: var( --color-twitter );
	}

	&.google-plus {
		color: var( --color-gplus );
	}

	&.linkedin {
		color: var( --color-linkedin );
	}

	&.tumblr {
		color: var( --color-tumblr );
	}

	&.pinterest {
		color: var( --color-pinterest );
	}
}
