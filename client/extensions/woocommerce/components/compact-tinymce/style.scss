.compact-tinymce {
	.mce-toolbar .mce-btn-group .mce-btn.mce-listbox:hover {
		border-left-color: transparent;
		border-right-color: var( --color-neutral-100 );
	}

	.mce-edit-area {
		border: 1px solid var( --color-neutral-100 ) !important;
		margin-bottom: 4px;
		padding-top: 36px;
		padding-left: 8px;
	}

	div.mce-toolbar-grp {
		background-color: rgba( var( --color-white-rgb ), 0.92 );
		border-color: var( --color-neutral-100 );
		border-style: solid;
		border-width: 1px;
		padding: 0;
		overflow-x: auto;
		margin: 0 auto;
	}

	.mce-toolbar .mce-btn-group .mce-btn.mce-listbox {
		margin: 0 2px 0 0;
		padding: 0 8px;
		vertical-align: top;
		border-right: 1px solid var( --color-neutral-100 );
		height: 36px;

		@include breakpoint( '<660px' ) {
			height: 44px;
		}
	}

	.mce-tinymce .mce-stack-layout-item.mce-last {
		border-top-width: 0;
	}

	.mce-container.mce-tinymce {
		& > .mce-container-body {
			&::after {
				@include breakpoint( '<660px' ) {
					@include long-content-fade( $size: 0 );
				}

				@include breakpoint( '660px-960px' ) {
					@include long-content-fade( $size: 0 );
				}
			}
		}
	}

	.mce-toolbar .mce-btn-group .mce-btn:hover,
	.mce-toolbar .mce-btn-group .mce-btn:focus,
	.mce-toolbar .mce-btn-group .mce-btn.mce-listbox:hover,
	.mce-toolbar .mce-btn-group .mce-btn.mce-listbox:focus {
		box-shadow: none;
	}

	.mce-toolbar .mce-btn-group .mce-ico {
		color: var( --color-neutral-500 );
	}

	.mce-toolbar .mce-btn-group .mce-btn .gridicon {
		fill: var( --color-neutral-500 );
	}

	.mce-toolbar .mce-btn-group .mce-btn:hover .mce-ico,
	.mce-toolbar .mce-btn-group .mce-btn:focus .mce-ico {
		color: var( --color-neutral-700 );
	}

	.mce-toolbar .mce-btn-group .mce-btn:hover .gridicon,
	.mce-toolbar .mce-btn-group .mce-btn:focus .gridicon {
		fill: var( --color-neutral-700 );
	}
}
