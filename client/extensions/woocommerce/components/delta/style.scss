.delta {
	align-items: center;
	display: flex;
	flex-direction: row;
	margin: 0 auto;

	&.is-favorable {
		.delta__icon {
			&.gridicon {
				fill: var( --color-success );
			}
		}
		.delta__labels {
			.delta__value {
				color: var( --color-success );
			}
		}
	}
	&.is-unfavorable {
		.delta__icon {
			&.gridicon {
				fill: var( --color-error );
			}
		}
		.delta__labels {
			.delta__value {
				color: var( --color-error );
			}
		}
	}
	&.is-neutral {
		.delta__icon {
			&.gridicon {
				fill: var( --color-neutral-light );
			}
		}
		.delta__labels {
			.delta__value {
				color: var( --color-neutral-light );
			}
		}
	}

	.delta__icon {
		width: 20px;
		height: 20px;
		border-radius: 5px;
		min-width: 15px;

		&.gridicon {
			fill: white;
			padding: 3px 1px;
			margin: 0;
		}
	}

	.delta__labels {
		align-items: left;
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;

		.delta__suffix,
		.delta__value {
			color: var( --color-neutral-light );
			display: inline-block;
			font-size: 13px;
			font-weight: 300;
			line-height: 13px;
			text-align: left;
		}
		.delta__value {
			margin-right: 6px;
		}
	}
}
