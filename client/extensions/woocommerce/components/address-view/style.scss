.address-view__address {
	margin-bottom: 8px;

	p {
		margin-bottom: 0;

		&.address-view__address-name {
			font-weight: bold;
		}
	}
}

.form-label {
	color: var( --color-neutral-700 );
}

.address-view__editable-city-state-postcode {
	display: flex;

	@include breakpoint( '>960px' ) {
		align-items: flex-end;
	}

	.form-fieldset {
		flex: 1;
		margin-right: 16px;

		&:last-child {
			margin-right: 0;
		}
	}

	@include breakpoint( '<960px' ) {
		flex-direction: column;

		label + select {
			width: 100%;
		}

		.form-fieldset {
			margin-right: 0;
			width: 100%;
		}
	}

	.form-select {
		width: 100%;
	}
}

.address-view__country {
	margin-bottom: 32px;

	.form-select {
		width: 100%;
	}
}

.address-view__show-line-2 {
	margin-top: -20px;
}
