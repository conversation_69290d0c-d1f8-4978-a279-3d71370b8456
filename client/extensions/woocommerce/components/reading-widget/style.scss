.reading-widget__container {
	box-sizing: border-box;
	background: var( --color-white );
	padding-bottom: 0;
	box-shadow: 0 0 0 1px rgba( var( --color-neutral-100-rgb ), 0.5 ),
		0 1px 2px var( --color-neutral-0 );

	.reading-widget__heading {
		h2 {
			font-size: 20px;
			margin-bottom: 16px;
		}

		p {
			font-size: 14px;
		}
	}

	.reading-widget__article-list {
		list-style: none;
		margin-left: 0;
		margin-bottom: 24px;

		li {
			margin-bottom: 12px;
			padding-top: 12px;
			border-top: 1px solid var( --color-neutral-0 );

			&:first-child {
				border-top: 0;
				padding-top: 0;
			}

			a {
				display: block;
			}

			span {
				color: var( --color-neutral-700 );
				font-size: 12px;
			}
		}
	}

	.reading-widget__subscription-form {
		align-items: stretch;
		border-top: 1px solid var( --color-neutral-0 );
		background: var( --color-neutral-0 );
		display: flex;
		flex-direction: column;
		padding: 24px 16px 0;
		margin-left: -16px;
		margin-right: -16px;

		@include breakpoint( '>660px' ) {
			flex-direction: row;
		}

		@include breakpoint( '>480px' ) {
			padding: 24px 24px 0;
			margin-left: -24px;
			margin-right: -24px;
		}

		.reading-widget__subscription-form-explanation {
			flex: 1 0 0;
			font-size: 14px;
			margin-bottom: 24px;

			@include breakpoint( '>660px' ) {
				padding-right: 16px;
			}

			p {
				margin-bottom: 0;

				&:nth-child( 2 ) {
					margin-top: 1.5em;
				}
			}
		}

		.reading-widget__subscription-form-fields {
			flex: 1 0 0;

			@include breakpoint( '>660px' ) {
				padding-left: 16px;
			}

			.form-text-input {
				margin-bottom: 16px;
			}

			.multi-checkbox {
				margin-bottom: 8px;
				font-size: 13px;
				display: flex;
				flex-wrap: wrap;

				label {
					width: 50%;
					margin-bottom: 8px;
				}
			}

			.button {
				margin-bottom: 24px;
			}
		}
	}
}
