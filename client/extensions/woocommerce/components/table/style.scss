.table {
	table {
		margin: 0;
	}

	padding: 0;

	thead .table-heading,
	thead .table-item {
		border-bottom: 1px solid var( --color-neutral-100 );
	}

	tbody .table-heading,
	tbody .table-item {
		border-bottom: 1px solid var( --color-neutral-0 );
	}

	// Remove the border from the last item in the body of the table.
	tbody tr:last-child .table-heading,
	tbody tr:last-child .table-item {
		border: none;
	}

	&.is-compact-table {
		padding: 0.5em 0;

		.table-heading,
		.table-item {
			border-bottom: none;
		}

		.table-row {
			line-height: 28px;
			&.is-header {
				.table-item__cell-title::after {
					display: none;
				}

				&:hover {
					background-color: initial;
				}
			}

			&:hover {
				background: var( --color-neutral-0 );

				.table-item__cell-title:after {
					background-image: linear-gradient(
						to right,
						rgba( 255, 255, 255, 0 ) 0%,
						var( --color-neutral-0 ) 90%
					);
				}
			}
		}

		.table-heading,
		.table-item {
			text-align: right;
			padding: 0 8px;
			white-space: nowrap;
			font-size: 12px;

			&:first-child {
				padding: 0 8px 0 16px;
			}

			&:last-child {
				padding: 0 16px 0 8px;
			}

			&.is-title-cell {
				text-align: left;
				width: 100%;
				// https://stackoverflow.com/questions/9789723/css-text-overflow-in-a-table-cell
				max-width: 0;
			}

			svg {
				margin: auto 0 auto auto;
			}
		}

		.table-item__cell-title {
			width: 100%;
			position: relative;
			overflow: hidden;

			&::after {
				background-image: linear-gradient( to right, rgba( var( --color-white-rgb ), 0 ) 0%, var( --color-white ) 90% );
				position: absolute;
				z-index: 1;
				right: 0;
				top: 0;
				bottom: 0;
				content: '';
				width: 48px;
			}
		}
	}

	&.is-horizontally-scrollable {
		.table__wrapper-shadow {
			position: relative;
			overflow: hidden;

			&::after {
				content: '';
				display: block;
				width: 24px;
				height: 100%;
				position: absolute;
				right: -12px;
				top: 0;
				background: radial-gradient(
					ellipse at center,
					rgba( var( --color-neutral-700-rgb ), 0.125 ) 0%,
					rgba( var( --color-white-rgb ), 0 ) 75%,
					rgba( var( --color-white-rgb ), 0 ) 90%
				);
			}

			@include breakpoint( '>660px' ) {
				&::after {
					display: none;
				}
			}
		}

		.table__wrapper {
			width: 1px;
			max-width: 100%;
			min-width: 100%;
			overflow-x: auto;

			table {
				white-space: nowrap;
			}
		}
	}
}

.table-row {
	&:hover {
		background: var( --color-neutral-0 );

		&.is-header {
			background-color: initial;
		}
	}

	&.is-header {
		color: var( --color-neutral-700 );
		font-weight: 600;
	}

	&.has-action {
		cursor: pointer;

		&:focus,
		&:active {
			box-shadow: inset 2px 0 0 var( --color-primary );
		}
	}
}

.table-heading,
.table-item {
	font-size: 14px;
	padding: 16px;
	vertical-align: middle;

	&:first-child {
		padding-left: 24px;
	}

	&:last-child {
		padding-right: 24px;
	}

	&.is-row-heading {
		font-weight: normal;
	}

	&.is-align-right {
		text-align: right;

		svg {
			margin: auto 0 auto auto;
		}
	}

	svg {
		display: block;
	}
}
