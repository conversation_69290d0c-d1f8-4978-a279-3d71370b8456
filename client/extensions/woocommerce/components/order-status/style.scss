.order-status__item {
	display: inline-flex;
	padding: 0 12px;
	line-height: 32px;
	background: var( --color-neutral-100 );
	border-radius: 4px;

	&.is-processing {
		background: var( --color-success-200 );
		color: var( --color-success-800 );

		span + span {
			border-color: var( --color-success );
		}
	}

	&.is-failed {
		background: var( --color-error-200 );
		color: var( --color-error-800 );

		span + span {
			border-color: var( --color-error );
		}
	}

	&.is-cancelled,
	&.is-refunded,
	&.is-completed {
		background: var( --color-neutral-0 );
		color: var( --color-neutral-700 );

		span + span {
			border-color: var( --color-neutral-100 );
		}
	}

	&.is-on-hold {
		background: var( --color-warning-50 );
		color: var( --color-warning-dark );

		span + span {
			border-color: var( --color-warning );
		}
	}

	span + span {
		margin-left: 12px;
		padding-left: 12px;
		border-left: 1px solid var( --color-neutral-light );
	}
}
