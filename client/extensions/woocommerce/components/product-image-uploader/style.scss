.product-image-uploader__wrapper {
	background: var( --color-neutral-0 );
	height: 250px;
	width: 100%;
	position: relative;
	margin-bottom: 16px;
	border: 1px dashed var( --color-neutral-light );
	border-radius: 5px;

	@include breakpoint( '>960px' ) {
		width: 250px;
	}

	@include breakpoint( '>1040px' ) {
		width: 337px;
		height: 337px;
	}

	.product-image-uploader__picker {
		position: absolute;
		top: 0;
		left: 0;
		height: 100%;
		width: 100%;
		text-align: center;
		cursor: pointer;
		display: flex;
		align-items: center;

		.file-picker,
		.product-image-uploader__file-picker {
			margin: 0 auto;

			p {
				margin: 0;
			}
		}

		.file-picker {
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: center;
			flex-direction: column;
		}

		.gridicon {
			color: var( --color-neutral-400 );
		}

		p {
			font-size: 14px;
		}

		&:hover {
			.gridicon {
				color: var( --color-neutral-600 );
			}
		}
	}
}

.product-image-uploader__picker.compact {
	position: relative;
	margin-bottom: 16px;
	margin-left: 8px;
	margin-right: 8px;
	background: var( --color-neutral-0 );
	border: 1px dashed var( --color-neutral-light );
	width: 70px;
	height: 70px;
	border-radius: 5px;
	display: inline-block;

	@include breakpoint( '>960px' ) {
		margin-left: 0;
		margin-right: 16px;
	}

	@include breakpoint( '>1040px' ) {
		width: 100px;
		height: 100px;
	}

	&:nth-child( 3n ) {
		@include breakpoint( '>960px' ) {
			margin-right: 0;
		}
	}

	.file-picker,
	.product-image-uploader__file-picker {
		display: block;
		text-align: center;
		cursor: pointer;
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;

		@include breakpoint( '>960px' ) {
			height: 72px;
		}

		@include breakpoint( '>1040px' ) {
			height: 100px;
		}

		.gridicon {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate( -50%, -50% );
			color: var( --color-neutral-400 );
		}
	}

	&:hover {
		.gridicon {
			color: var( --color-neutral-600 );
		}
	}
}

.product-image-uploader__wrapper.placeholder {
	animation: loading-fade 1.6s ease-in-out infinite;
	cursor: default;
	.product-image-uploader__picker {
		cursor: default;
	}
}

.product-image-uploader__wrapper.placeholder.compact {
	width: 55px;
	height: 55px;
}
