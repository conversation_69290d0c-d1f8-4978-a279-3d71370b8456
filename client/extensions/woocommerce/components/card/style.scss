.card {
	display: block;
	position: relative;
	margin: 0 auto 10px;
	padding: 16px;
	box-sizing: border-box;
	background: var( --color-surface );
	box-shadow: 0 0 0 1px var( --color-border-subtle );

	@include clear-fix;

	@include breakpoint( '>480px' ) {
		margin-bottom: 16px;
		padding: 24px;
	}

	// Compact Card
	&.is-compact {
		margin-bottom: 1px;

		@include breakpoint( '>480px' ) {
			margin-bottom: 1px;
			padding: 16px 24px;
		}
	}

	&.is-card-link {
		padding-right: 48px;
		&:not( a ) {
			color: var( --color-primary );
			font-size: 100%;
			line-height: 1.5;
			text-align: left;
			width: 100%;

			&:active,
			&:focus,
			&:hover {
				color: var( --color-link-dark );
			}
		}
	}

	&.is-clickable {
		cursor: pointer;
	}

	&.is-highlight {
		padding-left: 21px;
	}

	&.is-error {
		border-left: 3px solid var( --color-error );
	}

	&.is-info {
		border-left: 3px solid var( --color-primary );
	}

	&.is-success {
		border-left: 3px solid var( --color-success );
	}

	&.is-warning {
		border-left: 3px solid var( --color-warning );
	}
}

// Clickable Card
.card__link-indicator {
	color: var( --color-text-subtle );
	display: block;
	height: 100%;
	position: absolute;
	top: 0;
	right: 16px;

	html[dir='rtl'] & {
		&.gridicons-chevron-right {
			transform: scaleX( -1 );
		}
	}
}

a.card:hover,
.is-card-link.card:hover {
	.card__link-indicator {
		color: var( --color-text );
	}
}

a.card:focus,
.is-card-link.card:focus {
	outline: 0;

	.card__link-indicator {
		color: var( --color-link-dark );
	}
}