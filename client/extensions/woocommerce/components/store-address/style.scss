&.store-location__edit-dialog {
	@include breakpoint( '>660px' ) {
		width: 660px;
	}
}

.store-address {
	&.is-placeholder {
		@include placeholder();
		pointer-events: none;
		background: var( --color-white );

		p {
			width: 300px;
			height: 14px;
			animation: loading-fade 1.6s ease-in-out infinite;
			background-color: var( --color-neutral-50 );
		}
	}

	.address-view__address {
		width: 300px;
	}

	a {
		cursor: pointer;
	}
}
