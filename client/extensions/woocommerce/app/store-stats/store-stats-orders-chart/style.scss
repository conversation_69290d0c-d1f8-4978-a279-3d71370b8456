.store-stats-orders-chart {

	.stats-tabs {
		display: flex;
		flex-direction: column;

		.stats-tab a::after {
			content: none;
		}

		@include breakpoint( '>480px' ) {
			flex-direction: row;
		}
	}

	.stats-tab {
		float: none;

		@include breakpoint( '>480px' ) {
			width: 33.3%;
		}

		a {
			align-items: center;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			box-sizing: border-box;
			padding: 10px 5px;
			width: 100%;
			height: 100%;

			.value {
				display: none;
				float: none;
			}

			.store-stats-orders-chart__value.value {
				display: inline-block;
				font-size: 20px;
				font-weight: 300;
				margin-top: 4px;
			}

			.delta {
				margin-top: 8px;
				width: initial;
			}

			.delta__icon {
				width: 16px;
				height: 16px;
			}

			@include breakpoint( '>480px' ) {
				.delta__icon {
					width: initial;
					height: initial;
				}
			}
		}
	}
}
