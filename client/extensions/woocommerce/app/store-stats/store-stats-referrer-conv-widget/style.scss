.store-stats-referrer-conv-widget.card {
	padding: 0;

	.table {
		box-shadow: none;
		margin-bottom: 0;

		.table-row.is-selected,
		.table-row.is-selected:hover {
			// color: var( --color-white );
			box-shadow: inset 4px 0 0 var( --color-accent );
			background-color: rgba( var( --color-accent-rgb ), 0.1 );
		}
		.store-stats-referrer-conv-widget__referrer {
			text-align: left;
			width: 35%;
		}
		.store-stats-referrer-conv-widget__views,
		.store-stats-referrer-conv-widget__carts,
		.store-stats-referrer-conv-widget__purchases {
			text-align: center;
			width: 15%;
		}
		.store-stats-referrer-conv-widget__delta {
			color: var( --color-neutral-light );
			text-align: center;
			width: 10%;
		}
	}
}
