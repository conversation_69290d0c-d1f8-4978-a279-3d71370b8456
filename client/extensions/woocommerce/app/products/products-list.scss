.products__list {
	.empty-content.has-title-only {
		margin-top: 42px;
	}

	.empty-content.has-title-only .empty-content__title {
		margin-bottom: 24px;
	}

	.table {
		margin-bottom: 16px;
		margin-top: 16px;
	}

	.products__list-placeholder {
		@include placeholder();
		background: var( --color-white );
		height: 525px;
		margin-bottom: 16px;
		margin-top: -15px;

		&.is-header {
			height: 50px;
			width: 100%;
		}

		.table-heading {
			border-bottom: 0;
		}

		&.pagination {
			height: 44px;
			margin-top: 16px;
		}
	}

	.is-requesting td div {
		@include placeholder();
		background: transparent;

		.image-thumb::after {
			display: none;
		}
	}

	.products__list-product .table-item__cell-title {
		display: flex;
		align-items: center;
	}

	.products__list-product .image-thumb {
		margin-right: 16px;
	}

	.is-header th {
		&:first-child {
			width: 50%;
		}

		&:nth-child( 2 ) {
			width: 20%;
		}

		&:last-child {
			width: 30%;
		}
	}

	.table-row:not( .is-header ) {
		cursor: pointer;
	}

	.table-row:hover {
		background: var( --color-neutral-0 );
	}

	.products__list-name {
		color: var( --color-primary );
	}

	.search,
	.search .search__open-icon {
		z-index: initial;
	}
}
