/**
 * TODO: Variation modal width
 */

.products__form {
	&.is-placeholder div {
		@include placeholder();
		background: var( --color-white );
		width: 720px;
		margin-bottom: 8px;

		@include breakpoint( '<480px' ) {
			width: 100%;
		}

		@include breakpoint( '660px-960px' ) {
			width: 620px;
		}

		&:first-child {
			height: 575px;
		}

		&:nth-child( 2 ) {
			height: 200px;
		}

		&:last-child {
			height: 400px;
		}
	}
}

.form-label {
	margin-bottom: 5px;
}

.foldable-card.products__variation-card {
	margin-bottom: 16px;

	.foldable-card__header {
		@include breakpoint( '>480px' ) {
			padding: 24px;
		}
	}

	.form-setting-explanation {
		padding: 16px;
	}
}

.foldable-card.products__variation-card .foldable-card__content {
	padding: 0;
	border: 0;
}

.foldable-card.products__variation-card .foldable-card__main {
	flex: inherit;
}

.products__product-form-featured {
	.form-toggle__label {
		font-weight: 400;
	}

	.form-toggle__wrapper {
		display: block;
	}
}

.products__form .token-field__input,
.products__form .token-field__token-text {
	font-size: 16px;
}

.products__product-form-details-wrapper {
	display: flex;
	flex-direction: column;
	@include breakpoint( '>960px' ) {
		flex-direction: row;
	}
}

.products__product-form-images {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;

	.products__product-form-images-item {
		position: relative;
		display: inline-block;
		margin-bottom: 16px;
		margin-right: 8px;
		margin-left: 8px;

		@include breakpoint( '>960px' ) {
			margin-left: 0;
			margin-right: 16px;
		}

		figure {
			border: 1px solid var( --color-neutral-0 );
		}

		img {
			display: block;
			width: 100%;
			height: auto;
		}
	}

	.products__product-form-images-featured {
		.products__product-form-images-item {
			margin-right: 0;
			margin-left: 0;
			clear: both;
			width: 100%;
		}
	}

	figure {
		position: relative;
		overflow: hidden;
	}

	.products__product-form-images-item.preview figure::after {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
	}

	.products__product-form-images-item.preview img {
		opacity: 0.7;
	}

	.spinner {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate( -50%, -50% );
	}

	.products__product-form-images-thumbs {
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		width: 100%;

		@include breakpoint( '>960px' ) {
			justify-content: flex-start;
		}

		.products__product-form-images-item.thumb {
			&:nth-child( 3n ) {
				@include breakpoint( '>960px' ) {
					margin-right: 0;
				}
			}
		}
	}

	.products__product-form-images-item.thumb figure {
		width: 70px;
		height: 70px;
		overflow: hidden;

		@include breakpoint( '>1040px' ) {
			width: 100px;
			height: 100px;
		}

		img {
			position: absolute;
			left: 50%;
			top: 50%;
			height: 100%;
			width: auto;
			transform: translate( -50%, -50% );
		}
	}

	.products__product-form-images-item-remove {
		position: absolute;
		top: 0;
		right: 0;
		width: 28px;
		height: 28px;
		transform: translate( 25%, -25% );
		border: 1px solid var( --color-neutral-200 );
		border-radius: 50%;
		background-color: var( --color-neutral-0 );
		cursor: pointer;

		&:hover {
			background: var( --color-neutral-0 );
		}
	}

	.products__product-form-images-item-remove-icon.gridicon {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate( -50%, -50% );
		width: 24px;
		height: 24px;
		margin: 0;
		color: var( --color-neutral-200 );

		&:hover {
			color: var( --color-neutral-light );
		}
	}
}

.products__product-form-images-wrapper {
	width: 100%;
	margin-bottom: 16px;

	@include breakpoint( '>960px' ) {
		margin-bottom: 0;
		width: 250px;
	}

	@include breakpoint( '>1040px' ) {
		width: 339px;
	}
}

.products__product-form-images-wrapper .form-setting-explanation {
	margin: 0;
}

.products__product-form-details-basic {
	flex: 2;
}

.products__product-form-images-wrapper,
.products__product-form-details-basic {
	@include breakpoint( '>960px' ) {
		margin-right: 24px;
	}

	&:last-child {
		margin-right: 0;
	}

	&:last-child {
		margin-right: 0;
	}

	&:last-child {
		margin-right: 0;
	}
}

.products__product-form-details-basic-description textarea {
	resize: vertical;
}

.products__product-form-tinymce-placeholder {
	@include placeholder();
	background: var( --color-neutral-0 );
	height: 225px;
	border: 1px solid var( --color-neutral-100 );
}

.products__product-form-details-basic-description.form-fieldset {
	margin-bottom: 0;
}

.products__categories-card {
	.form-fieldset {
		&:last-child {
			margin-bottom: 0;
		}
	}

	@include breakpoint( '>1040px' ) {
		display: flex;

		> .form-fieldset {
			width: 339px;
			margin-right: 24px;
			margin-bottom: 0;
		}
	}
}

.products__product-form-details-basic-name {
	margin-bottom: 4px;
}

.products__product-form-details-basic-sku {
	margin-top: 0;
	margin-bottom: 12px;
	font-size: 13px;

	.form-label {
		display: inline;
		margin-right: 4px;
		color: var( --color-neutral-700 );
		font-size: 13px;
		line-height: 35px;
	}

	input {
		width: 65%;
		font-size: 13px;
	}

	.form-click-to-edit-input__wrapper {
		min-height: 35px;
		display: inline-block;
		transform: translateY( 5px );

		&.editing {
			transform: none;
		}
	}

	.form-click-to-edit-input__text {
		color: var( --color-text-subtle );
		display: inline-block;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
		max-width: 220px;
	}

	.form-text-input {
		width: 220px;
		margin-bottom: 5px;
		padding: 4px 8px;
	}

	.button {
		padding: 0;
	}
}

.products__variation-types-form-wrapper {
	padding: 24px;
	border-top: 1px solid var( --color-neutral-0 );
}

.products__variation-types-form-group {
	.products__variation-types-form-fieldset,
	.products__variation-types-form-labels {
		display: flex;
	}

	.products__variation-types-form-fieldset {
		margin-bottom: 16px;

		&.is-error {
			margin-bottom: 0;
		}
	}

	.products__variation-types-form-field {
		width: 50%;
	}

	.products__variation-types-form-label {
		width: 35%;
	}

	.token-field {
		margin-left: 16px;
	}
}

.products__product-form-variation-table {
	border-spacing: 24px 16px;
	border-collapse: collapse;

	.products__product-form-variation-all-row {
		background: var( --color-neutral-0 );

		.products__product-manage-stock-toggle {
			display: inline;
		}
	}

	.products__product-form-variation-row {
		td {
			.products__product-manage-stock {
				.form-text-input {
					width: 110px;
				}
			}
		}
	}

	.products__product-form-variation-all-row + .products__product-form-variation-row {
		td {
			padding-top: 16px;
		}
	}

	.form-dimensions-input__width,
	.form-dimensions-input__height {
		margin-left: -1px;
	}

	th,
	td {
		padding: 8px;
		vertical-align: middle;

		&:first-child {
			padding-left: 24px;
		}

		&:last-child {
			padding-right: 24px;
		}
	}

	.form-text-input-with-affixes {
		flex-direction: row;
	}
}

.products__product-form-variation-table th {
	font-size: 14px;
	vertical-align: bottom;
	min-width: 122px;

	&.products__product-price {
		width: 80px;
	}
}

.products__all-variations {
	font-size: 13px;
}

.products__product-form-variation-table-wrapper {
	width: 1px;
	max-width: 100%;
	min-width: 100%;
	overflow-x: auto;
}

.products__product-form-variation-table-shadow {
	position: relative;
	overflow: hidden;

	&::after {
		content: '';
		display: block;
		width: 24px;
		height: 100%;
		position: absolute;
		right: -12px;
		top: 0;
		background: radial-gradient(
			ellipse at center,
			rgba( var( --color-neutral-700-rgb ), 0.125 ) 0%,
			rgba( var( --color-white-rgb ), 0 ) 75%,
			rgba( var( --color-white-rgb ), 0 ) 90%
		);

		@include breakpoint( '>1280px' ) {
			display: none;
		}
	}
}

.products__product-form-variation-table-wrapper .form-text-input-with-affixes .form-text-input,
.form-dimensions-input__length,
.form-dimensions-input__width {
	min-width: 80px;
}

.products__product-form-variation-table-wrapper .form-text-input-with-affixes .form-currency-input {
	min-width: 100px;
}

.products__product-form-variation-table-wrapper {
	.form-dimensions-input__length,
	.form-dimensions-input__width,
	.form-dimensions-input__height {
		max-width: 80px;
	}
}

.products__product-form-variation-image {
	margin-bottom: 0;
	margin-right: 0;
	flex-shrink: 0;
	position: relative;

	.product-image-uploader__picker.compact {
		height: 40px;
		width: 40px;
		margin-bottom: 0;
		margin-right: 0;
		margin-left: 0;

		.file-picker {
			height: 42px;
		}
	}

	&:hover {
		cursor: pointer;
	}

	.products__product-form-variation-image-remove {
		position: absolute;
		top: 0;
		right: 0;
		width: 14px;
		height: 14px;
		transform: translate( 33%, -33% );
		border: 1px solid var( --color-neutral-200 );
		border-radius: 50%;
		background-color: var( --color-neutral-0 );
		cursor: pointer;
	}

	.products__product-form-variation-image-remove-icon.gridicon {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate( -50%, -50% );
		width: 12px;
		height: 12px;
		margin: 0;
		color: var( --color-neutral-200 );
	}

	&:not( .uploader ) {
		border: 1px solid var( --color-neutral-0 );
	}

	figure {
		width: 40px;
		height: 40px;
		overflow: hidden;

		img {
			position: absolute;
			left: 50%;
			top: 50%;
			height: 100%;
			width: auto;
			transform: translate( -50%, -50% );
		}
	}

	.products__product-form-variation-image.preview figure::after {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
	}

	.products__product-form-variation-image.preview img {
		opacity: 0.7;
	}

	.spinner {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate( -50%, -50% );
	}
}

.products__product-form-variation-table-wrapper .products__product-form-variation-image {
	margin-bottom: 0;
	margin-right: 12px;
	max-height: 42px;
}

.products__product-form-variation-all-row .products__product-id {
	vertical-align: middle;
}

.products__product-name-thumb {
	display: inline-flex;
	flex-direction: row;
	align-items: center;
	transform: translateY( 2px );
}

.products__product-name {
	font-size: 13px;
	text-align: left;
}

.products__product-form-sku {
	color: var( --color-text-subtle );
}

.products__variation-settings-link {
	text-decoration: underline;
	cursor: pointer;
	color: var( --color-primary );
	&:hover {
		color: var( --color-primary-100 );
	}
}

.products__product-form-variation-dialog {
	max-width: 600px;
}

.products__product-form-variation-modal {
	min-width: 600px;
	overflow-x: auto;

	h2 {
		margin-bottom: 0;
		font-weight: bold;
		font-size: 20px;
	}

	.products__product-form-variation-description {
		margin-top: 1em;
	}

	.vertical-menu {
		width: 200px;
		flex: 1 auto;
		overflow-y: scroll;
		max-height: 600px;
	}

	.vertical-menu__items {
		padding-left: 16px;
		min-height: 45px;

		&:focus {
			color: var( --color-primary );
		}
	}

	.products__product-form-modal-wrapper {
		display: flex;
		flex-flow: row wrap;
		padding: 16px;
		max-height: 80vh;
		overflow-y: hidden;

		.compact-tinymce {
			padding: 16px;
			padding-left: 0;
			max-width: 700px;
		}
	}

	h1 {
		flex: 1 100%;
	}

	.products__product-form-modal-contents {
		padding-left: 40px;
		flex: 1 auto;
	}

	.form-toggle__wrapper {
		margin-left: 8px;
	}
}

.products__additional-details-card {
	border-top: 0;
	margin-top: -10px;

	@include breakpoint( '>480px' ) {
		margin-top: -16px;
	}

	&.is-expanded {
		margin-bottom: 16px;
	}

	.foldable-card__header {
		padding: 16px;

		@include breakpoint( '>480px' ) {
			padding: 24px;
		}

		.foldable-card__expand {
			@include breakpoint( '>480px' ) {
				width: 63px;
			}
		}
	}

	.foldable-card__content {
		@include breakpoint( '>480px' ) {
			padding: 24px;
		}
	}
}

.products__additional-details-form-labels {
	display: flex;

	label:first-child {
		width: calc( 40% - 1px );
		margin-left: 16px;
		margin-right: 8px;
	}

	label:last-child {
		width: calc( 40% - 1px );
		margin-left: 8px;
		margin-right: 16px;
	}
}

.products__additional-details-container {
	display: flex;
	flex-direction: column;
	flex-wrap: wrap;
	@include breakpoint( '>960px' ) {
		margin-top: 1em;
		flex-direction: row;
	}
}

.products__additional-details-form-group {
	width: 100%;
	padding: 16px 0;
	@include breakpoint( '>960px' ) {
		width: 60%;
		margin-right: 1.5em;
		padding: 0;
	}
}

.products__additional-details-form-inputs {
	background: var( --color-neutral-0 );
	border: 1px solid var( --color-neutral-0 );
	padding: 16px 0 0;
	margin-bottom: 8px;
	@include breakpoint( '>960px' ) {
		margin-bottom: 16px;
	}

	.form-setting-explanation {
		padding: 10px;
		padding-top: 0;
	}

	.products__additional-details-form-remove {
		.button {
			padding: 4px 6px 7px;

			@include breakpoint( '>660px' ) {
				padding-left: 12px;
				padding-right: 12px;
			}
		}
	}
}

.products__additional-details-preview-container {
	width: 100%;
	@include breakpoint( '>960px' ) {
		width: 30%;
	}
}

.products__additional-details-preview-label {
	text-transform: uppercase;
	font-size: 11px;
	color: var( --color-text-subtle );
}

.products__additional-details-preview-title {
	border-bottom: 1px solid var( --color-neutral-0 );
	padding-bottom: 10px;
	margin-bottom: 1em;
}

.products__additional-details-preview {
	border: 1px solid var( --color-neutral-100 );
	padding: 16px 16px 4px;
	margin-top: 4px;
	font-size: 13px;
	color: var( --color-neutral-400 );
}

.products__additional-details-preview-row {
	display: flex;
	flex-direction: row;
	border-bottom: 1px solid var( --color-neutral-0 );
	padding-bottom: 10px;
	margin-bottom: 1em;
}

.products__additional-details-preview-type {
	width: 35%;
	margin-right: 1em;
	font-weight: bold;
	overflow-wrap: break-word;

	& + div {
		width: 60%;
	}
}

.products__additional-details-form-fieldset {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 16px;

	.form-text-input {
		width: 40%;
		margin-right: 8px;
		margin-left: 16px;
	}

	.token-field {
		width: 40%;
		margin-left: 8px;
	}
}

.products__additional-details-form-first-row {
	.products__additional-details-form-remove {
		width: 24px;
		height: 24px;
	}
}

.products__product-form-variation-table {
	.products__product-dimensions-weight {
		flex-direction: row;

		.form-fieldset.products__product-dimensions-input {
			margin-bottom: 0;
		}

		.products__product-dimensions-input {
			margin-right: 8px;
		}
	}

	.products__product-manage-stock .form-text-input {
		width: 110px;
	}
}

.products__product-dimensions-weight {
	display: flex;
	justify-content: flex-start;
	flex-direction: column;
	max-width: 440px;

	.form-fieldset.products__product-dimensions-input {
		margin-bottom: 12px;
		width: 342px;
	}

	@include breakpoint( '>960px' ) {
		flex-direction: row;

		.form-fieldset.products__product-dimensions-input {
			margin-right: 24px;
			margin-bottom: 0;
		}
	}

	.products__product-weight-input {
		width: 150px;

		.form-text-input-with-affixes__suffix {
			flex-grow: 0;
		}

		@include breakpoint( '>480px' ) {
			.form-text-input {
				min-width: 60px;
			}
		}
	}

	.form-fieldset {
		margin-bottom: 0;
	}
}

.products__product-simple-cards {
	border-top: 0;

	.products__product-form-price {
		margin-top: -16px;
	}

	.card {
		margin-top: -10px;

		@include breakpoint( '>480px' ) {
			margin-top: -16px;
		}
	}

	.products__product-form-price .form-text-input-with-affixes {
		width: 150px;
	}

	.products__product-manage-stock {
		margin-bottom: 12px;

		@include breakpoint( '>960px' ) {
			margin-bottom: 0;
		}
	}

	.products__product-form-stock-disabled {
		.products__product-manage-stock {
			margin-bottom: 0;
		}
	}

	.products__product-manage-stock-toggle,
	.products__product-manage-stock-wrapper .form-label {
		display: inline-flex;
		cursor: pointer;
	}

	.products__product-manage-stock-toggle {
		align-items: center;

		.form-label {
			margin: 0;
			font-weight: normal;
		}
	}

	.products__product-backorders-wrapper label {
		margin-bottom: 5px;
	}

	.products__product-backorders-wrapper select {
		padding: 8px 32px 8px 14px;
	}

	.products__product-stock-options-wrapper {
		display: flex;
		flex-direction: column;

		.products__product-manage-stock {
			width: 150px;
			margin-right: 24px;
		}

		.products__product-backorders-wrapper {
			label + select {
				min-width: 150px;
			}
		}

		@include breakpoint( '>960px' ) {
			flex-direction: row;

			.products__product-manage-stock-wrapper {
				margin-bottom: 0;
			}
		}
	}
}

.products__product-form-delivery-details {
	.form-setting-explanation {
		max-width: 500px;
	}
}
