.foldable-card.card.order-activity-log__day-header,
.foldable-card.card.order-activity-log__day-header.is-expanded {
	margin: 0 0 16px;

	@include breakpoint( '>480px' ) {
		margin: 0 0 24px;
	}
}

.order-activity-log__day .foldable-card__content {
	position: relative;
	z-index: 0;

	&::before {
		content: '';
		z-index: -1;
		position: absolute;
		top: 0;
		left: 45px;
		bottom: 0;
		width: 1px;
		background: rgba( var( --color-neutral-100-rgb ), 0.5 );
	}
}

.order-activity-log .is-placeholder {
	.foldable-card__main h3,
	.foldable-card__main small,
	.order-activity-log__note-time,
	.order-activity-log__note-type,
	.order-activity-log__note-content,
	.order-activity-log__note-meta .gridicon {
		@include placeholder();
	}

	.order-activity-log__note-time {
		height: 1.3em;
	}

	.order-activity-log__note-meta .gridicon {
		fill: transparent;
	}
}

.order-activity-log__note {
	display: flex;

	& + .order-activity-log__note {
		margin-top: 16px;
	}

	.order-activity-log__note-meta {
		flex: 1 0 60px;
		width: 60px;
		text-align: center;

		.gridicon {
			display: inline-block;
			padding: 4px;
			background: var( --color-primary );
			fill: var( --color-white );
			border-radius: 50%;
			border: 4px solid white;
		}
	}

	.order-activity-log__note-time {
		display: block;
		font-size: 12px;
		background: white;
	}

	.order-activity-log__note-body {
		flex: 1 0 calc( 100% - 76px );
		width: calc( 100% - 76px );
		box-sizing: border-box;
		margin-left: 16px;
		padding: 12px 16px 16px;
		box-shadow: 0 0 0 1px rgba( var( --color-neutral-100-rgb ), 0.5 );
	}

	.order-activity-log__note-type {
		font-size: 12px;
		color: var( --color-text-subtle );
		text-transform: uppercase;
	}

	.order-activity-log__note-content {
		margin: 8px 8px 0 0;
	}
}

.order-activity-log__new-note-content {
	margin: 0 -15px 16px;
	border: 1px solid var( --color-border-subtle );
	border-width: 1px 0;

	@include breakpoint( '>480px' ) {
		margin: 0 -24px 24px;
	}

	.form-label {
		padding: 0 24px;
		margin-bottom: 8px;
	}

	.form-textarea {
		padding: 16px;
		border-color: var( --color-white );
		resize: vertical;
		display: block;
		font-size: 14px;

		&::placeholder {
			color: var( --color-text-subtle );
		}

		&:focus {
			border-color: var( --color-primary );
		}

		@include breakpoint( '>480px' ) {
			padding: 16px 24px;
		}
	}
}

.order-activity-log__new-note-type {
	.button {
		float: right;
	}
}
