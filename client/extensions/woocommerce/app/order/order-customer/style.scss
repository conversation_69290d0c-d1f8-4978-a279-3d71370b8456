.order-customer {
	.order-customer__shipping-details {
		margin-top: 32px;
	}

	.order-customer__edit-link {
		float: right;
		margin-top: -7px;
	}

	h4 {
		font-weight: bold;
	}

	.order-customer__shipping {
		.order__shipping-address {
			margin-bottom: 0;
		}
	}

	.order-customer__billing-address,
	.order-customer__billing-phone,
	.order-customer__shipping-address {
		margin-bottom: 1.5em;

		p {
			margin-bottom: 0;
		}
	}
}

.order-customer__billing-details,
.order-customer__shipping-details {
	margin-bottom: 16px;
	color: var( --color-text-subtle );
	font-size: 12px;
	text-transform: uppercase;
	font-weight: 500;
	display: flex;
	align-items: baseline;
	justify-content: space-between;

	.button {
		color: var( --color-primary );
		padding-bottom: 0;

		&:hover {
			color: var( --color-link-dark );
		}
	}
}

&.order-customer__dialog {
	.order-customer__fieldset {
		display: flex;
		flex-wrap: wrap;
		align-items: flex-start;
		justify-content: space-between;
	}

	.order-customer__field {
		flex: 0 1 calc( 50% - 10px );
		max-width: calc( 50% - 10px );
		margin-bottom: 20px;
	}

	.address-view__editable-city-state-postcode .form-select {
		max-width: none;
		width: 100%;
	}

	.order-customer__billing-fields .form-fieldset:last-child {
		margin-bottom: 0;
	}

	.address-view__editable-city-state-postcode {
		align-items: end;
	}
}
