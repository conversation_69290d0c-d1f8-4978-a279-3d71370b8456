&.order-payment__dialog {
	display: flex;
	flex-direction: column;
	padding: 0;
	flex-grow: 0;

	@include breakpoint( '>660px' ) {
		width: 640px;
	}

	.order__detail-header,
	.order__details-total {
		color: var( --color-neutral-700 );
	}

	.form-text-input,
	.form-text-input-with-affixes input {
		text-align: right;
	}

	.form-label {
		.form-text-input-with-affixes {
			font-weight: normal;
		}
	}

	.form-text-input-with-affixes,
	.form-text-input-with-suffixes {
		width: 13em;
	}

	.order-payment__item-total {
		text-align: right;
	}

	.notice {
		float: left;
	}
}

.order-payment__container {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	margin: 0 -24px -24px;
	padding: 24px;
	border-top: 1px solid var( --color-neutral-0 );
	background: var( --color-neutral-0 );

	.order-payment__note {
		margin-bottom: 16px;
		width: 100%;

		@include breakpoint( '>660px' ) {
			width: 40%;
			margin-bottom: 0;
		}

		.form-textarea {
			margin-top: 8px;
		}
	}

	.order-payment__details {
		margin-bottom: 0;

		@include breakpoint( '>660px' ) {
			width: 55%;
		}
	}

	.order-payment__amount {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		justify-content: space-between;
		padding: 14px;
		margin-bottom: 16px;
		background: var( --color-neutral-0 );
		border: 1px solid var( --color-neutral-100 );
	}

	.order-payment__amount-value {
		text-align: right;
		font-weight: bold;
	}

	.order-payment__method h3 {
		font-size: 11px;
		text-transform: uppercase;
		margin-bottom: 8px;
		color: var( --color-text-subtle );
	}
}
