.order-details__table {
	margin: 0 -24px;
	box-shadow: none;
	border-bottom: 1px solid var( --color-neutral-0 );

	.order-details__item-sku {
		display: block;
		margin-top: 6px;
		color: var( --color-text-subtle );
		font-size: 11px;
		text-transform: uppercase;
	}

	&.is-editing .order-details__item-tax {
		color: var( --color-text-subtle );
		font-style: italic;
	}

	&.hide-taxes .order-details__item-tax {
		display: none;
	}

	thead .table-heading {
		border-bottom: 1px solid var( --color-neutral-0 );
	}

	.table-row {
		&:hover {
			background: transparent;
		}
	}

	.table-item {
		vertical-align: top;
	}

	.order-details__item-pre-discount,
	.order-details__item-with-discount {
		display: block;
		background: transparent;
	}

	.order-details__item-pre-discount {
		font-size: 0.9em;
		text-decoration: line-through;
	}

	.order-details__item-quantity .form-text-input {
		width: 5em;
	}

	.order-details__item-delete {
		width: 1.5em;

		.gridicons-trash {
			color: var( --color-error );
		}
	}
}

.order-details__item-cost,
.order-details__item-quantity,
.order-details__item-tax,
.order-details__item-delete,
.order-details__item-total {
	text-align: right;
}

.order-details__actions {
	margin: 0 -24px;
	padding: 16px 80px 16px 24px;
	border-bottom: 1px solid var( --color-neutral-0 );

	.button + .button {
		margin-left: 24px;
	}

	.button {
		&.is-borderless {
			color: var( --color-primary );

			&:hover {
				color: var( --color-link-dark );
			}
		}
	}
}

// Duplicating `.table` for increased specificity
.order-details__totals.table.table {
	margin: 8px -24px;
	box-shadow: none;
	border: none;
	text-align: right;

	& + .form-setting-explanation {
		padding-right: 55px;
	}

	.table-row {
		&:hover {
			background: transparent;
		}
	}

	.table-item {
		font-size: 14px;
	}

	.order-details__coupon-list {
		.table-item {
			padding-left: 24px;
			border-bottom: 1px solid var( --color-neutral-0 );
			padding-bottom: 16px;
		}

		.table-item:not( .is-row-heading ) {
			border-bottom: 1px solid var( --color-neutral-0 );
			padding-top: 0;
			vertical-align: top;
		}

		.order-details__coupon-list-label {
			text-align: left;
		}

		.order-details__coupon-list-title {
			font-weight: 600;
			margin-bottom: -6px;
		}
	}

	.order-details__coupon-list + .order-details__total {
		.table-item {
			padding-top: 16px;
		}
	}

	.order-details__total-refund {
		color: var( --color-error );

		.table-item {
			border-bottom: 1px solid var( --color-neutral-0 );
			padding-bottom: 16px;
		}

		& + .order-details__total-remaining {
			.table-item {
				padding-top: 16px;
			}
		}
	}

	.order-details__total-full .order-details__totals-label,
	.order-details__total-full .order-details__totals-value {
		font-weight: 600;
	}

	.order-details__totals-value.table-item {
		padding-right: 24px;
		width: 10em;
	}

	&.has-taxes {
		.order-details__totals-tax,
		.order-details__totals-value {
			width: 8em;
		}
	}

	&.has-refund {
		.order-details__total-full .order-details__totals-label,
		.order-details__total-full .order-details__totals-value {
			font-weight: 400;
		}
		.order-details__total-remaining .order-details__totals-label,
		.order-details__total-remaining .order-details__totals-value {
			font-weight: 600;
		}
	}

	&.is-editing {
		.order-details__totals-value {
			// Increase width for PriceInput
			width: 13em;
			padding-right: 79px;
		}

		.form-text-input-with-affixes {
			max-width: 13em;

			.form-currency-input {
				// Fixed width fixes layout issue in Firefox
				width: 7em;
			}
		}

		.order-details__totals-tax {
			color: var( --color-text-subtle );
			font-style: italic;
		}
	}
}

.order-details__card .form-setting-explanation {
	margin: -8px 0 16px;
	text-align: right;
}

@include breakpoint( '<660px' ) {
	.order-details__table,
	.order-details__totals {
		table,
		tbody,
		thead {
			display: block;
		}

		.table-row {
			width: 100%;
			display: flex;
			justify-content: flex-end;
			flex-wrap: wrap;

			.table-heading,
			.table-item {
				flex: 1 0;
			}
		}
	}

	.order-details__table .table-row {
		.order-details__item-product {
			flex: 1 0 100%;
			padding-bottom: 0;
			border-bottom: none;
		}

		.order-details__item-cost {
			padding-left: 24px;
			text-align: left;
		}

		&.order-details__header {
			.order-details__item-product {
				display: none;
			}
		}
	}

	.order-details__totals .table-row {
		&.order-details__coupon-list {
			.table-item.order-details__coupon-list-label {
				flex: 1 0 100%;
				padding-bottom: 0;
				border-bottom: none;
			}
		}
	}
}
