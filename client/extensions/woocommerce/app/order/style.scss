
.order__container {
	padding-bottom: 0;

	* {
		box-sizing: border-box;
	}

	.order-activity-log__note-meta .gridicon {
		box-sizing: unset;
	}

	@include breakpoint( '>960px' ) {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;

		.order-details {
			flex: 1 1 100%;
		}

		.order-activity-log {
			order: 2;
			flex: 2;
			margin-right: 16px;
		}

		.order-customer {
			order: 3;
			flex: 1;
		}
	}

	@include breakpoint( '>1040px' ) {
		.order-details {
			order: 1;
			flex: 2;
			margin-right: 16px;
		}

		.order-activity-log {
			order: 3;
			flex: none;
			margin-right: 0;
			width: 100%;
		}

		.order-customer {
			order: 2;
			flex: 1;
		}
	}

	.section-header__label {
		font-size: 16px;
	}

	thead {
		.table-row:hover {
			background: transparent;
		}
	}

	.labels-setup-notice {
		width: 100%;
	}
}

.order-details__card {
	padding-top: 0;
	padding-bottom: 0;
}

.order-created,
.order-payment,
.order-fulfillment {
	margin: 0 -16px;
	padding: 16px;
	border-top: 1px solid var( --color-neutral-0 );
	display: flex;
	flex-wrap: wrap;
	align-items: center;

	@include breakpoint( '>480px' ) {
		margin: 0 -24px;
		padding: 24px;
	}

	.order-created__label,
	.order-payment__label,
	.order-fulfillment__label {
		display: flex;
		align-items: center;
		flex-grow: 1;
	}

	.order-created__label .gridicon,
	.order-fulfillment__label .gridicon {
		color: var( --color-neutral-light );
	}

	.order-payment__label .gridicon {
		color: var( --color-success );
	}

	.order-payment__action,
	.order-fulfillment__action {
		display: flex;
		justify-content: center;
		flex-grow: 0;

		.button {
			white-space: nowrap;

			& + .button {
				margin-left: 12px;
			}

			&.is-placeholder {
				@include placeholder();
				pointer-events: none;
			}
		}

		.order-fulfillment__button-group {
			.button {
				margin: 0;
			}
		}

		& > .button:last-child {
			margin-right: 0;
		}
	}

	.order-fulfillment__print-container {
		flex-wrap: wrap;
		justify-content: center;
	}

	.gridicon {
		margin-right: 8px;
	}

	@include breakpoint( '<660px' ) {
		.order-payment__label,
		.order-fulfillment__label,
		.order-payment__action,
		.order-fulfillment__action {
			width: 100%;
		}
		.order-payment__label + .order-payment__action {
			margin-top: 8px;
		}
	}
}
