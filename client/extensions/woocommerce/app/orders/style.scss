.orders__container {
	thead {
		.table-row {
			&:hover {
				background: transparent;
			}
		}
	}

	.table-row {
		&:hover {
			background: var( --color-neutral-0 );
		}

		&.orders__status-processing {
			background: var( --color-primary-0 );

			&:hover {
				background: var( --color-primary-0 );
			}
		}
	}

	.orders__row-placeholder span {
		display: inline-block;
		width: 75%;
		@include placeholder();
	}

	.search,
	.search .search__open-icon {
		z-index: initial;
	}
}

.orders__item-link,
.orders__item-name {
	display: inline-block;
	font-weight: normal;
	color: var( --color-primary );
}

.orders__item-link {
	margin-right: 5px;
}

.orders__table-total {
	text-align: right;
}

.orders__table-old-total {
	text-decoration: line-through;
	color: var( --color-text-subtle );
}
