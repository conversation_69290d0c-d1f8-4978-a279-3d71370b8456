.taxes__taxes-rates {
	.form-toggle__label-content {
		float: left;
		font-size: 14px;
		margin-right: 8px;
	}

	.taxes__taxes-calculate {
		color: var( --color-success );
		margin-bottom: 1.5em;
	}

	.taxes__taxes-calculate .gridicon {
		color: var( --color-success );
		vertical-align: top;
		margin-right: 4px;
	}

	.taxes__taxes-calculate-label {
		margin-top: 2px;
		display: inline-block;
		color: var( --color-neutral-light );
	}

	a.external-link {
		margin-left: 8px;
	}

	a.external-link .gridicons-external {
		margin-left: 4px;
	}
}

.taxes__taxes-options {
	.form-fieldset:last-child {
		margin-bottom: 0;
	}
}

.taxes__taxes-rates-table {
	.table-row {
		&:hover {
			background-color: transparent;
		}
	}
}

.taxes__placeholder .card {
	@include placeholder();
}

.taxes__tax-jar-info p {
	&:last-child {
		margin-bottom: 0;
	}
}
