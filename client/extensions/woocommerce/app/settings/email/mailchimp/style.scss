.dashboard {
	.mailchimp__getting-started-title {
		display: none;
	}

	.mailchimp__getting-started-content {
		@include breakpoint( '>960px' ) {
			padding-top: 48px;
			padding-bottom: 48px;
		}

		img {
			@include breakpoint( '>960px' ) {
				width: 390px;
			}
		}
	}

	.mailchimp__getting-started-list-header {
		font-weight: 400;
		font-size: 24px;
		line-height: 1.2em;
		margin-bottom: 16px;
	}
}

.mailchimp__getting-started-title {
	margin: 0;
}

.mailchimp__getting-started-title-text {
	font-size: 18px;
	font-weight: 500;
	padding-bottom: 4px;
}

.mailchimp__getting-started-subtitle-text {
	color: var( --color-text-subtle );
	font-size: 12px;
	line-height: 18px;
	padding-bottom: 4px;
}

.mailchimp__getting-started-content {
	width: 100%;
	align-items: center;
	display: flex;
	flex-direction: column;

	@include breakpoint( '>960px' ) {
		flex-direction: row;
	}

	img {
		width: 200px;
		margin-bottom: 16px;

		@include breakpoint( '>960px' ) {
			margin-bottom: 0;
			width: 230px;
		}
	}
}

.mailchimp__getting-started-list-header {
	font-weight: 500;
}

.mailchimp__getting-started-list {
	color: var( --color-text-subtle );
	list-style: none;
	margin: 0 0 16px;
	font-size: 14px;

	li {
		margin: 6px 0;
		padding-left: 27px;

		.gridicon {
			display: inline;
			margin-right: 8px;
			margin-left: -27px;
			vertical-align: bottom;
			color: var( --color-success );
		}
	}
}

&.dialog__content.mailchimp__setup {
	width: 500px;
	text-align: left;
	padding-top: 0;

	.wizard__progress-indicator {
		text-align: left;
		justify-content: left;
		margin-bottom: 0;
		flex-shrink: 0;
		margin-left: 16px;
	}

	.mailchimp__setup-dialog-progress {
		margin: 0 -16px 16px;
		padding: 16px;
		border-bottom: 1px solid var( --color-neutral-0 );
		display: flex;
		align-items: center;
		background: var( --color-neutral-0 );
		
		.progress-bar,
		.progress-bar__progress {
			border-radius: 0;
		}

		@include breakpoint( '>480px' ) {
			margin: 0 -24px 16px;
			padding: 16px 24px;
		}
	}
}

.mailchimp__setup-dialog-title {
	font-size: 24px;
}

.setup-steps__login-title {
	font-size: 18px;
	font-weight: 500;
	margin-bottom: 8px;
}

.setup-steps__sync-explanation {
	margin-top: 16px;
}

.setup-steps__campaign-defaults,
.setup-steps__store-info-field,
.setup-steps__mailchimp-key-input {
	.form-text-input {
		text-align: left;
	}
}

.setup-steps__store-info-field .payments__currency-container {
	padding-left: 0;
}

.setup-steps__campaign-defaults-title {
	font-size: 16px;
	font-weight: 500;
	margin-bottom: 8px;
}

.setup-steps__mailchimp-api-intro-notice {
	font-size: 16px;
	font-weight: 500;
	margin-bottom: 16px;
}

.setup-steps__mailchimp-api-directions-bold {
	font-weight: 600;
}

.setup-steps__mailchimp-api-directions {
	margin-bottom: 8px;
}

.setup-steps__mailchimp-login-button .gridicon {
	padding-left: 8px;
}

.mailchimp__getting-started-loading-placeholder {
	@include placeholder();
	pointer-events: none;
	background: var( --color-white );

	p {
		height: 14px;
		animation: loading-fade 1.6s ease-in-out infinite;
		background-color: var( --color-neutral-50 );
	}
}

.mailchimp__getting-started-list-header,
.mailchimp__getting-started-list,
.mailchimp__getting-started-button {
	@include breakpoint( '>660px' ) {
		margin-left: 24px;
	}
}

&.is-busy {
	animation: button__busy-animation 3000ms infinite linear;
	background-size: 120px 100%;
	background-image: linear-gradient( -45deg, var( --color-neutral-0 ) 28%, var( --color-white ) 28%, var( --color-white ) 72%, var( --color-neutral-0 ) 72% );
}

.mailchimp__dashboard {
	margin: 0;
	padding: 0;
}

.mailchimp__dashboard-first-section {
	display: flex;
	flex-direction: column;

	@include breakpoint( '>960px' ) {
		flex-direction: row;
		align-items: stretch;
	}
}

.mailchimp__dashboard-title {
	flex-grow: 1;
	font-size: 18px;
	font-weight: 400;
}

.mailchimp__dashboard-title-and-slogan {
	box-shadow: 0 0 0 1px rgba( var( --color-neutral-100-rgb ), 0.5 ),
		0 1px 2px var( --color-neutral-0 );
	flex-grow: 1;
	padding: 16px;
	display: flex;
	align-items: center;

	@include breakpoint( '>480px' ) {
		padding-left: 24px;
		padding-right: 24px;
	}
}

.mailchimp__dashboard-sync-status {
	box-shadow: 0 0 0 1px rgba( var( --color-neutral-100-rgb ), 0.5 ),
		0 1px 2px var( --color-neutral-0 );
	flex-grow: 0;
	width: 255px;
	font-size: 12px;
	background: var( --color-white );

	@include breakpoint( '<960px' ) {
		width: auto;
	}

	.notice {
		margin: 2px 8px 4px 16px;

		@include breakpoint( '>480px' ) {
			margin-left: 24px;
		}

		@include breakpoint( '>960px' ) {
			margin-left: 12px;
		}
	}
}

.mailchimp__sync-notice-content {
	display: flex;
	max-width: 140px;

	@include breakpoint( '<960px' ) {
		width: auto;
	}
}

.mailchimp__sync-notice-list {
	margin-right: 4px;
	text-overflow: ellipsis;
	overflow: hidden;
	flex-grow: 0;
	white-space: nowrap;
	flex-shrink: 2;
	font-weight: 600;
}

.mailchimp__sync-notice-info {
	flex-grow: 1;
	white-space: nowrap;
}

.mailchimp__sync-notice-syncing {
	animation: button__busy-animation 3000ms infinite linear;
	background-size: 120px 100%;
	background-image: linear-gradient( -45deg, var( --color-primary ) 28%, var( --color-primary-dark ) 28%, var( --color-primary-dark ) 72%, var( --color-primary ) 72% );
	border-color: var( --color-primary-200 );
}

.mailchimp__header-description {
	color: var( --color-text-subtle );
	font-size: 12px;
	line-height: 18px;
}

.mailchimp__account-data {
	font-weight: 600;
	padding: 4px 16px;
	background: var( --color-neutral-0 );
	border-top: 1px solid var( --color-neutral-0 );

	@include breakpoint( '>480px' ) {
		padding-left: 24px;
		padding-right: 24px;
	}

	@include breakpoint( '>960px' ) {
		padding-left: 12px;
		padding-right: 12px;
	}
}

.mailchimp__account-info,
.mailchimp__account-info-orders {
	color: var( --color-text-subtle );
	font-weight: 400;
}

.mailchimp__account-info-orders {
	padding-left: 8px;
}

.mailchimp__account-info-name {
	margin-bottom: 4px;
	margin: 8px 16px 0;
	font-weight: 600;

	@include breakpoint( '>480px' ) {
		margin-left: 24px;
		margin-right: 24px;
	}

	@include breakpoint( '>960px' ) {
		margin-left: 12px;
		margin-right: 12px;
	}
}

.mailchimp__sync-status {
	display: flex;
	margin-bottom: 4px;
	align-items: center;
}

.mailchimp__resync-link {
	cursor: pointer;
}

.mailchimp__dashboard-settings {
	display: flex;
	padding: 16px;
	flex-direction: column;

	@include breakpoint( '>480px' ) {
		padding: 24px;
	}

	@include breakpoint( '>960px' ) {
		flex-direction: row;
	}
}

.mailchimp__dashboard-settings-form {
	flex-grow: 1;

	.form-fieldset:last-child {
		margin-bottom: 0;
	}

	@include breakpoint( '>960px' ) {
		padding-right: 24px;
	}
}

.mailchimp__dashboard-settings-preview {
	padding: 0;
	width: 250px;
	flex-grow: 0;

	@include breakpoint( '<960px' ) {
		width: 100%;
	}
}

.mailchimp__dashboard-settings-preview-view {
	border: 1px solid var( --color-neutral-100 );
	padding: 12px;
	margin-top: 4px;
	font-size: 13px;
	color: var( --color-neutral-400 );

	.form-label {
		margin: 0;
		display: flex;
		align-items: center;

		input[type=checkbox] + span {
			margin: 0;
			margin-left: 6px;
		}
	}
}

.mailchimp__dashboard-settings-preview-heading {
	text-transform: uppercase;
	font-size: 11px;
	color: var( --color-text-subtle );
}

.mailchimp__dashboard-settings-form-checkbox {
	margin-left: 4px;
	margin-right: 16px;
}

.mailchimp__dashboard-settings-form-field {
	font-weight: 400;
}

.mailchimp__dashboard-settings-subscribe-message {
	font-weight: 600;
}
