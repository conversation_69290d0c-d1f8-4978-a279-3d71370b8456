
.email-settings__container {
	.components__notification-component-title {
		width: 50%;
	}

	.components__notification-component-title-long {
		width: 80%;
	}

	.components__notification-component-title,
	.components__notification-component-title-long {
		.form-setting-explanation {
			margin-top: 0;
		}

		.form-label {
			margin-bottom: 0;
		}
	}

	.components__notification-component-input {
		flex-shrink: 1;
		width: 40%;
	}

	.components__notification-component-toggle {
		height: 58px;
		width: 80px;
	}

	.components__notification-component-toggle-label,
	.components__notification-component-toggle {
		width: 100px;
		text-align: center;
	}

	.list-header {
		background: var( --color-neutral-0 );
	}

	.list-header + .list-item {
		border-top: 0;
	}

	.components__notification-component-item {
		flex-wrap: nowrap;
		align-items: center;
		border-top: 1px solid var( --color-border-subtle );
	}

	.components__notification-origin {
		width: 100%;
		margin-bottom: 20px;
	}

	.components__notification-origin:last-child {
		margin-bottom: 0;
	}

	.components__is-placeholder {
		@include placeholder();
		padding: 0;
		margin: 0 0 4px;
		pointer-events: none;
		background: var( --color-white );
		animation: loading-fade 1.6s ease-in-out infinite;
		background-color: var( --color-neutral-50 );
		color: transparent;
		cursor: default;
		&::placeholder {
			color: transparent;
		}
	}
}
