&.payments__dialog {
	min-width: 90%;
	@include breakpoint( '>660px' ) {
		min-width: 660px;
	}

	.dialog__content {
		.form-fieldset:last-child {
			margin-bottom: 0;
		}
	}

	.dialog__action-buttons {
		overflow: visible;

		.button {
			&.is-borderless {
				color: var( --color-primary );

				&:hover {
					color: var( --color-link-dark );
				}
			}
		}
	}

	.is-international-option {
		display: none;
	}

	&.show-international-options {
		.is-international-option {
			display: block;
		}
	}
}

.payments__edit-international-options-checkbox {
	margin-right: 8px;
}

.payments__address-currency-container {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;

	@include breakpoint( '<660px' ) {
		flex-direction: column;
	}

	.store-address.card {
		margin: 0;
		padding: 0;
		box-shadow: none;
		padding-right: 24px;
	}
}

.payments__currency-container {
	display: flex;
	flex-direction: column;
	max-width: 255px;
	padding-top: 24px;

	@include breakpoint( '>960px' ) {
		padding-top: 0;
	}
}

.payments__type-container .section-header__label::before {
	display: none;
}

// PAYMENT-METHOD-HEADER

.payments__type-container .list-header {
	justify-content: flex-start;
	background: var( --color-neutral-0 );
	font-weight: 600;
	font-size: 14px;

	.list-item-field {
		padding-top: 12px;
		padding-bottom: 12px;
	}
}

// PAYMENT-METHOD-ROW
.payments__type-container {
	p.payments__method-suggested {
		font-size: 11px;
		text-transform: uppercase;
		color: var( --color-text-subtle );
		display: none;
	}

	p.payments__method-suggested,
	p.payments__method-name,
	p.payments__method-information {
		margin: 0;
		padding: 0;
	}

	p.payments__method-information p.payments__method-name {
		font-size: 14px;
	}

	.payments__method-name-paypal {
		text-indent: -999em;
		height: 24px;
		width: 90px;
		background: url( '/calypso/images/extensions/woocommerce/paypal.svg' );
		background-repeat: no-repeat;
		background-size: contain;
	}

	.list-header + .list-item {
		border-top: 0;
	}

	.list-item {
		border-top: 1px solid var( --color-border-subtle );
		align-items: center;
	}

	.payments__method-information {
		font-size: 14px;
	}

	.payments__method-method-suggested-container,
	.list-item-field.payments__methods-column-method {
		width: 30%;
	}

	.payments__method-method-suggested-container {
		@include breakpoint( '<660px' ) {
			width: 50%;
		}
	}

	.payments__method-method-information-container,
	.list-item-field.payments__methods-column-fees {
		width: 35%;
	}

	.list-item-field.payments__methods-column-fees {
		@include breakpoint( '<660px' ) {
			display: none;
		}
	}

	.payments__method-method-information-container {
		@include breakpoint( '<660px' ) {
			width: 60%;
			order: 4;
			padding-top: 0;
		}
	}

	.payments__method-enable-container {
		width: 20%;

		@include breakpoint( '<660px' ) {
			width: 30%;
			padding-left: 0;
			padding-right: 0;
		}

		.payments__method-enable {
			margin-bottom: 0;
		}

		.payments__method-enable .form-label {
			display: inline-block;
			color: var( --color-neutral-light );
			font-size: 11px;
			font-weight: 400;
			line-height: 16px;
			margin-top: 6px;
			margin-right: 8px;
			vertical-align: top;
			text-transform: uppercase;
			cursor: pointer;
		}
	}

	.payments__method-action-container {
		width: 15%;

		@include breakpoint( '<660px' ) {
			width: 20%;
			padding-left: 0;
		}

		.button {
			align-self: flex-end;
			white-space: nowrap;
		}
	}
}

.payments__method-edit-header {
	font-size: 18px;
	justify-content: space-between;
	padding: 0 0 16px;
}

.payments__method-edit-fields {
	padding: 16px;
	border-top: 1px solid var( --color-border-subtle );
	width: 100%;

	@include breakpoint( '>480px' ) {
		padding: 24px;
	}

	.segmented-control {
		max-width: 300px;
	}
}

.payments__method-loading-suggested,
.payments__method-loading-title,
.payments__method-loading-fee,
.payments__method-loading-feelink,
.payments__method-loading-enabled,
.payments__method-loading-settings {
	background-color: var( --color-neutral-0 );
	animation: loading-fade 1.6s ease-in-out infinite;
}

.payments__method-loading-suggested {
	height: 15px;
	margin-bottom: 5px;
	width: 100%;

	@include breakpoint( '<660px' ) {
		width: 60%;
	}
}

.payments__method-loading-title {
	height: 12px;
	width: 90%;

	@include breakpoint( '<660px' ) {
		width: 50%;
	}
}

.payments__method-loading-fee {
	height: 15px;
	margin-bottom: 5px;
	width: 100%;

	@include breakpoint( '<660px' ) {
		width: 60%;
	}
}

.payments__method-loading-feelink {
	height: 12px;
	width: 90%;

	@include breakpoint( '<660px' ) {
		width: 50%;
	}
}

.payments__method-loading-enabled {
	align-self: baseline;
	height: 17px;
	width: 82px;
}

.payments__method-loading-settings {
	align-self: flex-end;
	height: 26px;
	width: 54px;
}
