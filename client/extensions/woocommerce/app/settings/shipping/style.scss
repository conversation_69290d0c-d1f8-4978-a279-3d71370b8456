
@import 'shipping-zone/style';
@import 'shipping-zone/style';

.shipping__zones-header {
	font-weight: 600;
}

.shipping__credit-card-description {
	margin-bottom: 4px;
	color: var( --color-text-subtle );
	padding-bottom: 8px;
	font-size: 14px;
	line-height: 18px;
}

.shipping__origin-settings {
	.store-address {
		margin: 0 0 16px;
		padding: 0;
		box-shadow: none;
		flex-shrink: 1;
		padding-right: 24px;
	}

	@include breakpoint( '>960px' ) {
		display: flex;
	}
}

.shipping__units {
	display: flex;
	flex-grow: 1;
	flex-direction: row;

	@include breakpoint( '<1280px' ) {
		flex-direction: column;
	}

	.shipping__weight-select {
		margin-right: 16px;
		margin-bottom: 12px;
	}
}

.shipping__address {
	margin-bottom: 24px;

	p {
		margin-bottom: 0;

		&.shipping__address-name {
			font-weight: bold;
		}
	}
}

.shipping__zones-notice {
	margin-bottom: 0;
}

.shipping__zones {
	padding: 0;
}

.shipping__zones-row {
	display: flex;
	flex-direction: row;
	padding: 16px 0;
	align-items: center;

	&:not( :last-child ) {
		border-bottom: 1px solid var( --color-neutral-100 );
	}
}

.shipping__zones-row {
	&.is-placeholder {
		@include placeholder();
		pointer-events: none;
		background: var( --color-white );

		.gridicon,
		p,
		a,
		button {
			animation: loading-fade 1.6s ease-in-out infinite;
			background-color: var( --color-neutral-50 );
			color: transparent;
			cursor: default;
		}

		p {
			width: 100px;
			height: 14px;
		}

		.gridicon {
			fill: transparent;
			stroke: transparent;
		}
	}

	&:last-child {
		background: var( --color-neutral-0 );
	}
}

.shipping__zones-header {
	padding: 12px 0;
	font-size: 14px;
	border-top: 0;
	background: var( --color-neutral-0 );
}

.shipping__zones-row-icon {
	width: 48px;
	text-align: left;
	padding-left: 16px;

	@include breakpoint( '>480px' ) {
		padding-left: 24px;
	}

	svg {
		margin-top: 6px;
		width: 24px;
	}
}

.shipping__zones-row-location {
	width: 25%;
}

.shipping__zones-row-location-name {
	margin-bottom: 0;
	font-size: 14px;
}

.shipping__zones-row-location-name {
	padding-right: 12px;
}

.shipping__zones-row-method {
	font-size: 14px;
	display: flex;
	flex-direction: column;

	@include breakpoint( '>960px' ) {
		flex-direction: row;
	}
}

.shipping__zones-row-location-description,
.shipping__zones-row-method-description,
.shipping__card-name {
	margin-bottom: 0;
	color: var( --color-text-subtle );
	font-size: 13px;
	line-height: 22px;
}

.shipping__zones-row-method-description {
	@include breakpoint( '>960px' ) {
		margin-left: 4px;
	}
}

.shipping__zones-row-methods {
	width: 45%;
}

.shipping__zones-row-method:not( :last-child ) {
	margin-bottom: 8px;
}

.shipping__zones-row-method-name {
	margin-bottom: 0;
}

.shipping__zones-row-actions {
	width: 20%;
	text-align: right;
	padding-right: 16px;

	@include breakpoint( '>480px' ) {
		padding-right: 24px;
	}
}
