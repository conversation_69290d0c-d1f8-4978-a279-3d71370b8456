.shipping-zone__name {
	display: flex;
	flex-direction: row;
	align-items: center;

	&.is-placeholder {
		.gridicon, button, span {
			@include placeholder();
			color: transparent;
			cursor: default;
		}

		span {
			flex-grow: 1;
			height: 40px;
		}

		pointer-events: none;
	}
}

.shipping-zone__loading-spinner {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 16px;
}

.shipping-zone__locations-container .list-item,
.shipping-zone__methods-container .list-item {
	border-top: 1px solid var( --color-border-subtle );
}

.shipping-zone__locations-container .list-header + .list-item,
.shipping-zone__methods-container .list-header + .list-item {
	border-top: 0;
}

.shipping-zone__locations-container .list-item-field,
.shipping-zone__methods-container .list-item-field {
	flex-direction: row;
	align-items: baseline;
}

.shipping-zone__methods-container,
.shipping-zone__locations-container {
	.list-header,
	.list-item {
		justify-content: flex-start;
	}

	.list-header {
		background: var( --color-neutral-0 );
		font-weight: 600;
		font-size: 14px;

		.list-item-field {
			padding-top: 12px;
			padding-bottom: 12px;
		}

		.shipping-zone__methods-column-title {
			width: 50%;

			@include breakpoint( '>660px' ) {
				width: 30%;
			}
		}

		.shipping-zone__methods-column-summary {
			display: none;

			@include breakpoint( '>660px' ) {
				width: 30%;
				display: block;
			}
		}
	}

	.list-item.is-placeholder {
		@include placeholder();
		pointer-events: none;
		background: var( --color-white );

		.shipping-zone__placeholder-flag, .gridicon, p, a, span, button {
			animation: loading-fade 1.6s ease-in-out infinite;
			background-color: var( --color-neutral-50 );
			color: transparent;
			cursor: default;
		}

		.shipping-zone__placeholder-flag {
			margin-right: 4px;
			width: 26px;
			height: 20px;
			border-radius: 4px;
		}

		p, span {
			width: 100px;
			height: 14px;
			margin-right: 10px;
		}

		.gridicon {
			fill: transparent;
			stroke: transparent;
		}

		&::after {
			display: none;
		}
	}

	.list-item-field {
		justify-content: left;
		align-items: center;
	}

	.shipping-zone__method-title {
		font-size: 14px;
		width: 50%;

		@include breakpoint( '>660px' ) {
			width: 30%;
		}
	}

	.shipping-zone__method-summary {
		font-size: 14px;
		width: 30%;

		@include breakpoint( '<660px' ) {
			width: 100%;
			order: 4;
			padding-top: 0;
		}
	}

	.shipping-zone__enable-container {
		width: 30%;
		color: var( --color-neutral-light );
		font-size: 11px;
		font-weight: 400;
		line-height: 16px;
		vertical-align: top;
		text-transform: uppercase;

		@include breakpoint( '>660px' ) {
			width: 25%;
		}

		span {
			cursor: pointer;
		}

		.form-toggle__switch {
			margin-left: 4px;
			margin-top: -3px;
		}

		.form-toggle__label-content {
			margin-left: 8px;
		}
	}

	.shipping-zone__method-actions {
		width: 20%;
		justify-content: flex-end;

		.button {
			flex-shrink: 0;
		}

		@include breakpoint( '>660px' ) {
			width: 15%;
		}
	}

	.shipping-zone__location-title {
		font-size: 14px;
		width: 100%;

		@include breakpoint( '>660px' ) {
			width: 30%;
		}
	}

	.shipping-zone__location-summary {
		font-size: 14px;
		width: 70%;

		@include breakpoint( '<660px' ) {
			width: 100%;
			order: 4;
			padding-top: 0;
		}
	}
}

&.shipping-zone__method-dialog {
	@include breakpoint( '>660px' ) {
		width: 660px;
	}

	.dialog__content:last-child {
		margin-bottom: 0;
	}

	.dialog__content {
		display: flex;
		flex-direction: column;
		padding: 24px 0;

		> div {
			padding-left: 24px;
			padding-right: 24px;
		}
	}
}

.shipping-zone__method-delete {
	float: left;
	margin-left: 0;
}

.shipping-zone__method-type-select {
	width: 100%;
}

.shipping-zone__method-container {
	position: relative;
	padding: 20px;
	border-bottom: 1px solid var( --color-neutral-100 );
	margin-bottom: 24px;

	.form-text-input-with-affixes {
		width: 200px;
	}
}

.shipping-methods__free-shipping-option {
	display: flex;
	align-items: center;

	.form-text-input-with-affixes {
		width: 100px;
		margin: 0 8px;
	}

	.form-text-input-with-affixes__prefix,
	.form-currency-input {
		padding: 4px 8px;
	}

	input:disabled {
		border-color: var( --color-neutral-100 );
	}
}

.shipping-methods__checkbox {
	margin-right: 7px;
	margin-top: 4px;
}

.shipping-zone__method-dialog-header {
	padding-bottom: 16px;
	font-size: 18px;
	display: flex;
	justify-content: space-between;
	flex-shrink: 0;

	.form-fieldset {
		margin-bottom: 0;
	}
}

.shipping-zone__method-dialog-type-field {
	flex-shrink: 0;
}

.shipping-zone__method-dialog-content {
	overflow-y: auto;
	// 1px padding to show the box-shadow border
	padding-top: 1px;
	padding-bottom: 1px;
}

.shipping-zone__enable {
	transform: translateY( 6px );

	span {
		margin-left: 6px;
		color: var( --color-neutral-light );
		font-size: 11px;
		line-height: 16px;
		vertical-align: top;
		text-transform: uppercase;
		font-weight: normal;
		cursor: pointer;
	}
}

&.shipping-zone__location-dialog {
	@include breakpoint( '>660px' ) {
		width: 660px;
	}

	.dialog__action-buttons {
		overflow: visible;
	}

	.form-label,
	.form-fieldset,
	.shipping-zone__location-dialog-header {
		color: var( --color-neutral-700 );
	}
}

.shipping-zone__location-dialog-header {
	padding: 0 0 24px;
	font-size: 18px;
}

.shipping-zone__location-dialog-list-item {
	list-style: none;
	padding: 3px 6px;
	margin: 1px 0;

	label {
		display: block;
		cursor: pointer;
	}

	small {
		margin-left: 4px;
	}

	&.is-disabled {
		color: var( --color-text-subtle );

		img {
			filter: grayscale( 1 );
			opacity: 0.5;
		}

		&:hover {
			background: transparent;
		}
	}

	.location-flag {
		margin-bottom: 1px;
	}
}

.shipping-zone__location-dialog-list-item:hover {
	background: var( --color-neutral-0 );
}

.shipping-zone__location-dialog-list-item-checkbox {
	margin-right: 8px;
	margin-top: 3px;

	& + span {
		display: inline;
		margin: 0;
	}

	&.is-country {
		margin-left: 24px;
	}
}

.shipping-zone__location-dialog-settings {
	label {
		display: block;
	}
}
