.dashboard__placeholder .card {
	@include placeholder();
}

.dashboard__placeholder-large button,
.dashboard__placeholder-small button {
	display: none;
}

.dashboard__placeholder-small {
	height: 250px;
}

.dashboard__placeholder-large {
	margin-bottom: 16px;
	height: 200px;
}

.dashboard__manage-has-orders-header {
	margin-bottom: 24px;
	text-align: center;

	h2 {
		font-size: 24px;

		span {
			display: block;
		}
	}
}

.dashboard__manage-has-orders-stats-actions {
	padding: 24px;
	text-align: center;
}

.dashboard__manage-has-orders {
	h2 {
		font-size: 24px;
		line-height: 1.2em;
		margin-bottom: 16px;
	}

	.dashboard__reports-widget .dashboard-widget__image {
		margin-right: 10%;
		width: calc( 35% - 8px ) !important;
		margin-bottom: 16px;

		@include breakpoint( '>660px' ) {
			margin-bottom: 0;
		}

		@include breakpoint( '<660px' ) {
			margin-right: auto;
			margin-left: auto;
			width: 80% !important;
		}

		& + .dashboard-widget__children {
			@include breakpoint( '>660px' ) {
				width: calc( 65% - 34px ) !important;
			}
		}
	}

	.dashboard-widget__row {
		@include breakpoint( '<960px' ) {
			flex-direction: column;
		}
	}

	.dashboard-widget__row.dashboard__process-orders-container {
		margin-bottom: 10px;
		min-width: 100%;
		background: var( --color-white );
		box-shadow: 0 0 0 1px rgba( var( --color-neutral-100-rgb ), 0.5 ),
			0 1px 2px var( --color-neutral-0 );
		flex-direction: row;

		@include breakpoint( '<480px' ) {
			flex-direction: column;
		}

		@include breakpoint( '>480px' ) {
			margin-bottom: 16px;
		}

		&.has-reviews {
			min-width: calc( 75% - 16px );
		}

		.dashboard-widget {
			text-align: center;
			box-shadow: none;
			background-color: transparent;
			margin: 0;

			.dashboard__process-orders-value {
				display: block;
				font-size: 24px;
			}

			&.dashboard__process-orders-revenue .dashboard__process-orders-value {
				color: var( --color-success );
			}

			.dashboard__process-orders-label {
				color: var( --color-text-subtle );
			}
		}

		.dashboard__process-orders-action {
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	.dashboard-widget.dashboard__reviews-widget {
		display: flex;
		justify-content: space-between;
		align-items: center;
		min-width: 25%;
		width: 100%;
		text-align: center;

		@include breakpoint( '>960px' ) {
			margin-left: 16px;
		}

		.dashboard-widget__title {
			margin-bottom: 12px;
		}

		.dashboard-widget__content {
			width: 100%;
		}

		.dashboard-widget__children {
			@include breakpoint( '<960px' ) {
				display: flex;
				align-items: stretch;

				.dashboard-widget__title,
				.button {
					flex: 1 0 50%;
					margin: auto;
				}
			}
		}
	}
}
