.stats-widget {

	.select-dropdown {
		display: inline-flex;
		flex-direction: row;
		margin-left: 8px;
	}

	.select-dropdown__options {
		text-align: left;
	}

	.card {
		padding: 0;
	}

	.dashboard-widget__title, .stats-widget__footer {
		padding: 16px;
		@include breakpoint( '>480px' ) {
			padding: 24px;
		}
	}

	.dashboard-widget__title {
		border-bottom: 1px solid var( --color-neutral-0 );
		white-space: nowrap;

		p {
			margin-top: 16px;
		}
	}

	.stats-widget__footer {
		display: flex;
		justify-content: space-between;
		align-items: center;

		@include breakpoint( '<1040px' ) {
			flex-direction: column;

			a {
				margin-top: 8px;
			}
		}

		span {
			color: var( --color-text-subtle );
			font-size: 14px;
		}
	}

	.stats-widget__boxes {
		display: flex;
		flex-wrap: wrap;
		margin-top: -8px;
	}

	.stats-widget__box-label, .table-heading .table-item__cell-title, .table.is-compact-table .table-heading {
		font-size: 16px;
		font-weight: 600;
		white-space: nowrap;
	}

	.stats-widget__box-label-tooltip {
		cursor: help;
	}

	.table.is-compact-table .table-item {
		font-size: 14px;
		color: var( --color-neutral-500 );
	}

	.table.is-compact-table .table-heading:nth-child( 3 ), .table.is-compact-table .table-item:nth-child( 3 ) {
		padding-left: 32px;

		@include breakpoint( '<960px' ) {
			padding-left: 48px;
		}
	}

	.stats-widget__box-contents {
		max-width: 50%;
		min-width: 50%;
		box-sizing: border-box;
		border-bottom: 1px solid var( --color-neutral-0 );
		padding: 32px;
		text-align: left;

		@include breakpoint( '<480px' ) {
			padding: 16px;
		}

		&.stats-type-stat {
			display: flex;
			height: 120px;

			@include breakpoint( '<960px' ) {
				flex-direction: column;
				height: 140px;
			}

			.stats-module__placeholder {
				margin: 0 auto;
				margin-top: -40px;
			}
		}

		&.stats-type-list {
			height: 200px;

			@include breakpoint( '<480px' ) {
				height: 175px;
			}

			@include breakpoint( '<960px' ) {
				max-width: 100%;
				min-width: 100%;

				.is-error {
					text-align: left;
				}
			}
		}


		.stats-widget__box-data, .stats-widget__box-sparkline {
			width: 50%;
		}

		.stats-widget__box-value {
			color: var( --color-neutral-500 );
			font-size: rem( 20px );
			font-weight: 400;
		}

		.delta {
			margin: 0x;
			margin-left: 4px;
		}

		.store-stats-module, .stats-widget__more {
			margin-left: -16px;
		}

		.stats-widget__more {
			@include breakpoint( '<480px' ) {
				margin-top: -8px;
			}
		}
	}

	.stats-widget__box-value-and-delta {
		display: flex;
	}

	.stats-widget__box-contents:nth-child( odd ) {
		border-right: 1px solid var( --color-neutral-0 );
	}

	.store-stats-module .card {
		box-shadow: none;
		.table-row {
			&:hover {
				background: inherit;
				.table-item__cell-title::after {
					background: none;
				}
			}
		}
	}

	.stats-widget__more {
		text-align: left;
		padding: 0 8px 0 16px;
		font-size: 14px;
		margin-top: -14px;
	}

}
