.product-categories__list-placeholder {
	@include placeholder();
	background: var( --color-white );
	height: 525px;
}

.product-categories__list-container {
	border: 1px solid var( --color-neutral-0 );
	border-bottom: 0;
}

.product-categories__list-item {
	background-color: var( --color-neutral-0 );
}

.product-categories__list-item-card.card.is-compact {
	padding: 0;
}

.accessible-focus & .product-categories__list-item-card:focus {
	border-left: 3px solid var( --color-primary );
}

.product-categories__list-item-wrapper {
	display: flex;
	flex-direction: row;

	.product-categories__list-item-info {
		line-height: 69px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		flex-grow: 1;
		position: relative;
		padding-right: 16px;
	}

	.product-categories__list-item-description {
		margin-left: 24px;
		color: var( --color-text );
	}

	.image-thumb {
		margin: 12px 16px 0;
	}

	.count {
		margin: 18px 0 0 16px;
	}

	&:hover {
		background: var( --color-neutral-0 );
	}
}

.product-categories__list-nested {
	margin-left: 2em;
}

.product-categories__form {
	&.is-placeholder div {
		@include placeholder();
		background: var( --color-white );
		margin-bottom: 8px;

		&:first-child {
			height: 575px;
		}
	}
}

.product-categories__form-info-fields {
	display: flex;
	flex-direction: column;
	@include breakpoint( '>960px' ) {
		flex-direction: row;
	}
}

.product-categories__form-name-description {
	flex: 2;
}

.product_categories__list-wrapper {
	.search,
	.search__open-icon,
	.search__close-icon {
		z-index: initial;
	}
}

.product-categories__form-image-wrapper {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;

	width: 100%;
	margin-bottom: 16px;

	.product-image-uploader__wrapper {
		@include breakpoint( '>960px' ) {
			width: 175px;
			height: 175px;
		}
		@include breakpoint( '>1040px' ) {
			width: 225px;
			height: 225px;
		}
	}

	@include breakpoint( '>960px' ) {
		margin-bottom: 0;
		width: 175px;
		margin-right: 24px;
	}

	@include breakpoint( '>1040px' ) {
		width: 225px;
	}

	.product-categories__form-image {
		position: relative;
		display: inline-block;
		margin-bottom: 16px;
		margin-right: 0;
		margin-left: 0;
		clear: both;
		width: 100%;
		cursor: pointer;

		figure {
			border: 1px solid var( --color-neutral-0 );
		}

		img {
			display: block;
			width: 100%;
			height: auto;
		}
	}

	figure {
		position: relative;
		overflow: hidden;
	}

	.product-categories__form-image.preview figure::after {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
	}

	.product-categories__form-image.preview img {
		opacity: 0.7;
	}

	.spinner {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate( -50%, -50% );
	}

	.product-categories__form-image-remove {
		position: absolute;
		top: 0;
		right: 0;
		width: 28px;
		height: 28px;
		transform: translate( 25%, -25% );
		border: 1px solid var( --color-neutral-200 );
		border-radius: 50%;
		background-color: var( --color-neutral-0 );
		cursor: pointer;

		&:hover {
			background: var( --color-neutral-0 );
		}
	}

	.product-categories__form-image-remove-icon.gridicon {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate( -50%, -50% );
		width: 24px;
		height: 24px;
		margin: 0;
		color: var( --color-neutral-200 );

		&:hover {
			color: var( --color-neutral-light );
		}
	}
}
