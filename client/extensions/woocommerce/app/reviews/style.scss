.reviews__placeholder {
	display: inline-block;
	width: 100%;
	padding: 12px 0;
	@include placeholder();
}

.reviews__filter-nav {
	.section-nav {
		margin-top: 8px;
	}

	.search,
	.search .search__open-icon {
		z-index: initial;
	}
}

.reviews__list {
	font-size: 14px;
	.pagination {
		margin-top: 16px;
	}
}

.reviews__card {
	margin: 0 auto;
	padding: 0;

	&.is-unapproved.is-collapsed {
		.reviews__header {
			box-shadow: inset 4px 0 0 0 var( --color-warning );
		}
	}

	.reviews__product-name {
		padding: 16px;
		border-bottom: 1px solid var( --color-neutral-0 );
	}

	&.is-expanded {
		margin: 16px auto;

		.reviews__header {
			border-bottom: 1px solid var( --color-neutral-0 );
		}

		.reviews__content {
			width: auto;
			margin-left: 16px;
			margin-bottom: 0;
			padding-bottom: 16px;
			margin-top: 0;

			p:last-child {
				margin-bottom: 0;
			}
		}
	}

	&.is-collapsed {
		.reviews__content {
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
	}

	.reviews__header-content {
		width: calc( 100% - 56px );
		display: flex;
		align-items: center;
		flex-flow: row wrap;

		@include breakpoint( '>660px' ) {
			flex-flow: initial;
		}
	}

	.reviews__header {
		display: flex;
		align-items: center;

		.reviews__action-collapse .gridicon {
			fill: var( --color-neutral-light );
			transform: rotate( 180deg );
			transition: transform 0.15s cubic-bezier( 0.175, 0.885, 0.32, 1.275 ), color 0.2s ease-in;
		}

		.reviews__action-collapse:hover .gridicon {
			fill: var( --color-primary );
		}

		&.is-preview {
			&:hover .reviews__action-collapse .gridicon {
				fill: var( --color-primary );
			}
			.reviews__action-collapse {
				flex: none;
				margin-top: -6px;
				text-align: center;
				width: 36px;
				margin-right: 12px;
				margin-left: 8px;

				.gridicon {
					transform: rotate( 0deg );
				}
			}
		}

		&:not( .is-preview ) .button.is-borderless {
			padding: 10px 8px 14px;

			&.reviews__action-collapse {
				flex-grow: 1;
				padding-right: 12px;
				margin-right: 4px;
				text-align: right;
			}
		}
	}

	.reviews__author-name {
		font-weight: 600;

		.gridicon {
			transform: translateX( 4px ) translateY( 4px );
		}
	}

	.reviews__date {
		color: var( --color-text-subtle );
		font-size: 13px;
	}

	.reviews__rating {
		height: 18px;
	}

	.reviews__author-gravatar,
	.reviews__info,
	.reviews__content,
	.reviews__rating,
	.reviews__product {
		margin: 12px 8px;
	}

	.reviews__author-gravatar {
		display: flex;
		align-items: center;
		margin-left: 16px;
		margin-right: 0;
		width: 32px;
		flex-shrink: 0;
		display: none;

		img {
			display: block;
		}

		@include breakpoint( '>660px' ) {
			display: block;
		}
	}

	.reviews__info {
		line-height: 1.4;
		margin-left: 16px;
		width: calc( 100% - 157px );

		@include breakpoint( '>660px' ) {
			width: 150px;
			margin-left: 8px;
		}

		.gridicon {
			color: var( --color-success );
		}
	}

	.reviews__content {
		width: 100%;
		order: 6;
		margin-left: 16px;
		margin-right: 16px;
		margin-top: 0;

		@include breakpoint( '>660px' ) {
			order: 0;
			width: calc( 100% - 304px );
			margin-left: 8px;
			margin-right: 8px;
			margin-top: 12px;
		}
	}

	.reviews__product {
		height: 18px;
		width: 18px;
		overflow: hidden;
		position: relative;
		flex-shrink: 0;
		margin-right: 0;

		@include breakpoint( '>660px' ) {
			height: 32px;
			width: 32px;
			margin-right: 8px;
		}

		&.is-placeholder {
			background: var( --color-neutral-0 );
		}

		img {
			position: absolute;
			left: 50%;
			top: 50%;
			height: 100%;
			width: auto;
			-webkit-transform: translate( -50%, -50% );
			transform: translate( -50%, -50% );
		}
	}

	.reviews__verified-label {
		color: var( --color-success );
		display: inline-block;
		margin-left: 6px;
		font-weight: normal;

		span {
			margin-left: 6px;
		}
	}

	.reviews__expanded-card-details {
		display: flex;
		flex-flow: row nowrap;
		padding: 0 8px 0 0;
		align-items: center;

		.reviews__rating {
			margin-left: auto;
		}

		.reviews__info {
			width: auto;
		}
	}
}

.reviews__actions {
	color: var( --color-neutral-light );
	padding-left: 4px;
	user-select: none;

	.button.is-borderless {
		span {
			display: none;
			font-weight: normal;
			margin-left: 4px;

			@include breakpoint( '>960px' ) {
				display: inline;
			}
		}

		&.reviews__action-approve {
			&:focus,
			&:hover {
				color: var( --color-success );
			}
		}

		&.reviews__action-spam,
		&.reviews__action-trash,
		&.reviews__action-delete {
			&:focus,
			&:hover {
				color: var( --color-error );
			}
		}

		&.is-approved {
			color: var( --color-success );
		}
		&.is-spam {
			color: var( --color-error );
		}
		&.is-trash {
			color: var( --color-neutral-700 );
		}
	}
}

.reviews__expanded-card-details-wrap {
	border-left: 4px solid var( --color-neutral-0 );
	margin-left: 16px;
}

.reviews__reply {
	border-top: 1px solid var( --color-neutral-0 );
	border-left: 4px solid var( --color-neutral-0 );
	margin-left: 16px;
	padding-bottom: 16px;
	background: var( --color-neutral-0 );

	.reviews__reply-content {
		padding: 0 16px;

		p:last-child {
			margin-bottom: 0;
		}
	}
}

.reviews__reply-textarea {
	line-height: 0;
	overflow: hidden;
	position: relative;
	border-top: 1px solid var( --color-neutral-0 );

	textarea {
		font-size: 14px;
		border-color: transparent;
		margin: 2px;
		width: calc( 100% - 4px );
		padding: 10px 14px;
		min-height: 68px;

		&:not( :focus ) {
			border-color: var( --color-white );
			padding-left: 48px;
			padding-right: 16px;
			resize: none;

			&.has-content {
				padding-right: 70px;
			}
		}

		&:focus,
		&.has-focus {
			border-color: var( --color-primary );
		}

		&.has-scrollbar {
			overflow-y: auto;
		}
	}

	.gravatar {
		position: absolute;
		left: 16px;
		top: 12px;
		transition: transform 0.2s ease-in-out;

		&:not( .is-visible ) {
			transform: translate3d( -40px, 0, 0 );
		}
	}

	.reviews__reply-submit {
		color: var( --color-neutral-light );
		font-size: 12px;
		font-weight: 600;
		position: absolute;
		right: -70px;
		top: 1px;
		text-transform: uppercase;
		transition: transform 0.2s ease-in-out;
		padding: 10px;

		&.is-active {
			color: var( --color-primary );
			cursor: pointer;
		}

		&.is-visible {
			transform: translate3d( -78px, 0, 0 );
		}
		&.is-visible.has-scrollbar {
			transform: translate3d( -84px, 0, 0 );
		}

		.accessible-focus &:focus {
			border-color: var( --color-primary );
			box-shadow: 0 0 0 2px var( --color-primary-100 );
		}
	}
}

.reviews__reply-edit {
	border-top: 1px solid var( --color-neutral-0 );
	padding: 8px;

	.reviews__reply-edit-buttons {
		text-align: right;
		padding: 8px;

		.button {
			margin-left: 8px;
		}
	}

	textarea {
		resize: vertical;
	}
}

.reviews__reply-bar {
	display: flex;
	flex-flow: row nowrap;
	align-items: center;

	.reviews__reply-actions {
		margin-left: auto;
		margin-right: 16px;

		.gridicon {
			margin-right: 4px;
		}

		.button {
			margin-left: 16px;
			font-size: 14px;
			font-weight: 400;
		}
	}
}
