.subscriptions-usage__list {
	padding: 0;

	.subscriptions-usage__list-item {
		display: flex;
		align-items: center;
		padding: 8px 0;

		.subscriptions-usage__list-item-carrier-icon {
			width: 48px;
			padding-left: 16px;

			@include breakpoint( '>480px' ) {
				padding-left: 24px;
			}
		}

		.subscriptions-usage__list-item-name {
			width: 35%;
		}

		.subscriptions-usage__list-item-usage {
			display: flex;
			flex-flow: column;
			justify-content: center;
			text-align: center;
			width: 30%;

			@include breakpoint( '>480px' ) {
				flex-flow: row;
				justify-content: flex-start;
				text-align: left;
			}

			& > .subscriptions-usage__list-item-usage-icon {
				display: none;
			}

			&.is-over-limit {
				& > .subscriptions-usage__list-item-usage-numbers {
					color: var( --color-red-light );
					font-weight: 600;
				}

				& > .subscriptions-usage__list-item-usage-icon {
					display: block;
					margin-top: 5px;

					@include breakpoint( '>480px' ) {
						margin-left: 5px;
						margin-top: 0;
					}
				}

				.components-popover__content {
					white-space: normal;
					width: 200px;
				}
			}
		}

		.subscriptions-usage__list-item-actions {
			width: 25%;
			text-align: right;
			padding-right: 16px;

			@include breakpoint( '>480px' ) {
				padding-right: 24px;
			}
		}
	}
}

.subscriptions-usage__header {
	display: flex;
	flex-direction: row;
	align-items: center;
	font-weight: 600;
	padding: 12px 0;
	font-size: 14px;
	border-top: 0;
	border-bottom: 1px solid var( --color-neutral-0 );
	border-bottom-color: var( --color-neutral-100 );
	background: var( --color-neutral-0 );

	.subscriptions-usage__header-icon {
		width: 72px;
	}
	.subscriptions-usage__header-name {
		width: 35%;
	}
}
