/** @format */

/**
 * External dependencies
 */

import React from 'react';
import PropTypes from 'prop-types';
import { localize } from 'i18n-calypso';
import Gridicon from 'gridicons';
import { compose } from 'redux';
import { connect } from 'react-redux';
import classNames from 'classnames';
import { Button } from '@wordpress/components';

/**
 * Internal dependencies
 */
import CarrierIcon from '../../components/carrier-icon';
import Dialog from 'components/dialog';
import * as api from 'woocommerce/woocommerce-services/api';
import { errorNotice as errorNoticeAction, successNotice as successNoticeAction } from 'state/notices/actions'
import { getSelectedSiteId } from 'state/ui/selectors';

const CarrierAccountListItem = ( props ) => {
	const { accountData, translate, errorNotice, successNotice, siteId } = props;

	const [isDisconnectDialogVisible, setIsDisconnectDialogVisible] = React.useState(false);
	const [isSaving, setIsSaving] = React.useState(false);
	const [carrierId, setCarrierId] = React.useState(accountData.id);

	const handleShowDisconnectDialogConfirmation = React.useCallback(() => {
		setIsDisconnectDialogVisible(true);
	}, [setIsDisconnectDialogVisible]);

	const handleDisconnectDialogCancel = React.useCallback(() => {
		setIsDisconnectDialogVisible(false);
	}, [setIsDisconnectDialogVisible]);

	const handleDisconnectConfirmation = React.useCallback(() => {
		const submitDeletion = async () => {
			setIsSaving(true);

			try {
				await api.del( siteId, api.url.shippingCarrierDelete( carrierId ) )
				setIsDisconnectDialogVisible(false);
				successNotice( translate( 'Your carrier account was disconnected succesfully.' ) );
				setCarrierId(null);
			} catch (err) {
				errorNotice( translate( 'There was an error trying to disconnect your carrier account' ) );
			}

			setIsSaving(false);
		};

		submitDeletion();
	}, [setIsDisconnectDialogVisible, errorNotice, successNotice, setIsSaving, siteId, setCarrierId, carrierId]);

	const buttonClasses = classNames( 'button','is-compact');

	const disconnectDialogButtons = React.useMemo(() => {
		return [
			<Button
				disabled = { isSaving }
				className = { buttonClasses }
				onClick={ handleDisconnectDialogCancel }
			>
				{ translate( 'Cancel' ) }
			</Button>,
			<Button
				isPrimary
				disabled = { isSaving }
				isBusy = { isSaving }
				className = { classNames( buttonClasses, 'is-scary') }
				onClick={ handleDisconnectConfirmation }
			>
				{ translate( 'Disconnect' ) }
			</Button>,
		];
	}, [handleDisconnectDialogCancel, handleDisconnectConfirmation, isSaving]);

	const carrierTypeIconMap = {
		DhlExpressAccount: 'dhlexpress',
		DhlEcsAccount: 'dhlecommerce',
		DhlEcommerceAsiaAccount: 'dhlecommerceasia',
		UpsAccount: 'ups',
	}

	return (
		<div className="carrier-accounts__list-item">
			<div className="carrier-accounts__list-item-carrier-icon">
				<CarrierIcon carrier={ carrierTypeIconMap[accountData.type] } size={ 18 } />
			</div>
			<div className="carrier-accounts__list-item-name">
				<span>{ accountData.carrier }</span>
			</div>
			<div className="carrier-accounts__list-item-credentials">
				<span>{ carrierId ? accountData.account : null }</span>
			</div>
			<div className="carrier-accounts__list-item-actions">
				{ carrierId ? (
					<Button 
						onClick={ handleShowDisconnectDialogConfirmation } 
						className = { classNames( buttonClasses, 'is-scary', 'is-borderless') }>
						{ translate( 'Disconnect' ) }
					</Button>
				) : (
					<a
						href={
							`admin.php?page=wc-settings&tab=shipping&section=woocommerce-services-settings&carrier=${accountData.type}`
						}
						// eslint-disable-next-line wpcalypso/jsx-classname-namespace
						className= { buttonClasses }
					>
						{ translate( 'Connect' ) }
					</a>
				) }
			</div>
			<Dialog
				isVisible={ isDisconnectDialogVisible }
				additionalClassNames="carrier-accounts__settings-cancel-dialog"
				onClose={ handleDisconnectDialogCancel }
				buttons={ disconnectDialogButtons }
			>
				<div className="carrier-accounts__settings-cancel-dialog-header">
					<h2 className="carrier-accounts__settings-cancel-dialog-title">
						{ translate( 'Disconnect your %(carrier_name)s account', {
							args: { carrier_name: accountData.carrier },
						} ) }
					</h2>
					<button
						className="carrier-accounts__settings-cancel-dialog-close-button"
						onClick={ handleDisconnectDialogCancel }
					>
						<Gridicon icon="cross" />
					</button>
				</div>
				<p className="carrier-accounts__settings-cancel-dialog-description">
					{ translate( 'This will remove the connection with %(carrier_name)s. All of your %(carrier_name)s account information will be deleted and you won’t see %(carrier_name)s rates.', {
						args: { carrier_name: accountData.carrier },
					} ) }
				</p>
			</Dialog>
		</div>
	);
};

CarrierAccountListItem.propTypes = {
	errorNotice: PropTypes.func.isRequired,
	successNotice: PropTypes.func.isRequired,
	siteId: PropTypes.number.isRequired,
	accountData: PropTypes.shape( {
		id: PropTypes.string,
		carrier: PropTypes.string.isRequired,
		account: PropTypes.string,
		type: PropTypes.string,
	} ).isRequired,
};

const mapStateToProps = (state) => ({
	siteId: getSelectedSiteId( state ),
});

const mapDispatchToProps = {
	errorNotice:errorNoticeAction,
	successNotice:successNoticeAction,
}

export default compose(connect(mapStateToProps, mapDispatchToProps),localize)( CarrierAccountListItem ) ;
