.rates-step__package-container {
	margin-bottom: 20px;

	.form-fieldset:last-child {
		margin-bottom: 0;
	}
}

.rates-step__package-container .form-server-error {
	margin-left: -2px; // align with form-input-validation gridicon
}

.rates-step__package-container .form-fieldset ~ .form-server-error {
	// when there is a rates drop-down
	margin-top: -1em;
}

.rates-step__package-container-rates-header {
	font-weight: bold;
	margin-bottom: 20px;
}

.notice.rates-step__notice {
	margin: -24px -24px 24px;
	width: inherit;
}

.rates-step__shipping-info-cost {
	font-weight: 600;
}


.rates-step__shipping-rate-container {
	display: flex;
	align-items: stretch;
	margin-top: 15px;
	padding: 0 0 15px;
	border-bottom: 1px solid #eee
}

.rates-step__shipping-rate-information {
	display: flex;
	flex-grow: 1;
	.carrier-icon {
		display: flex;
		height: 30px;
		max-width: 30px;
		margin: 15px 15px;
		align-items: center;
	}
}

.rates-step__shipping-rate-radio-control {
	margin: 21px 0 0 0;
	input[type='radio'] {
		border: 2px solid gray;
	}
	input[type='radio']:checked {
		border-color: #d52c82;
	}
	input[type='radio']:checked::before {
		margin: 2px;
		background-color: #d52c82;

		// For radio buttons to be visible in high contrast mode on Windows, a border or outline is required
		// See: https://github.com/WordPress/gutenberg/pull/23706
		border: 4px solid #d52c82;
		box-sizing: border-box;

		// Unset the translate() transform introduced in Gutenberg.
		// See: https://github.com/WordPress/gutenberg/pull/27377
		transform: none;
		left:0;
		top:0;
	}
}

.rates-step__shipping-rate-description {
	display: flex;
	flex-direction: column;
	width: 75%;
	margin: 15px 0 0 0;
}

.rates-step__shipping-rate-description-title {
	font-size: 15px;
}

.rates-step__shipping-rate-description-details {
	color: #636d75;
	font-size: 12px;
	margin-bottom: 4px;
	* label {
	}
	.components-base-control {
		padding: 4px 0;
	.components-base-control__field {
		margin: 0;
	}
	}
}

.rates-step__shipping-rate-description-signature-select {
	* select {
		color: #646970;
		font-size: 12px;
		min-width: 250px;
	}
}

.rates-step__shipping-rate-details {
	display: flex;
	flex-direction: column;
	margin: 15px auto;
	width: 25%;
}

.rates-step__shipping-rate-delivery-date {
	color: #646970;
	font-size: 12px;
	text-align: right;
}

.rates-step__shipping-rate-rate {
	text-align: right;
}

.rates-step__shipping-rate-rate-tooltip {
	display: inline-block;
	cursor: help;
	margin-right: 4px;
	svg {
		display: inline-block;
		vertical-align: top;
		margin-left: 2px;
		fill: #7c848b;
	}
}

.rates-step__shipping-info {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 56px;
	background: #EBF7F1;
	border: 1px solid #008A20;
	box-sizing: border-box;
	border-radius: 3px;
	margin-bottom: 36px;

	.gridicon {
		padding-right: 8px;
		color:#008A20;
	}
}
