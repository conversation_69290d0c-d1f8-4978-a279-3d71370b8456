@import './step-confirmation-button/style';
@import './address-step/style';
@import './packages-step/style';
@import './customs-step/style';
@import './rates-step/style';

&.label-purchase-modal {
	height: calc(100% - 10vmin);
	width: calc(100% - 10vmin);
    max-height: 1080px;
    max-width: 1920px;
  	box-shadow: rgba(151,150,150,.5) 2px 2px 7px;
	background-color: $studio-gray-0;

	@include breakpoint( '<660px' ) {
		width: 100%;
		&.dialog.card {
			height: 100%;
			max-height: 100%;
			width: 100%;
			max-width: 100%;
		}
	}

	.components-modal__content {
		background: $studio-gray-0;
		height: auto;
	}
}

&.tracking-modal {
	height: 60%;
	width: 40%;

	@include breakpoint( '<660px' ) {
		&.dialog.card {
			height: 100%;
			max-height: 100%;
			width: 100%;
			max-width: 100%;
		}
	}
}

.label-purchase-modal__sidebar {
	flex-basis: 100%;
	height: auto;
	padding: 24px;
	margin-bottom: 24px;

	@include breakpoint( '>660px' ) {
		flex-basis: 40%;
		margin-left: 16px;
		background: var( --color-white );
	}

	@include breakpoint( '>960px' ) {
		flex-basis: 30%;
	}
}

.label-purchase-modal__content {
	display: flex;
	min-height: calc(100% - 10vmin);
	flex-flow: column;
	margin: 0 -24px -24px;

	@include breakpoint( '>660px' ) {
		flex-flow: row;
		margin: 0;
	}

	.gridicon.is-success {
		color: var( --color-success );
	}

	.gridicon.is-warning {
		color: var( --color-warning );
	}

	.is-error:not( .notice ) {
		color: var( --color-error );
	}

	.is-error,
	.is-warning,
	.is-success {
		.notice__icon {
			display: block;
		}
	}

	select {
		width: 100%;
	}

	.foldable-card__header {
		padding: 12px 16px;
	}

	.foldable-card__secondary {
		white-space: nowrap;
	}

	.foldable-card.is-expanded {
		.foldable-card__content {
			padding: 24px;
		}
	}

	.foldable-card__summary,
	.foldable-card__summary-expanded {
		> span:first-child {
			display: flex;
			align-items: center;
			justify-content: flex-end;
		}

		svg:empty {
			display: none;
		}
	}

	@include breakpoint( '<660px' ) {
		.form-section-heading {
			padding-top: 20px;
			padding-left: 20px;
		}
		min-height: 0;
	}
}

.label-purchase-modal__body {
	display: flex;
	flex-grow: 1;
}

.label-purchase-modal__main-section {
	flex-basis: 100%;
	margin-bottom: 16px;

	@include breakpoint( '>960px' ) {
		flex-basis: 70%;
	}
}

.label-purchase-modal__step-title,
.label-purchase-modal__step-status {
	float: left;
}

.label-purchase-modal__step-status {
	margin: 3px 0 0 5px;
}

.label-purchase-modal__step-title {
	margin: 0 0 0 8px;
}

.label-purchase-modal__price-item {
	display: flex;
	margin-bottom: 8px;

	&.label-purchase-modal__price-item-total {
		font-weight: bold;
		margin-bottom: 0;
		margin-top: 24px;
	}
}

.label-purchase-modal__price-item-addons {
	margin-left: 20px;
}

.label-purchase-modal__price-item-carrier-account-notice {
	color: var( --color-neutral-400 );
	margin-bottom: 8px;
}

.label-purchase-modal__price-item-help {
	color: var( --color-neutral-200 );
	cursor: help;
	text-align: center;

}

.label-purchase-modal__price-item-amount {
	flex-grow: 1;
	text-align: right;
}

.label-purchase-modal__discount {
	background-color: var( --color-neutral-0 );
	color: var( --color-neutral-400 );
	padding: 3px;
	margin: 20px 0;
	font-size: 12px;

	svg {
		display: inline-block;
		vertical-align: top;
		margin-left: 2px;
	}
}

.label-purchase-modal__shipping-summary-header {
	font-size: 16px;
	font-weight: bold;
}

.label-purchase-modal__shipping-summary-street {
	color: #636d75;
	margin-top: 18px;
	a {
		margin-left: 7px;
	}
}

.label-purchase-modal__shipping-summarry-labels {
	margin-top: 18px;
}

.label-purchase-modal__shipping-summary-section {
	padding-bottom: 15px;
}
