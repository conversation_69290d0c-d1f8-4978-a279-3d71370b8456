.packages-step__dialog-package-option {
	font-weight: normal;
	margin-bottom: 10px;
}

.packages-step__dialog-package-name {
	font-weight: bold;
}

.packages-step__contents {
	display: flex;
	padding-bottom: 24px;
	flex-direction: column;

	@include breakpoint( '>960px' ) {
		flex-direction: row;
	}
}

.packages-step__list {
	width: auto;
	padding: 0 0 24px;
	flex-shrink: 0;

	@include breakpoint( '>960px' ) {
		flex-direction: row;
		width: 35%;
		padding: 0 24px 0 0;
	}
}

.packages-step__list-header {
	font-weight: 600;
	margin-bottom: 5px;
}

.packages-step__list-package {
	display: flex;
	padding: 6px 12px;
	cursor: pointer;
	align-items: center;
	width: 100%;

	&.is-selected {
		background-color: var( --color-neutral-0 );
	}

	.gridicon {
		top: 0;
	}
}

.packages-step__list-package-name {
	flex-grow: 1;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-right: 2px;
	text-align: left;
	color: var( --color-text );
}

.packages-step__list-package-count {
	display: inline-block;
	padding: 1px 6px;
	border: solid 1px var( --color-neutral-200 );
	border-radius: 12px;
	font-size: 11px;
	font-weight: 600;
	line-height: 14px;
	color: var( --color-text );
	text-align: center;
}

.packages-step__package {
	flex-grow: 1;

	> div {
		margin-bottom: 24px;

		&:last-child {
			margin-bottom: 0;
		}
	}
}

.packages-step__add-item-row {
	padding: 8px 0;
	display: flex;
}

.packages-step__no-items-message {
	padding: 8px 0;
	font-style: italic;
	flex-grow: 1;
}

.packages-step__no-items-message .packages-step__add-item-btn {
	vertical-align: middle;
	margin-left: 8px;
}

.packages-step__package-item-description {
	font-weight: bold;
}

.packages-step__package-items-header-name {
	width: 60%;
	display: inline-block;
}

.packages-step__package-items-header-weight {
	width: 15%;
	display: inline-block;
	text-align: center;
}

.packages-step__package-items-header-qty {
	width: 15%;
	display: inline-block;
	text-align: center;
}

.packages-step__package-items-header-move {
	width: 10%;
	display: inline-block;
}

.packages-step__item {
	display: flex;
	padding: 0 0 8px;

	&:last-child {
		padding-bottom: 0;
	}
	p {
		line-height: 1.4em;
	}
}

.packages-step__item-name {
	padding: 8px 0;
	width: 70%;
}

.packages-step__item-weight {
	padding: 8px 0;
	width: 15%;
	text-align: center;
}

.packages-step__item-qty {
	padding: 8px 0;
	width: 15%;
	text-align: center;
}

.packages-step__item-move {
	width: 10%;
	margin: 4px 0 4px 16px;
}

.packages-step__package-weight {
	width: 100%;
	margin-bottom: 10px;
	margin-right: 15px;
}

@-moz-document url-prefix() { /* fix the width in Firefox */
	.packages-step__package-weight div {
		width: 135px;
		margin-right: 65px;
	}
}

.packages-step__package-weight-unit {
	margin-left: 8px;
}

.packages-step__package-signature {
	width: 180px;
	float: left;
}

.packages-step__no-packages {
	background: #f6f6f6;
	width:  100%;
	text-align: center;
	padding: 20px 0;
	display: flex;
	align-items: center;
	justify-content: center;
	> a {
		margin-left: 8px;
	}
}
.packages-step__with-packages {
	margin-top: 5px;
}
.packages-step__package-details-header {
	display: flex;
	justify-content: space-between;
}
