.address-step__city-state-postal-code,
.address-step__company-phone {
	display: flex;
	flex-direction: column;

	@include breakpoint( '>960px' ) {
		flex-direction: row;
	}
}

.notice.is-error {
	margin: 6px 0 16px;
}

.address-step__address-1.form-fieldset {
	margin-bottom: 8px;
}

.address-step__city,
.address-step__state,
.address-step__postal-code,
.address-step__company,
.address-step__phone {
	width: 100%;

	@include breakpoint( '>960px' ) {
		margin-left: 24px;
	}
}

.address-step__city,
.address-step__company {
	margin-left: 0;
}

.address-step__suggestion-container,
.address-step__unverifiable-container {
	display: flex;
	align-items: flex-start;
	margin-top: 24px;
	flex-direction: column;

	@include breakpoint( '>960px' ) {
		flex-direction: row;
	}
}

.address-step__suggestion-title,
.address-step__unverifiable-title {
	font-weight: bold;
}

.address-step__suggestion,
.address-step__unverifiable-info {
	padding: 16px;
	flex-grow: 1;

	@include breakpoint( '>960px' ) {
		flex-direction: row;
		width: 50%;

		&:first-child {
			margin-right: 16px;
			margin-bottom: 24px;
		}
	}
}

.address-step__suggestion {
	border: 1px solid transparent;
	border-radius: 4px;
	cursor: pointer;
	width: 100%;

	&.is-selected {
		border-color: var( --color-primary );
	}
}

.address-step__suggestion-edit {
	margin-left: 24px;
	color: var( --color-primary );
	font-weight: bold;
}

.address-step__summary {
	margin: 8px 0 8px 24px;
	font-weight: normal;

	p {
		margin-bottom: 2px;
	}

	.highlight {
		background-color: var( --color-primary-0 );
	}
}

.address-step__unverifiable-info {
	.components-external-link {
		float: left;
		clear: both;
	}

	.address-step__summary {
		margin-left: 0;
	}
}

.address-step__actions {
	@extend .step-confirmation-button;

	.form-button {
		margin-left: 10px;

		&:first-child {
			margin-left: 0;
		}
	}
}
