@import './label-purchase-modal/style';
.shipping-label__payment .gridicon.notice__icon {
	align-self: flex-start;
	margin-top: 8px;
}

.shipping-label__item {
	p {
		margin-bottom: 3px;
		text-align: left;

		&.shipping-label__item-tracking {
			font-size: 12px;

			a:focus {
				outline: none;
				box-shadow: none;
			}
		}
	}

	.shipping-label__new-label-button {
		width: 100%;
		margin-top: 16px;
	}

	.ellipsis-menu__toggle {
		padding: 0;
	}

	.shipping-label__item-detail, .shipping-label__item-actions {
		display: flex;
		justify-content: space-between;

		a {
			display: inline-block;
			font-size: 12px;
		}

		span svg,
		a svg {
			position: relative;
			top: 2px;
			margin-right: 2px;
		}
	}

	.shipping-label__item-detail {
		margin-bottom: 6px;

		a {
			vertical-align: text-top;
		}
	}

	.shipping-label__item-actions {
		margin-bottom: 0;

		a {
			margin-top: 10px;
		}
	}

	.shipping-label__purchase-error {
		color: var( --color-error );
		cursor: help;
		text-decoration: underline;
		text-decoration-color: var( --color-error );
		text-decoration-style: dotted;
	}
}

&.dialog.card.label-reprint-modal {
	.shipping-label__reprint-modal-notice {
		color: var( --color-neutral-400 );
		font-style: italic;
	}
}

&.dialog.card.label-refund-modal {
	dd {
		margin-left: 0;
	}
}

&.dialog.card.label-details-modal {
	width: 460px;

	dd {
		margin-left: 0;

		ul {
			margin-left: 1.2em;
		}
	}

}

.error-notice {
	width: inherit;
}

.shipping-label__loading-spinner {
	margin: 8px auto;
	text-align: center;
}

.shipping-label__label-details-modal-heading {
	display: flex;
}

.shipping-label__label-details-modal-heading-title {
	flex-grow: 1;
}
