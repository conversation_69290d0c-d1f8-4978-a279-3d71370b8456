.packages__add-package-weight {
	margin-bottom: 8px;
	@include breakpoint( '>660px' ) {
		float: left;
	}

	&:nth-child( 1 ) {
		width: 100%;
		@include breakpoint( '>660px' ) {
			width: 50%;
		}
	}

	&:nth-child( 2 ) {
		width: 100%;
		@include breakpoint( '>660px' ) {
			width: 50%;
		}
	}

}

.packages__add-package-weight-group {
	.form-setting-explanation {
		@include breakpoint( '>660px' ) {
			clear: both;
		}
	}
}

&.packages__add-edit-dialog {
	height: 90%;
	width: 90%;

	.form-setting-explanation {
		display: inline-block;
	}

	@include breakpoint( '>660px' ) {
		width: 610px;
		height: auto;
		padding: 0;

		.dialog__content {
			padding: 0;

			.packages__add-edit-title {
				padding: 22px;
				margin: 0;
				border-bottom: 1px solid #EDEFF0;
				font-size: 20px;
			}

			.packages__mode-select {

				.segmented-control__link {
					border-radius: 0;
					border: 0 none;
					padding: 15px 0;

					&:focus {
						box-shadow: none;
					}

				}

				.is-selected {
					.segmented-control__link,
					.segmented-control__link:focus {
						background-color: inherit;
						color: black;
						font-weight: bold;
						border-bottom: 3px solid #96588A;
						outline: 0;
					}
				}

			}

			.packages__properties-group {
				margin: 24px;
			}
		}
	}
}

.packages__group-header-checkbox {
	margin-right: 12px;
	vertical-align: text-bottom;
}

.packages__packages-row {
	display: flex;
	flex-direction: row;
	padding: 8px 0;
	align-items: center;

	&:not( :last-child ) {
		border-bottom: 1px solid var( --color-neutral-0 );
	}

	&.packages__packages-header {
		border-bottom-color: var( --color-neutral-100 );
	}

	&.prefixed {
		&:first-child {
			border-top: 1px solid var( --color-neutral-0 );
		}

		.form-checkbox {
			margin-left: 16px;

			@include breakpoint( '>480px' ) {
				margin-left: 24px;
			}
		}

		.packages__packages-row-icon {
			padding-left: 0;
			text-align: center;
			color: var( --color-text-subtle );
		}

		.packages__packages-row-actions {
			width: 38px;
			padding-right: 8px;

			.components-base-control__field {
				margin: 0;
			}

			.components-checkbox-control__input-container {
				height: 16px;
				min-width: 16px;
				width: 16px;
			}

		}

		.packages__packages-row-details {
			width: 50%;
		}
	}

	&.error {
		color: var( --color-error );
		a {
			color: var( --color-error );
		}
		.packages__packages-row-icon .gridicon {
			background-color: unset;
			border-radius: unset;
			fill: var( --color-error );
			padding: unset;
			margin-left: -1px;
		}
	}

	&.placeholder {
		@include placeholder();
		pointer-events: none;
		background: var( --color-white );

		.gridicon,
		span,
		p,
		a,
		button {
			animation: loading-fade 1.6s ease-in-out infinite;
			background-color: var( --color-neutral-50 );
			color: transparent;
			cursor: default;
		}

		p,
		span {
			display: block;
			width: 100px;
			height: 14px;
		}

		.gridicon {
			fill: transparent;
			stroke: transparent;
		}
	}
}

.packages__packages-header {
	font-weight: 600;
	padding: 12px 0;
	font-size: 14px;
	border-top: 0;
	background: var( --color-neutral-0 );
}

.packages__packages-row-icon {
	width: 48px;
	text-align: left;
	padding-left: 16px;

	@include breakpoint( '>480px' ) {
		padding-left: 24px;
	}

	svg {
		margin-top: 6px;
		width: 24px;
	}
}

.packages__packages-row-details-name {
	margin-bottom: 0;
	font-size: 14px;
}

.packages__packages-row-actions {
	width: 25%;
	text-align: right;
	padding-right: 16px;
	margin: 0;

	@include breakpoint( '>480px' ) {
		padding-right: 24px;
	}
}

.packages__packages {
	padding: 0;
}

.packages__predefined-packages {
	margin-bottom: 0;

	.packages__packages-header {
		border-top: 1px solid var( --color-neutral-100 );
	}

	.foldable-card__header {
		.foldable-card__main {
			flex-grow: 3;
		}

		.foldable-card__secondary {
			flex-grow: 2;
		}

		.packages__group-header {
			display: flex;
			align-items: center;
		}

		@include breakpoint( '>480px' ) {
			padding-left: 24px;
		}
	}

	.foldable-card__content {
		padding: 0;
		border-top: 0;
	}
}

.packages__packages-row-details {
	width: 35%;
}

.packages__packages-row-dimensions {
	width: 30%;
	font-size: 14px;
}

.packages__setting-explanation {
	display: block;
	font-size: 13px;
	font-style: italic;
	font-weight: 400;
	margin: 5px 0 0;
	color: var( --color-primary );
	text-decoration: underline;
}

.packages__delete {
	float: left;
}

.packages__mode-select {
	margin-bottom: 12px;
}
