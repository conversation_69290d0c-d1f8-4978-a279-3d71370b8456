/** component optimized for printing
 *
 * Using an inline SVG for the logo ensures it will display correctly when printing,
 * as external image references may not resolve properly in print
 */
export const WPCOMPrintLogo = () => {
	return (
		<svg
			width="100%"
			height="100%"
			viewBox="0 0 31 31"
			preserveAspectRatio="xMidYMid meet"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			className="wpcom-print-logo"
		>
			<path
				d="M15.5496 1.05547C18.4502 1.05547 21.2736 1.91948 23.6651 3.53948C25.9948 5.11319 27.8308 7.33491 28.9262 9.92691C30.8393 14.4474 30.3302 19.616 27.5839 23.6737C26.0102 26.0034 23.7885 27.8394 21.1965 28.9349C16.6759 30.848 11.5073 30.3389 7.44966 27.5926C5.11994 26.0189 3.28394 23.7972 2.18852 21.2052C0.275379 16.6846 0.784522 11.516 3.5308 7.45834C5.10452 5.12862 7.32624 3.29262 9.91823 2.19719C11.6925 1.44119 13.6211 1.05547 15.5496 1.05547ZM15.5496 0.129761C7.03309 0.129761 0.121094 7.04176 0.121094 15.5583C0.121094 24.0749 7.03309 30.9869 15.5496 30.9869C24.0662 30.9869 30.9782 24.0749 30.9782 15.5583C30.9782 7.04176 24.0662 0.129761 15.5496 0.129761Z"
				fill="#3858e9"
			/>
			<path
				d="M2.69727 15.5581C2.69727 20.4798 5.50526 24.9849 9.94869 27.1295L3.80812 10.3278C3.06755 11.9787 2.69727 13.7529 2.69727 15.5581ZM24.2355 14.9101C24.2355 13.3209 23.6647 12.2255 23.171 11.3616C22.6773 10.4976 21.9058 9.40213 21.9058 8.35299C21.9058 7.30385 22.8007 6.06956 24.0658 6.06956H24.2355C19.0053 1.27127 10.8744 1.62613 6.07612 6.87185C5.61327 7.38099 5.18127 7.92099 4.81098 8.49185H5.64412C6.98641 8.49185 9.06927 8.32213 9.06927 8.32213C9.76355 8.27585 9.84069 9.29413 9.14641 9.3867C9.14641 9.3867 8.45212 9.46385 7.68069 9.51013L12.3555 23.4421L15.1635 15.0027L13.1578 9.51013C12.4635 9.46385 11.8155 9.3867 11.8155 9.3867C11.1213 9.34042 11.1984 8.29127 11.8927 8.32213C11.8927 8.32213 14.0218 8.49185 15.2715 8.49185C16.5213 8.49185 18.6967 8.32213 18.6967 8.32213C19.391 8.27585 19.4681 9.29413 18.7738 9.3867C18.7738 9.3867 18.0795 9.46385 17.3081 9.51013L21.9521 23.3341L23.279 19.1375C23.8653 17.3015 24.2201 16.0055 24.2201 14.8947L24.2355 14.9101ZM15.7653 16.6689L11.9081 27.8855C14.5001 28.6415 17.2618 28.5798 19.8075 27.6849L19.715 27.5152L15.7653 16.6689ZM26.8275 9.3867C26.8893 9.8187 26.9201 10.2661 26.9201 10.7136C26.9201 12.0249 26.6733 13.4907 25.9481 15.3112L22.0138 26.6667C28.031 23.1644 30.1755 15.4964 26.8275 9.3867Z"
				fill="#3858e9"
			/>
		</svg>
	);
};
