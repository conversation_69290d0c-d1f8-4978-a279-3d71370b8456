@keyframes notice-loading-pulse {
	0% { opacity: 0; }
	50% { opacity: 0.5; }
	100% { opacity: 0; }
}

.notice {
	display: flex;
	position: relative;
	width: 100%;
	margin-bottom: 24px;
	box-sizing: border-box;
	animation: appear 0.3s ease-in-out;
	background: var( --color-neutral-700 );
	color: var( --color-white );
	border-radius: 3px;
	line-height: 1.5;

	// Success!
	&.is-success {
		.notice__icon-wrapper {
			background: var( --color-success );
		}
	}

	// Warning
	&.is-warning {
		.notice__icon-wrapper {
			background: var( --color-warning );
		}
	}

	// Error! OHNO!
	&.is-error {
		.notice__icon-wrapper {
			background: var( --color-error );
		}
	}

	// General notice
	&.is-info {
		.notice__icon-wrapper {
			background: var( --color-accent );
		}
	}

	&.is-loading {
		.notice__icon-wrapper::after {
			content: '';
			background-color: var( --color-white );
			animation: notice-loading-pulse 0.8s ease-in-out infinite;
			position: absolute;
			top: 0;
			bottom: 0;
			left: 0;
			right: 0;
		}
	}

	.notice__dismiss {
		overflow: hidden;
	}

	&.is-success,
	&.is-error,
	&.is-warning,
	&.is-info {
		.notice__dismiss {
			overflow: hidden;
		}
	}
}

.notice__icon-wrapper {
	position: relative;
	background: var( --color-text-subtle );
	color: var( --color-white );
	display: flex;
	align-items: baseline;
	width: 47px;
	justify-content: center;
	border-radius: 3px 0 0 3px;
	flex-shrink: 0;
	align-self: stretch;

	.gridicon {
		margin-top: 10px;

		@include breakpoint( '>480px' ) {
			margin-top: 12px;
		}
	}
}

.notice__content {
	padding: 13px;
	font-size: 12px;
	flex-grow: 1;

	@include breakpoint( '>480px' ) {
		font-size: 14px;
	}
}

.notice__text {
	a,
	a:visited,
	button.is-link {
		text-decoration: underline;
		color: var( --color-white );

		&:hover {
			color: var( --color-white );
			text-decoration: none;
		}
	}

	ul {
		margin-bottom: 0;
		margin-left: 0;
	}

	li {
		margin-left: 2em;
		margin-top: 0.5em;
	}

	p {
		margin-bottom: 0;
		margin-top: 0.5em;

		&:first-child {
			margin-top: 0;
		}
	}
}

.notice__button {
	cursor: pointer;
	margin-left: 0.428em;
}

// "X" for dismissing a notice
.notice__dismiss {
	flex-shrink: 0;
	padding: 12px;
	cursor: pointer;
	padding-bottom: 0;

	.gridicon {
		width: 18px;
		height: 18px;
	}

	@include breakpoint( '>480px' ) {
		padding: 11px;
		padding-bottom: 0;

		.gridicon {
			width: 24px;
			height: 24px;
		}
	}

	.notice & {
		color: var( --color-neutral-200 );

		&:hover,
		&:focus {
			color: var( --color-white );
		}
	}
}

// specificity for general `a` elements within notice is too great
a.notice__action {
	cursor: pointer;
	font-size: 12px;
	font-weight: 400;
	text-decoration: none;
	white-space: nowrap;
	color: var( --color-neutral-200 );
	padding: 13px;
	display: flex;
	align-items: center;

	@include breakpoint( '>480px' ) {
			flex-shrink: 1;
			flex-grow: 0;
		align-items: center;
		border-radius: 0;
		font-size: 14px;
		margin: 0 0 0 auto; // forces the element to the right;
		padding: 13px 16px;

		.gridicon {
			width: 24px;
			height: 24px;
		}
	}

	&:visited {
		color: var( --color-neutral-200 );
	}

	&:hover {
		color: var( --color-white );
	}

	.gridicon {
		margin-left: 8px;
		opacity: 0.7;
		width: 18px;
		height: 18px;
	}
}

// Compact notices
.notice.is-compact {
	display: inline-flex;
		flex-wrap: nowrap;
		flex-direction: row;
	width: auto;
	border-radius: 3px;
	min-height: 20px;
	margin: 0;
	padding: 0;
	text-decoration: none;
	text-transform: none;
	vertical-align: middle;
	line-height: 1.5;

	.notice__content {
		font-size: 12px;
		padding: 6px 10px;
	}

	.notice__icon-wrapper {
		width: 28px;

		.notice__icon {
			width: 18px;
			height: 18px;
			margin: 0;
		}

		.gridicon {
			margin-top: 6px;
		}
	}

	.notice__dismiss {
		position: relative;
		align-self: center;
		flex: none;
		margin: 0 8px 0 0;
		padding: 0;

		.gridicon {
			width: 18px;
			height: 18px;
		}
	}

	a.notice__action {
		background: transparent;
		display: inline-block;
		margin: 0;
		font-size: 12px;
		align-self: center;
		margin-left: 16px;
		padding: 0 10px;

		&:hover,
		&:active,
		&:focus {
			background: transparent;
		}

		.gridicon {
			margin-left: 8px;
			width: 14px;
			height: 14px;
			vertical-align: sub;
			opacity: 1;
		}
	}
}
