@keyframes rotate-spinner {
	100% {
		transform: rotate( 360deg );
	}
}

.spinner {
	display: flex;
	align-items: center;
}

.spinner__outer, .spinner__inner {
	margin: auto;
	box-sizing: border-box;
  border: 0.1em solid transparent;
	border-radius: 50%;
	animation: 3s linear infinite;
	animation-name: rotate-spinner;
}

.spinner__outer {
	border-top-color: var( --color-accent );
}

.spinner__inner {
	width: 100%;
  height: 100%;
	border-top-color: var( --color-accent );
  border-right-color: var( --color-accent );
  opacity: 0.4;
}
