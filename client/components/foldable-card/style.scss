// Multisite
.foldable-card.card {
	@include clear-fix;
	position: relative;
	transition: margin 0.15s linear;
	padding: 0;
	border-radius: 0;

	&.is-expanded {
		margin: 8px 0;
	}
}

.foldable-card__header {
	min-height: 64px;
	width: 100%;
	padding: 16px;
	box-sizing: border-box;
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;

	&.is-clickable {
		cursor: pointer;
	}

	&.has-border {
		.foldable-card__summary,
		.foldable-card__summary-expanded {
			margin-right: 48px;
		}

		.foldable-card__expand {
			border-left: 1px var( --color-neutral-0 ) solid;
		}
	}
}

.foldable-card.is-compact .foldable-card__header {
	padding: 8px 16px;
	min-height: 40px;
}

.foldable-card.is-expanded .foldable-card__header {
	margin-bottom: 0;
	height: inherit;
	min-height: 64px;
}

.foldable-card.is-expanded.is-compact .foldable-card__header {
	min-height: 40px;
}

.foldable-card.is-disabled .foldable-card__header {
	opacity: 0.2;
}

.foldable-card__action {
	position: absolute;
	top: 0;
	right: 0;
	height: 100%;
}

.foldable-card.is-expanded .foldable-card__action {
	height: 100%;
}

.foldable-card.is-disabled .foldable-card__action {
	cursor: default;
}

.accessible-focus .foldable-card__action:focus {
	outline: thin dotted;
}

button.foldable-card__action {
	cursor: pointer;
}

.foldable-card__main {
	max-width: calc( 100% - 36px );
	display: flex;
	align-items: center;
	flex: 2 1;
	margin-right: 5px;

	@include breakpoint( '<480px' ) {
		flex: 1 1;
	}
}

.foldable-card__secondary {
	display: flex;
	align-items: center;
	flex: 1 1;
	justify-content: flex-end;

	@include breakpoint( '<480px' ) {
		flex: 0 1;
	}
}

.foldable-card__expand {
	width: 48px;

	.gridicon {
		fill: var( --color-neutral-300 );
		display: flex;
		align-items: center;
		width: 100%;
		vertical-align: middle;

		transition: transform 0.15s cubic-bezier( 0.175, 0.885, 0.32, 1.275 ), color 0.2s ease-in;
	}

	.gridicon:hover {
		fill: var( --color-neutral-300 );
	}

	&:hover .gridicon {
		fill: var( --color-neutral-500 );
	}
}

.foldable-card.is-expanded .foldable-card__expand .gridicon {
	transform: rotate( 180deg );
}

.foldable-card__content {
	display: none;
}

.foldable-card.is-expanded .foldable-card__content {
	display: block;
	padding: 16px;
	border-top: 1px solid var( --color-neutral-0 );
}

.foldable-card.is-compact .foldable-card.is-expanded .foldable-card__content {
	padding: 8px;
}

.foldable-card__summary,
.foldable-card__summary-expanded {
	margin-right: 40px;
	color: var( --color-text-subtle );
	font-size: 12px;
	transition: opacity 0.2s linear;
	display: inline-block;

	@include breakpoint( '<480px' ) {
		display: none;
	}
}

.foldable-card.has-expanded-summary .foldable-card__summary,
.foldable-card.has-expanded-summary .foldable-card__summary-expanded {
	transition: none;
	flex: 2;
	text-align: right;
}

.foldable-card__summary {
	opacity: 1;
	display: inline-block;
}

.foldable-card.is-expanded .foldable-card__summary {
	display: none;
}

.has-expanded-summary .foldable-card.is-expanded .foldable-card__summary {
	display: none;
}

.foldable-card__summary-expanded {
	display: none;
}

.foldable-card.is-expanded .foldable-card__summary-expanded {
	display: inline-block;
}
