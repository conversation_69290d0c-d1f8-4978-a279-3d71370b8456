/**
 * Segmented Control
 *
 */

.segmented-control {
	display: flex;
	margin: 0;
	border-radius: 4px;
	background-color: var( --color-white );
	list-style: none;
}

.segmented-control__item {
	flex: 1 1 auto;
	cursor: pointer;

	&:first-of-type .segmented-control__link {
		border-top-left-radius: 4px;
		border-bottom-left-radius: 4px;
	}

	&:last-of-type .segmented-control__link {
		border-right: solid 1px var( --color-neutral-100 );
		border-top-right-radius: 4px;
		border-bottom-right-radius: 4px;
	}

	&.is-selected + .segmented-control__item .segmented-control__link {
		border-left-color: var( --color-neutral-700 );
	}
}

.segmented-control__link {
	display: block;
	padding: 8px 12px;
	border: solid 1px var( --color-neutral-100 );
	border-right: none;
	font-size: 14px;
	line-height: 18px;
	color: var( --color-text-subtle );
	text-align: center;
	transition: color 0.1s linear, background-color 0.1s linear;

	&:focus {
		color: var( --color-neutral-700 );
		outline: none;
		background-color: var( --color-neutral-0 );
	}
}

.segmented-control__item.is-selected .segmented-control__link {
	border-color: var( --color-neutral-700 );
	color: var( --color-neutral-700 );
}

.notouch .segmented-control__link:hover {
	color: var( --color-neutral-700 );
	background-color: var( --color-neutral-0 );
}

.segmented-control__text {
	display: block;
	max-width: 100%;
	color: inherit;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}

.segmented-control.is-compact {
	.segmented-control__link {
		font-size: 13px;
		padding: 4px 8px;
	}
}

//Primary variation
.segmented-control.is-primary {
	.segmented-control__item {
		&.is-selected {
			.segmented-control__link {
				border-color: var( --color-primary );
				background-color: var( --color-primary );
				color: var( --color-white );

				&:focus {
					background-color: var( --color-primary-light );
				}
			}

			+ .segmented-control__item .segmented-control__link {
				border-left-color: var( --color-primary );
			}
		}
	}

	.segmented-control__link:focus {
		background-color: var( --color-neutral-0 );
	}
}

.notouch .segmented-control.is-primary {
	.segmented-control__link:hover {
		background-color: var( --color-neutral-0 );
	}
	.segmented-control__item.is-selected .segmented-control__link:hover {
		background-color: var( --color-primary-light );
	}
}
