.text-control-with-affixes {
	display: inline-flex;
	flex-direction: column;
	width: 100%;

	&.no-wrap {
		flex-direction: row;
	}

	@include breakpoint( '>480px' ) {
		flex-direction: row;
	}

	input[type='email'],
	input[type='password'],
	input[type='url'],
	input[type='text'],
	input[type='number'] {
		flex-grow: 1;

		&:focus {
			// Fixes the right border of the box shadow displayed when this input element is focused which appears as
			// cut off when this input has a suffix, or is stuck to another element that has a higher stacking order
			// (fix found at http://stackoverflow.com/a/24728957)
			transform: scale( 1 );
		}

		&:disabled {
			border-right-width: 0;

			& + .text-control-with-affixes__suffix {
				border-left: 1px solid var( --color-neutral-100 );
			}
		}
	}
}

@mixin no-prefix-wrap() {
	border-bottom-left-radius: 2px;
	border-right: none;
	border-top-right-radius: 0;
}

@mixin no-suffix-wrap() {
	border-bottom-left-radius: 0;
	border-left: none;
	border-top-right-radius: 2px;
}

.text-control-with-affixes__prefix,
.text-control-with-affixes__suffix {
	position: relative;
	background: transparent;
	border: 1px solid var( --color-neutral-100 );
	color: var( --color-neutral-500 );
	padding: 6px 14px;
	white-space: nowrap;
	flex: 1 0 auto;
	font-size: 16px;
	line-height: 1.5;
}

.text-control-with-affixes__prefix {
	border-top-left-radius: 2px;
	border-top-right-radius: 2px;

	@include breakpoint( '<480px' ) {
		:not( .no-wrap ) > & {
			border-bottom: none;
		}
	}

	.no-wrap > & {
		@include no-prefix-wrap();
	}

	@include breakpoint( '>480px' ) {
		@include no-prefix-wrap();
	}

	& + input[type='email'],
	& + input[type='password'],
	& + input[type='url'],
	& + input[type='text'],
	& + input[type='number'] {
		&:disabled {
			border-left-color: var( --color-neutral-100 );
			border-right-width: 1px;
		}
	}
}

.text-control-with-affixes__suffix {
	border-bottom-left-radius: 2px;
	border-bottom-right-radius: 2px;

	@include breakpoint( '<480px' ) {
		:not( .no-wrap ) > & {
			border-top: none;
		}
	}

	.no-wrap > & {
		@include no-suffix-wrap();
	}

	@include breakpoint( '>480px' ) {
		@include no-suffix-wrap();
	}
}
