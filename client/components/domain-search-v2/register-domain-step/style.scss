@import '@wordpress/base-styles/breakpoints';
@import '@wordpress/base-styles/mixins';

.wpcom-domain-search-v2 {
	--domain-search-v2-background-color: var( --color-main-background );

	&.initial-state {
		max-width: 800px;
		margin-inline: auto;
	}

	hr {
		background-color: initial;
	}

	.wpcom-domain-search-v2__empty-state-search-controls {
		container-type: inline-size;
		container-name: search-controls;

		.domain-search-controls__submit {
			display: none;

			@container search-controls (min-width: #{$break-mobile}) {
				display: flex;
			}
		}
	}

	.wpcom-domain-search-v2__empty-state-search-controls-helper-text-link {
		font-size: inherit;
		color: var( --wp-components-color-gray-900, #{$gray-900} );
	}

	.wpcom-domain-search-v2__already-own-domain-btn {
		display: flex;
		justify-content: center;

		& > div {
			max-width: 31.5rem;
		}
	}

	.wpcom-domain-search-v2__sticky-bottom {
		position: fixed;
		z-index: z-index( 'root', '.sticky-panel.is-sticky .sticky-panel__content' );
		display: flex;
		justify-content: center;
		left: 1rem;
		right: 1rem;
		bottom: 1rem;

		@include break-small() {
			left: 0;
			right: 0;
			bottom: 2.5rem;
		}

		.summary-button {
			width: auto;
		}
	}

	.register-domain-step__domains-quickfilter-group {
		padding-bottom: 0;
	}
}
