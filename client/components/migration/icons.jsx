/**
 * External dependencies
 */
import React from 'react';

export const Dashboard = () => <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
																		fill="none">
	<path
		d="M15.8751 8.31934C16.2338 8.52644 16.3567 8.98514 16.1496 9.34385L12.6496 15.406C12.4425 15.7648 11.9838 15.8877 11.6251 15.6806C11.2664 15.4734 11.1435 15.0148 11.3506 14.656L14.8506 8.59385C15.0577 8.23514 15.5164 8.11223 15.8751 8.31934Z"
		fill="#1E1E1E"/>
	<path
		d="M7.31285 10.8816C6.95413 10.6745 6.49544 10.7974 6.28833 11.1561C6.08122 11.5149 6.20413 11.9736 6.56285 12.1807L6.99586 12.4307C7.35458 12.6378 7.81327 12.5149 8.02038 12.1561C8.22749 11.7974 8.10458 11.3387 7.74586 11.1316L7.31285 10.8816Z"
		fill="#1E1E1E"/>
	<path
		d="M8.53135 8.31946C8.89007 8.11235 9.34877 8.23526 9.55587 8.59398L9.80587 9.02699C10.013 9.38571 9.89007 9.8444 9.53135 10.0515C9.17264 10.2586 8.71394 10.1357 8.50684 9.77699L8.25684 9.34398C8.04973 8.98526 8.17264 8.52656 8.53135 8.31946Z"
		fill="#1E1E1E"/>
	<path
		d="M12.75 7.75C12.75 7.33579 12.4142 7 12 7C11.5858 7 11.25 7.33579 11.25 7.75V8.25C11.25 8.66421 11.5858 9 12 9C12.4142 9 12.75 8.66421 12.75 8.25V7.75Z"
		fill="#1E1E1E"/>
	<path fillRule="evenodd" clipRule="evenodd"
				d="M3.25 13C3.25 8.16751 7.16751 4.25 12 4.25C16.8325 4.25 20.75 8.16751 20.75 13C20.75 14.824 20.1911 16.5197 19.2351 17.9224L19.0118 18.25H4.98822L4.76493 17.9224C3.80893 16.5197 3.25 14.824 3.25 13ZM12 5.75C7.99594 5.75 4.75 8.99594 4.75 13C4.75 14.3736 5.1313 15.6562 5.79378 16.75H18.2062C18.8687 15.6562 19.25 14.3736 19.25 13C19.25 8.99594 16.0041 5.75 12 5.75Z"
				fill="#1E1E1E"/>
</svg>;

export const Preformatted = () => <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
																			 fill="none">
	<rect width="24" height="24" rx="2" fill="white"/>
	<path
		d="M18 4H6C4.9 4 4 4.9 4 6V18C4 19.1 4.9 20 6 20H18C19.1 20 20 19.1 20 18V6C20 4.9 19.1 4 18 4ZM18.5 18C18.5 18.3 18.3 18.5 18 18.5H6C5.7 18.5 5.5 18.3 5.5 18V6C5.5 5.7 5.7 5.5 6 5.5H18C18.3 5.5 18.5 5.7 18.5 6V18ZM7 16.5H13V15H7V16.5ZM11 12.5H17V11H11V12.5ZM9 11H7V12.5H9V11ZM15 16.5H17V15H15V16.5Z"
		fill="black"/>
</svg>;

export const Shipping = () => <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
																	 fill="none">
	<rect width="24" height="24" rx="2" fill="white"/>
	<path
		d="M3 6.75C3 5.784 3.784 5 4.75 5H15V7.313L15.05 7.34L20.106 10.07L20.5 10.282V13.75C20.5 14.2141 20.3156 14.6592 19.9874 14.9874C19.6592 15.3156 19.2141 15.5 18.75 15.5H18.738C18.7729 15.8476 18.7345 16.1987 18.6254 16.5306C18.5163 16.8624 18.3388 17.1678 18.1044 17.4269C17.8701 17.6859 17.584 17.893 17.2647 18.0347C16.9453 18.1765 16.5999 18.2497 16.2505 18.2497C15.9011 18.2497 15.5557 18.1765 15.2363 18.0347C14.917 17.893 14.6309 17.6859 14.3966 17.4269C14.1622 17.1678 13.9847 16.8624 13.8756 16.5306C13.7665 16.1987 13.7281 15.8476 13.763 15.5H9.737C9.77189 15.8476 9.73354 16.1987 9.62442 16.5306C9.51529 16.8624 9.33782 17.1678 9.10344 17.4269C8.86907 17.6859 8.58299 17.893 8.26367 18.0347C7.94435 18.1765 7.59886 18.2497 7.2495 18.2497C6.90014 18.2497 6.55465 18.1765 6.23533 18.0347C5.91601 17.893 5.62993 17.6859 5.39556 17.4269C5.16118 17.1678 4.98371 16.8624 4.87458 16.5306C4.76546 16.1987 4.72711 15.8476 4.762 15.5H3V6.75ZM13.5 14V6.5H4.75C4.6837 6.5 4.62011 6.52634 4.57322 6.57322C4.52634 6.62011 4.5 6.6837 4.5 6.75V14H5.465C5.69748 13.7623 5.97516 13.5734 6.28172 13.4446C6.58827 13.3158 6.91749 13.2497 7.25 13.25C7.95 13.25 8.582 13.537 9.035 14H13.5ZM18.035 14H18.75C18.8163 14 18.8799 13.9737 18.9268 13.9268C18.9737 13.8799 19 13.8163 19 13.75V11.177L15 9.017V13.585C15.3798 13.365 15.8111 13.2494 16.25 13.25C16.95 13.25 17.582 13.537 18.035 14ZM6.282 15.5C6.24426 15.6477 6.24074 15.8021 6.27168 15.9514C6.30263 16.1007 6.36725 16.241 6.4606 16.3615C6.55395 16.4821 6.67358 16.5797 6.81038 16.647C6.94718 16.7144 7.09753 16.7496 7.25 16.75C7.43704 16.75 7.62035 16.6976 7.7791 16.5987C7.93785 16.4998 8.06567 16.3583 8.14804 16.1904C8.23042 16.0225 8.26405 15.8348 8.2451 15.6487C8.22616 15.4627 8.15541 15.2856 8.04089 15.1377C7.92637 14.9898 7.77266 14.877 7.59724 14.8121C7.42182 14.7472 7.23171 14.7328 7.04851 14.7706C6.86531 14.8083 6.69637 14.8967 6.56087 15.0256C6.42538 15.1545 6.32876 15.3189 6.282 15.5ZM15.282 15.5C15.2489 15.6273 15.2413 15.7599 15.2595 15.8902C15.2778 16.0204 15.3215 16.1458 15.3883 16.2592C15.4551 16.3725 15.5435 16.4715 15.6487 16.5506C15.7538 16.6297 15.8735 16.6873 16.0009 16.72C16.1283 16.7528 16.2609 16.7601 16.3911 16.7415C16.5213 16.7229 16.6466 16.6788 16.7597 16.6118C16.8729 16.5447 16.9717 16.456 17.0505 16.3506C17.1293 16.2453 17.1866 16.1255 17.219 15.998C17.285 15.741 17.2463 15.4683 17.1113 15.2399C16.9762 15.0115 16.756 14.846 16.499 14.78C16.242 14.714 15.9693 14.7527 15.7409 14.8877C15.5125 15.0228 15.347 15.243 15.281 15.5H15.282Z"
		fill="black"/>
</svg>;
export const LessonPlan = () => <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
																		 fill="none">
	<path
		d="M8.85074 4.82081L7.64702 3.92578L5.56365 6.72769L4.44959 5.89687L3.55286 7.09931L5.87107 8.82813L8.85074 4.82081Z"
		fill="#1E1E1E"/>
	<path d="M20 7.74955H11.1111V6.24955H20V7.74955Z" fill="#1E1E1E"/>
	<path d="M20 12.7496H11.1111V11.2496H20V12.7496Z" fill="#1E1E1E"/>
	<path fillRule="evenodd" clipRule="evenodd"
				d="M6 13.9996C7.10457 13.9996 8 13.1041 8 11.9996C8 10.895 7.10457 9.99955 6 9.99955C4.89543 9.99955 4 10.895 4 11.9996C4 13.1041 4.89543 13.9996 6 13.9996ZM6 12.9996C6.55229 12.9996 7 12.5518 7 11.9996C7 11.4473 6.55229 10.9996 6 10.9996C5.44772 10.9996 5 11.4473 5 11.9996C5 12.5518 5.44772 12.9996 6 12.9996Z"
				fill="#1E1E1E"/>
	<path fillRule="evenodd" clipRule="evenodd"
				d="M8 16.9996C8 18.1041 7.10457 18.9996 6 18.9996C4.89543 18.9996 4 18.1041 4 16.9996C4 15.895 4.89543 14.9996 6 14.9996C7.10457 14.9996 8 15.895 8 16.9996ZM7 16.9996C7 17.5518 6.55229 17.9996 6 17.9996C5.44772 17.9996 5 17.5518 5 16.9996C5 16.4473 5.44772 15.9996 6 15.9996C6.55229 15.9996 7 16.4473 7 16.9996Z"
				fill="#1E1E1E"/>
	<path d="M11.1111 17.7496H20V16.2496H11.1111V17.7496Z" fill="#1E1E1E"/>
</svg>;
