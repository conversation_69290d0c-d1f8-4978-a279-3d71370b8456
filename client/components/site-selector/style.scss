@import "@automattic/typography/styles/variables";
@import "../../assets/stylesheets/shared/mixins/long-content-fade";
@import "../../assets/stylesheets/shared/mixins/breakpoints";

/**
 * Site Selector
 *
 * @component `selector`
 */

.site-selector {
	overflow: visible;
	position: static;
	border: none;
	z-index: z-index("root", ".site-selector");
	display: flex;
	flex-direction: column;

	&.is-large .search {
		display: flex;
		position: relative;
		opacity: 1;
	}

	&:not(.is-large) .search {
		pointer-events: none;
	}

	&.is-large .site-selector__sites {
		border-top: 1px solid var(--color-neutral-10);
	}

	.search-component {
		.search-component__input {
			width: 100%;
		}

		.components-button {
			border: none;
			box-shadow: none;
	
			&:hover {
				background: transparent;
			}
		}
	}
}

// Styles for Site elements within the Selector
.site-selector .site,
.site-selector .all-sites {
	font-size: $font-body-small;

	// Remove the global fade effect from layout.scss for all site-selector items
	.site__title::after,
	.site__domain::after {
		display: none;
	}

	// Highlight selected site
	&.is-selected {
		background-color: var(--color-sidebar-menu-selected-background);

		.site__title,
		.site__domain {
			color: var(--color-sidebar-menu-selected-text);
			&::after {
				@include long-content-fade( $color: var( --color-sidebar-menu-selected-background ) );
			}
		}

		.count {
			border-color: var(--color-sidebar-menu-selected-text);
			color: var(--color-sidebar-menu-selected-text);
		}

		&.is-private .site__title::before {
			color: var(--color-sidebar-menu-selected-text);
		}
	}
}

.site-selector .site.is-highlighted {
	background-color: var(--color-print);

	.site__title {
		color: var(--color-text);
	}

	.site__domain {
		color: var(--color-text-subtle);
	}
}

.notouch .site-selector.is-hover-enabled .site:hover,
.notouch .site-selector.is-hover-enabled .all-sites:hover .site-selector .site.is-highlighted,
.site-selector .all-sites.is-highlighted {
	background-color: var(--color-print);
	cursor: pointer;

	.site__badge {
		// Only apply hover styling to specific badge types that should change
		// Exclude styled-component badges (staging, production, etc.) which handle their own hover states
		&:not([class*="SitesLaunchStatusBadge"]):not([class*="SitesStagingBadge"]):not([class*="SitesProductionBadge"]) {
			background-color: var(--color-surface);
		}

		&.is-p2,
		&.is-p2-workspace {
			background: var(--p2-color-link-dark);
			color: var(--p2-color-white);
		}
	}
}

// Highlight & hover effects
.notouch .layout__secondary .site-selector.is-hover-enabled .site:hover,
.notouch
.layout__secondary
.site-selector.is-hover-enabled
.all-sites:hover
.layout__secondary
.site-selector
.site.is-highlighted,
.layout__secondary .site-selector .all-sites.is-highlighted {
	background: var(--color-sidebar-menu-hover-background);

	.site__badge {
		background: var(--color-sidebar-background);
		color: var(--color-sidebar-text);

		&.is-p2,
		&.is-p2-workspace {
			background: var(--p2-color-link-dark);
			color: var(--p2-color-white);
		}
	}

	.site__title,
	.site__domain {
		color: var(--color-sidebar-menu-hover-text);
	}

	.site__title::before {
		color: var(--color-sidebar-menu-hover-text);
	}

	.count {
		border-color: var(--color-sidebar-menu-hover-text);
		color: var(--color-sidebar-menu-hover-text);
	}
}

// Tweaking secondary Layout styles.
.layout__secondary .site-selector .site__badge {
	background: var(--color-sidebar-menu-hover-background);
	color: var(--color-sidebar-menu-hover-text);

	&.is-p2,
	&.is-p2-workspace {
		background: var(--p2-color-link);
		color: var(--p2-color-white);
	}
}

.site-selector .search {
	margin: 8px;
	height: 33px;
	max-height: 33px;
	border: 1px solid var(--color-neutral-10);
	display: block;
	opacity: 0;
	position: absolute;

	// ensure sufficient selector specificity for .search.is-open, too
	&,
	&.is-open {
		width: auto;
	}

	&.has-focus {
		box-shadow: 0 0 0 2px var(--color-primary-10);

		&:hover {
			box-shadow: 0 0 0 2px var(--color-primary-20);
		}
	}

	.search__open-icon,
	.search__close-icon {
		color: var(--color-neutral-light);
		width: 32px;
		height: 18px;
	}
}

// The actual list of sites
.site-selector__sites {
	flex: 1;
	overflow-y: auto;
	background: var(--color-surface);

	@include breakpoint-deprecated( "<660px" ) {
		max-height: calc(100% - 109px);
	}
}

.site-selector__no-results {
	color: var(--color-neutral-light);
	font-style: italic;
	padding: 10px 20px;
}

.site-selector__actions {
	--transparent-button-text-color: var(--color-sidebar-text);
	--transparent-button-text-color-hover: var(--color-sidebar-menu-hover-text);
	display: flex;
	flex-direction: column;
	gap: 8px;
	padding: 8px;
	margin-top: auto;
}

// Containers in the list of sites are larger
.site-selector .site-action {
	padding-top: 15px;
}

.site-selector .all-sites {
	border-bottom: 1px solid var(--color-neutral-10);
}

.site-selector__list-bottom-adornment {
	color: var(--color-text-subtle);
	display: block;
	font-size: $font-body-extra-small;
	padding: 16px 16px 24px;

	a {
		color: inherit;
		text-decoration: underline;

		&:hover {
			text-decoration: none;
		}
	}
}

.site-selector__no-results + .site-selector__list-bottom-adornment {
	display: none;
}
