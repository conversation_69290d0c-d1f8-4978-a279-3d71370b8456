<?xml version="1.0"?>
<ruleset name="WordPress Coding Standards">
	<!-- See https://github.com/squizlabs/PHP_CodeSniffer/wiki/Annotated-ruleset.xml -->
	<!-- See https://github.com/WordPress-Coding-Standards/WordPress-Coding-Standards/blob/develop/WordPress-Core/ruleset.xml -->

	<description>WooCommerce dev PHP_CodeSniffer ruleset.</description>

	<!-- Exclude paths -->
	<exclude-pattern>*/node_modules/*</exclude-pattern>
	<exclude-pattern>*/vendor/*</exclude-pattern>
	<exclude-pattern>bin/*</exclude-pattern>

	<!-- Configs -->
	<config name="minimum_supported_wp_version" value="4.7" />
	<config name="testVersion" value="5.6-"/>

	<!-- Rules -->
	<rule ref="WooCommerce-Core" />

	<rule ref="WordPress.WP.I18n">
		<properties>
			<property name="text_domain" type="array" value="woocommerce-services" />
		</properties>
	</rule>

	<rule ref="PHPCompatibility">
		<exclude-pattern>tests/</exclude-pattern>
	</rule>

	<rule ref="WordPress.Files.FileName.InvalidClassFileName">
		<exclude-pattern>tests/*</exclude-pattern>
		<exclude-pattern>woocommerce-services.php</exclude-pattern>
	</rule>

	<rule ref="WordPress.Files.FileName.NotHyphenatedLowercase">
		<exclude-pattern>classes/*</exclude-pattern>
	</rule>

	<rule ref="Generic.Arrays.DisallowShortArraySyntax.Found">
      <exclude-pattern>classes/*</exclude-pattern>
    </rule>

	<rule ref="Generic.Commenting">
		<exclude-pattern>tests/</exclude-pattern>
	</rule>
	<rule ref="Squiz.Commenting.FileComment.MissingPackageTag">
		<exclude-pattern>classes/</exclude-pattern>
		<exclude-pattern>tests/</exclude-pattern>
	</rule>
	<rule ref="Squiz.Commenting.FileComment.Missing">
		<exclude-pattern>classes/</exclude-pattern>
		<exclude-pattern>tests/</exclude-pattern>
	</rule>
</ruleset>