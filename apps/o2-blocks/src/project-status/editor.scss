.wp-block-project-status__bar {
	display: flex;
	width: 100%;
	border: 1px solid #222;
	height: 20px;
	border-radius: 4px;
	overflow: hidden;
}

.wp-block-project-status__bar span {
	transition: all 0.2s ease-out;

	&.if-missing-style {
		display: none !important;
	}
}

.wp-block-project-status__header {
	display: flex;
	justify-content: space-between;
	align-items: baseline;
	margin-bottom: 10px;

	.wp-block-project-status__title {
		font-size: 13px;
		font-weight: bold;
		font-family:
			-apple-system,
			BlinkMacSystemFont,
			"Segoe UI",
			Roboto,
			Oxygen-Sans,
			Ubuntu,
			Cantarell,
			"Helvetica Neue",
			sans-serif;
	}
}

.wp-block-project-status__counts {
	font-size: 13px;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.wp-block-project-status__counts span {
	margin-left: 12px;
}

.wp-block-project-status__estimate {
	display: inline-block;
	border: 1px solid #222;
	border-radius: 4px;
	padding: 0 20px;
	text-align: right;
	font-size: 13px;
	margin: 16px 0;
	margin-left: auto;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.wp-block-project-status__team {
	display: inline-block;
	border: 1px solid #222;
	border-radius: 4px;
	padding: 0 20px;
	font-size: 13px;
	margin: 16px 0;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.wp-block-project-status__footer {
	display: flex;
	justify-content: space-between;
}
