import { Button } from '@wordpress/components';
import { RichText } from '@wordpress/editor';
import { Component } from '@wordpress/element';
import { Icon, check, cancelCircleFilled, arrowDown, arrowUp } from '@wordpress/icons';

export const ItemEditor = class extends Component {
	constructor() {
		super( ...arguments );
		this.toggleDone = this.toggleDone.bind( this );
		this.updateValue = this.updateValue.bind( this );
		this.onSetup = this.onSetup.bind( this );
		this.editor = undefined;
	}

	// @TODO: Please update https://github.com/Automattic/wp-calypso/issues/58453 if you are refactoring away from UNSAFE_* lifecycle methods!
	UNSAFE_componentWillReceiveProps( newProps ) {
		if ( newProps.shouldFocus && ! this.props.shouldFocus ) {
			window.requestAnimationFrame( () => {
				this.editor.focus();
			} );
		}
	}

	toggleDone() {
		const { item } = this.props;
		item.done = ! item.done;
		this.props.onChange( item );
	}

	updateValue( newValue ) {
		const { item } = this.props;
		item.value = newValue;
		this.props.onChange( item );
	}

	onSetup( editor ) {
		const { shouldFocus } = this.props;
		this.editor = editor;
		if ( shouldFocus ) {
			window.requestAnimationFrame( () => {
				this.editor.focus();
			} );
		}
	}

	render() {
		const { item, moveUp, moveDown, canMoveUp, canMoveDown, classNames, onDelete } = this.props;
		const { done, value } = item;
		return (
			<li className={ classNames }>
				{ /* eslint-disable-next-line jsx-a11y/click-events-have-key-events */ }
				<span className="item-status" onClick={ this.toggleDone } role="button" tabindex="0">
					{ done && <Icon icon={ check } /> }
				</span>
				{ /* { 0 < item.level && <Button onClick={ moveLeft }>&lt;</Button> }
				{ 2 > item.level && <Button onClick={ moveRight }>&gt;</Button> }
				{ '-'.repeat( item.level ) }  */ }
				<span className="item-title">
					<RichText
						tagName="div"
						value={ value }
						onChange={ this.updateValue }
						multiline={ false }
						onSplit={ this.props.onSplit }
						onSetup={ this.onSetup }
					/>
				</span>
				<span className="move-buttons">
					{ canMoveUp && (
						<Button onClick={ moveUp }>
							<Icon icon={ arrowUp } />
						</Button>
					) }
					{ canMoveDown && (
						<Button onClick={ moveDown }>
							<Icon icon={ arrowDown } />
						</Button>
					) }
					<Button onClick={ onDelete }>
						<Icon icon={ cancelCircleFilled } />
					</Button>
				</span>
			</li>
		);
	}
};
