.wp-block-a8c-github-template {
	background: #f3f6f8;
	border-radius: 2px;
	border: 1px solid #d8e2e9;
	box-shadow: inset 40px 0 0 0 #6cc644;
	color: inherit;
	display: block;
	padding: 4px 6px 8px 50px;
	position: relative;
	text-decoration: none;

	&:hover {
		color: #555;
		text-decoration: none;
	}

	&.is-warning {
		box-shadow: inset 40px 0 0 0 #fc0;
		padding-bottom: 10px;

		.wp-block-a8c-github-template__icon {
			background: none;
			color: var(--color-text-inverted);
			font-size: 26px;
			top: calc(50% - 23px);

			&::after {
				content: "⚠";
			}
		}

		.wp-block-a8c-github-template__sub-title {
			line-height: initial;
		}
	}
}

.wp-block-a8c-github-template__icon {
	// Need to disable the lint rule because we have a data URL that needs quotes.
	/* stylelint-disable-next-line function-url-quotes */
	background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' aria-label='GitHub' role='img' viewBox='0 0 512 512' width='24' height='24' focusable='false'%3E%3Crect width='512' height='512' rx='15%25' fill='%231B1817'%3E%3C/rect%3E%3Cpath fill='%23fff' d='M335 499c14 0 12 17 12 17H165s-2-17 12-17c13 0 16-6 16-12l-1-50c-71 16-86-28-86-28-12-30-28-37-28-37-24-16 1-16 1-16 26 2 40 26 40 26 22 39 59 28 74 22 2-17 9-28 16-35-57-6-116-28-116-126 0-28 10-51 26-69-3-6-11-32 3-67 0 0 21-7 70 26 42-12 86-12 128 0 49-33 70-26 70-26 14 35 6 61 3 67 16 18 26 41 26 69 0 98-60 120-117 126 10 8 18 24 18 48l-1 70c0 6 3 12 16 12z'%3E%3C/path%3E%3C/svg%3E");
	color: var(--color-text-inverted);
	font-size: large;
	height: 24px;
	left: 9px;
	position: absolute;
	top: calc(50% - 10px);
	width: 24px;
}

.wp-block-a8c-github-template__title {
	font-weight: 700;
	vertical-align: middle;
}

.wp-block-a8c-github-template__sub-title {
	font-size: smaller;
}

a.wp-block-a8c-github-template {
	color: inherit;
}
