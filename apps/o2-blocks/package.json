{"name": "@automattic/o2-blocks", "version": "1.0.0", "description": "Gutenberg extensions for o2 theme.", "main": "dist/editor.js", "sideEffects": true, "repository": {"type": "git", "url": "git://github.com/Automattic/wp-calypso.git", "directory": "packages/o2-blocks"}, "private": true, "author": "Automattic Inc.", "license": "GPL-2.0-or-later", "bugs": "https://github.com/Automattic/wp-calypso/issues", "homepage": "https://github.com/Automattic/wp-calypso", "scripts": {"clean": "rm -rf dist", "build:o2-blocks": "calypso-build", "dev": "yarn run calypso-apps-builder --localPath / --remotePath /home/<USER>/public_html/wp-content/a8c-plugins/a8c-blocks", "build": "NODE_ENV=production yarn dev", "teamcity:build-app": "./bin/ci-build-steps.sh"}, "dependencies": {"@automattic/calypso-build": "workspace:^", "@wordpress/api-fetch": "^7.23.0", "@wordpress/base-styles": "^5.23.0", "@wordpress/block-editor": "^14.18.0", "@wordpress/blocks": "^14.12.0", "@wordpress/components": "^29.9.0", "@wordpress/data": "^10.23.0", "@wordpress/editor": "^14.23.0", "@wordpress/element": "^6.23.0", "@wordpress/hooks": "^4.23.0", "@wordpress/i18n": "^5.23.0", "@wordpress/icons": "^10.23.0", "@wordpress/is-shallow-equal": "^5.23.0", "@wordpress/primitives": "^4.23.0", "clsx": "^2.1.1", "lodash": "^4.17.21", "moment": "^2.30.1", "new-github-issue-url": "^0.2.1", "react": "^18.3.1", "react-dom": "^18.3.1", "redux": "^5.0.1"}, "devDependencies": {"@automattic/calypso-apps-builder": "workspace:^", "@automattic/calypso-eslint-overrides": "workspace:^", "@wordpress/readable-js-assets-webpack-plugin": "^3.24.0", "postcss": "^8.5.3", "webpack": "^5.99.8"}}