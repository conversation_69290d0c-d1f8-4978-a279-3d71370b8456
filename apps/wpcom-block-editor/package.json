{"name": "@automattic/wpcom-block-editor", "version": "1.0.0-alpha.0", "description": "Utilities for the block editor in WordPress.com integration.", "sideEffects": true, "repository": {"type": "git", "url": "git://github.com/Automattic/wp-calypso.git", "directory": "apps/wpcom-block-editor"}, "private": true, "author": "Automattic Inc.", "license": "GPL-2.0-or-later", "bugs": "https://github.com/Automattic/wp-calypso/issues", "homepage": "https://github.com/Automattic/wp-calypso", "scripts": {"build:wpcom-block-editor-no-minify": "NODE_ENV=development calypso-build", "build:wpcom-block-editor": "NODE_ENV=production calypso-build", "clean": "rm -rf dist", "dev": "yarn run calypso-apps-builder --localPath dist --remotePath /home/<USER>/public_html/widgets.wp.com/wpcom-block-editor", "build": "NODE_ENV=production yarn dev", "teamcity:build-app": "yarn run build"}, "dependencies": {"@automattic/calypso-url": "workspace:^", "@automattic/typography": "workspace:^", "@babel/runtime": "^7.27.1", "@wordpress/api-fetch": "^7.23.0", "@wordpress/base-styles": "^5.23.0", "@wordpress/block-editor": "^14.18.0", "@wordpress/blocks": "^14.12.0", "@wordpress/components": "^29.9.0", "@wordpress/compose": "^7.23.0", "@wordpress/data": "^10.23.0", "@wordpress/dom-ready": "^4.23.0", "@wordpress/edit-post": "^8.23.0", "@wordpress/edit-site": "^6.23.0", "@wordpress/editor": "^14.23.0", "@wordpress/element": "^6.23.0", "@wordpress/hooks": "^4.23.0", "@wordpress/i18n": "^5.23.0", "@wordpress/icons": "^10.23.0", "@wordpress/is-shallow-equal": "^5.23.0", "@wordpress/plugins": "^7.23.0", "@wordpress/private-apis": "^1.23.0", "@wordpress/rich-text": "^7.23.0", "@wordpress/router": "^1.23.0", "@wordpress/url": "^4.23.0", "debug": "^4.4.1", "lodash": "^4.17.21", "react": "^18.3.1", "react-dom": "^18.3.1", "redux": "^5.0.1", "tinymce": "^5.0.0"}, "devDependencies": {"@automattic/calypso-apps-builder": "workspace:^", "@automattic/calypso-build": "workspace:^", "@automattic/calypso-eslint-overrides": "workspace:^", "@wordpress/dependency-extraction-webpack-plugin": "^6.24.0", "npm-run-all": "^4.1.5", "postcss": "^8.5.3", "webpack": "^5.99.8", "webpack-bundle-analyzer": "^4.10.2"}}