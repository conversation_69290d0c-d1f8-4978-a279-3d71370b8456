@import '@automattic/calypso-color-schemes';

$breakpoint-mobile: 782px; //Mobile size.
$breakpoint-tablet: 1224px; //Large tablet size.
$breakpoint-desktop: 1440px; //Desktop size.
$search-icon: 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none"%3E%3Cpath d="M9.16667 15.8333C12.8486 15.8333 15.8333 12.8486 15.8333 9.16667C15.8333 5.48477 12.8486 2.5 9.16667 2.5C5.48477 2.5 2.5 5.48477 2.5 9.16667C2.5 12.8486 5.48477 15.8333 9.16667 15.8333Z" stroke="%238C8F94" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/%3E%3Cpath d="M17.5 17.5L13.875 13.875" stroke="%238C8F94" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/%3E%3C/svg%3E';
$system-font-family: -apple-system, BlinkMacSystemFont, 'Noto Sans', 'Segoe UI', 'Roboto',
	'Oxygen-Sans', 'Ubuntu', 'Cantarell', 'Helvetica Neue', sans-serif;
$recoleta-font-family: 'Recoleta', serif;

.happy-blocks-new-support-content-footer {
	padding: 32px 0 64px 0;

	@media ( max-width: $breakpoint-mobile ) {
		padding: 32px 16px;
	}

	.support-footer__heading {
		font-family: var( --wp--preset--font-family--recoleta );
		font-size: 2.25rem;
		margin-bottom: 24px;

		@media ( max-width: $breakpoint-mobile ) {
			font-size: 1.5rem;
		}
	}

	.support-content-resources {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 24px;

		@media ( max-width: $breakpoint-mobile ) {
			display: flex;
			flex-direction: column;
			gap: 16px;
		}

		.support-content-resource {
			background-color: #f7f8fe;
			border-radius: 4px;
			padding: 40px;
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			text-decoration: none;

			@media ( max-width: $breakpoint-mobile ) {
				padding: 24px;
			}

			&:hover {
				.support-footer__card-title {
					color: #3858e9;
				}
			}
		}

		.support-footer__icon-wrapper {
			background-color: #3858e9;
			border-radius: 50%;
			padding: 8px;
			margin-bottom: 24px;
			display: inline-flex;
		}

		.support-footer__card-title {
			color: var( --studio-black, #000 );
			font-size: 1.25rem;
			margin-top: 0;
			margin-bottom: 8px;

			@media ( max-width: $breakpoint-mobile ) {
				font-size: 1.125rem; /* stylelint-disable-line scales/font-sizes */
			}
		}

		.support-footer__card-description {
			font-size: 1rem;
			color: var( --studio-gray-50, #646970 );
			line-height: 24px;
			margin: 0;
			max-width: 365px;

			@media ( max-width: $breakpoint-mobile ) {
				font-size: 0.875rem;
			}
		}
	}
}

.happy-blocks-support-content-footer {
	max-width: none !important;
	align-items: normal !important;
	box-sizing: border-box;
	padding: 96px 24px 76px 24px;

	p {
		font-size: 1rem;
		line-height: 24px;
		margin-top: 4px;
	}

	h4 {
		font-size: 1.75rem;
		margin-bottom: 0;
	}

	> * {
		margin-left: auto !important;
		margin-right: auto !important;
		max-width: var( --wp--style--global--wide-size );
	}

	.support-content-resources {
		display: flex;
		gap: 24px;

		.support-content-resource {
			display: flex;
			flex-direction: column;
			min-width: 0;
			overflow-wrap: break-word;
			word-break: break-word;
			border-bottom: 1px solid var( --studio-gray-0 );
			padding: 0 16px 32px 0;
			font-size: 1.25rem;
		}

		p {
			color: var( --studio-gray-60 );
			margin-top: 16px;
			margin-bottom: 28px;
		}

		h4 {
			line-height: 26px;
		}
	}

	.support-content-resource__title {
		margin-top: 0;
		font-family: var( --wp--preset--font-family--system-font );
	}

	@media ( min-width: $breakpoint-mobile ) {
		.support-content-resource {
			flex-basis: 0;
			flex-grow: 1;
		}
	}

	.resource-link {
		margin-top: auto;
		display: flex;
		font-size: 1rem;
		align-items: center;
		gap: 0.5em;

		figure {
			margin: 0;
		}
	}

	@media ( max-width: $breakpoint-mobile ) {
		padding: 12px 24px !important;

		h4 {
			font-size: 1.25rem;
			line-height: 26px !important;
		}

		p {
			margin-top: 4px;
			margin-bottom: 16px;
		}

		.support-content-resources {
			flex-direction: column;
			gap: 0;

			.support-content-resource {
				min-width: 317px;
				padding: 24px 48px 24px 0;

				.resource-link {
					line-height: 20px;
				}

				p {
					margin-top: 4px;
					margin-bottom: 16px;
				}
			}
		}
	}
}
