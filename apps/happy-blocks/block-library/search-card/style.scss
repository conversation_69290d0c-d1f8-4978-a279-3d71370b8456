$breakpoint-mobile: 782px; //Mobile size.
$breakpoint-tablet: 1224px; //Large tablet size.
$breakpoint-desktop: 1440px; //Desktop size.

.happy-blocks-search-card {
	background:
		#F7F8FE url(./assets/hero-bg.png) no-repeat bottom right;
	background-size: contain;
	flex-shrink: 0;
	padding: 0;

	&.navigation-only {
		background: #fff;
		position: sticky;
		top: 0;
		z-index: 1;
	}

	.navigation-header {
		padding: 0 24px;

		@media ( max-width: $breakpoint-mobile ) {
			padding: 0;
		}
	}

	.navigation.desktop-nav {
		display: flex;
		align-items: center;
		gap: 16px;
		list-style: none;
		margin: 0;
		padding: 16px 0;
		max-width: 1260px;
		margin-right: auto;

		@media ( max-width: $breakpoint-mobile ) {
			display: none;
		}

		li {
			&.active {
				color: #000;
				font-weight: 500;
				position: relative;
			}
			color: #646970;
			font-size: 13px;
			font-style: normal;
			font-weight: 400;
			line-height: 20px;
			cursor: pointer;

			&:hover:not(.separator) {
				color: #000;
			}

			&.separator::before {
				content: "|";
				color: #ddd;
				font-weight: 400;
			}

			a {
				color: inherit;
				text-decoration: none;
				
				&:hover {
					color: #000;
				}
			}
		}
	}

	.mobile-nav-dropdown {
		display: none;
		position: relative;
		
		@media ( max-width: $breakpoint-mobile ) {
			display: block;
			padding: 16px 0;
		}


		.dropdown-trigger {
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;
			background: transparent;
			border: none;
			padding: 0 16px;

			&[aria-expanded="true"] .dropdown-arrow {
				transform: rotate(180deg);
			}

			.dropdown-current {
				font-weight: 500;
				display: flex;
				align-items: center;
				font-size: 16px;

				.support-center-text {
					color: #000;
					font-weight: 600;
					margin-right: 8px;
				}

				.separator {
					color: var(--gray-200, #E0E0E0);
					font-weight: 400;
				}

				.active-page-text {
					color: #000;
					position: relative;
					margin-left: 8px;
				}
			}

			.dropdown-arrow {
				transition: transform 0.2s ease;
			}
		}

		.dropdown-menu {
			position: absolute;
			top: 100%;
			width: 100%;
			background: #fff;
			list-style: none;
			margin: 0;
			padding: 8px 16px;
			opacity: 0;
			visibility: hidden;
			transform: translateY(-10px);
			transition: all 0.2s ease;
			z-index: 1000;

			&.show {
				opacity: 1;
				visibility: visible;
				transform: translateY(0);
			}

			li {
				margin-bottom: 16px;

				a {
					display: block;
					color: var(--gray-90, #1D2327);
					line-height: 20px;
					text-decoration: none;

					&.active {
						color: var(--blue-50, #3858E9);
						font-weight: 600;
						margin-left: -12px;

						&::before {
							content: "•";
							display: inline-block;
							margin-right: 4px;
						}
					}
				}
			}
		}
	}

	// Blur backdrop for mobile dropdown
	&.mobile-dropdown-open {
		@media ( max-width: $breakpoint-mobile ) {
			.support-search-content {
				filter: blur(10px);
				pointer-events: none;
				transition: filter 0.2s ease;
			}
		}
	}

	.support-search-content {
		padding: 33px 24px 72px 24px;
		max-width: 1260px;
		margin-left: auto;
		margin-right: auto;

		@media ( max-width: $breakpoint-mobile ) {
			padding: 36px 16px;
			margin-left: 0;
		}

		h2 {
			margin-top: 16px;
			margin-bottom: 24px;

			font-size: 50px;
			font-style: normal;
			font-weight: 400;
			line-height: 1.2;

			@media ( max-width: $breakpoint-mobile ) {
				font-size: 44px;
			}

		}

		.input-wrapper {
			width: 570px;
			position: relative;
			direction: inherit;

			@media ( max-width: $breakpoint-mobile ) {
				width: 100%;
			}

		}

		.input-wrapper input {
			height: 62px;
			width: 100%;
			padding-inline-start: 24px;
			padding-inline-end: 52px;
			border-radius: 4px;
			border: none;
			box-shadow: 0 0 4px 0 rgba(56, 88, 233, 0.12);
			font-size: 16px;

			&::-webkit-search-cancel-button {
				-webkit-appearance: none;
				appearance: none;
				display: none;
			}
		}

		.input-wrapper button {
			position: absolute;
			inset-block-start: 50%;
			transform: translateY(-50%);
			inset-inline-end: 20px;
			background: none;
			border: none;
			padding: 0;
			cursor: pointer;
			display: flex;
			align-items: center;
			justify-content: center;

			svg {
				fill: #2C3338;
			}

			&:hover svg {
				fill: #3858E9;
			}
		}

		.search-terms {
			margin-top: 16px;

			display: flex;
			align-items: flex-start;
			gap: 16px;
			list-style: none;

			overflow-x: auto;
			white-space: nowrap;

			scrollbar-width: none;
			&::-webkit-scrollbar {
				display: none;
			}

			width: calc(100% + 32px);
			margin-left: -16px !important;
			padding-left: 16px;
			padding-right: 16px;

			@media ( max-width: $breakpoint-mobile ) {
				gap: 8px;
			}

			li {
				box-sizing: border-box;
				border-radius: 4px;
				background: #EDF1FF;
				padding: 2px;
				flex-direction: column;
				justify-content: center;
				display: flex;

				a {
					padding: 6px 14px;
					color: #2C3338;
					font-size: 12px;
					font-style: normal;
					font-weight: 400;
					line-height: 20px;
					text-decoration: none;
				}

				&:hover {
					background: #3858E9;
					cursor: pointer;

					a {
						color: #fff;
					}
				}
			}
		}
	}
}

// If we have the admin bar, push the search card down to make room for it.
#wpadminbar ~ .happy-blocks-search-card {
	@media ( min-width: $breakpoint-mobile ) {
		margin-left: 0;
		top: 32px;
	}
}

// Global styles for mobile navigation blur effect
body.mobile-nav-open {
	@media ( max-width: 782px ) {
		// Blur everything except the mobile dropdown and global nav
		> *:not(.x-wpcom-global-nav):not(.happy-blocks-search-card) {
			filter: blur(4px);
			pointer-events: none;
			transition: filter 0.2s ease;
		}
		
		// Ensure global nav stays visible and interactive
		.x-wpcom-global-nav {
			filter: none !important;
			pointer-events: auto !important;
			z-index: 1001;
		}
	}
}
