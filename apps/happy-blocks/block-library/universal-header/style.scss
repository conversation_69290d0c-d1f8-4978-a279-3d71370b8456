@import "@automattic/calypso-color-schemes";

$breakpoint-mobile: 782px; //Mobile size.
$breakpoint-tablet: 1224px; //Large tablet size.
$breakpoint-desktop: 1440px; //Desktop size.
$recoleta-font-family: "Reco<PERSON><PERSON>", sans-serif;
$system-font-family: -apple-system, BlinkMacSystemFont, "Noto Sans", "Segoe UI", "Roboto",
	"Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;

.lpc-mobile-menu-mask {
	position: fixed !important;
	top: 0 !important;
	left: 0 !important;
	width: 100vw !important;
	height: 100vh !important;
	background-image: none !important;
	z-index: 2147483646;
	background-color: rgba(0, 0, 0, 0.5);
	animation-duration: 233ms;
	animation-timing-function: cubic-bezier(0, 0, 0.21, 1);
	animation-fill-mode: forwards;
	display: none;
	animation-name: i-amphtml-sidebar-mask-fade-in;
}

.lpc-locale-btn {
	border: 1px solid #1e8cbe;
	border-radius: 2px;
	display: inline-block;
	padding: 2px 4px 3px;
}

.x-menu,
.x-link {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
	text-rendering: optimizeLegibility;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.x-root [role="menuitem"]:focus {
	outline: 1px dotted Highlight;
	outline: 5px auto -webkit-focus-ring-color;
}

.x-link {
	appearance: none;
	margin: 0;
	padding: 0;
	border: none;
	background: none;
	color: inherit;
	font: inherit;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
	text-decoration: none;
	cursor: pointer;
	transition: 0.15s ease-out;
	transition-property: background, color, opacity;
}

.x-link:hover {
	opacity: 0.85;
}

.x-link:active {
	opacity: 0.65;
	outline: none;
	transition: none;
}

.x-menu-grid,
.x-nav-list,
.x-dropdown-content ul {
	list-style: none;
	padding: 0;
	margin-left: 0;
	margin-bottom: 0;
	margin-top: 0;
}

.x-hidden {
	border: 0;
	clip: rect(0 0 0 0);
	height: 1px;
	margin: -1px;
	overflow: hidden;
	padding: 0;
	position: absolute;
	width: 1px;
}

.x-icon {
	box-sizing: border-box;
	position: relative;
	display: block;
	height: 36px;
	fill: currentColor;
	overflow: hidden;
}

.x-icon span {
	display: block;
	height: 2px;
	background: currentColor;
}

.x-icon--external {
	display: inline-block;
	vertical-align: baseline;
	margin-left: 2px;
	width: 9px;
	height: 9px;
}

.x-icon--close {
	width: 36px;
}

.x-icon--close span {
	position: absolute;
	top: 17px;
	left: 11px;
	height: 2px;
	width: 14px;
	transform: rotate(45deg);
}

.x-icon--close span + span {
	transform: rotate(-45deg);
}

.x-icon--menu {
	width: 26px;
	padding-top: 10px;
}

.x-icon--menu span + span {
	margin-top: 4px;
}

.x-dropdown {
	position: absolute;
	z-index: 802;
	top: 45px;
	bottom: 0;
	/*rtl:ignore*/
	left: 0;
	width: 312px;
	opacity: 0;
	transform-origin: 50% 0;
	pointer-events: none;

	.logged-in.admin-bar & {
		top: 77px; //If logged in, we need to take into consideration the adminbar height.
	}
}

.x-dropdown-bottom,
.x-dropdown-top {
	position: absolute;
	right: 0;
	left: 0;
	padding: 0 48px;
	overflow: hidden;
}

.x-dropdown-top {
	box-sizing: border-box;
	top: -48px;
	height: 96px;
	padding-top: 57px;
	pointer-events: none;
}

.x-dropdown-middle {
	position: absolute;
	z-index: 1;
	top: 47px;
	right: 48px;
	left: 48px;
	height: 2px;
	background: #fff;
}

.x-dropdown-bottom {
	top: 48px;
	height: 0;
}

.x-dropdown-bottom-fill,
.x-dropdown-top-fill {
	position: relative;
	border-radius: 4px;
	background: #fff;
	box-shadow: 0 9px 48px rgba(16, 21, 23, 0.25);
}

.x-dropdown-top-fill {
	height: 96px;
}

.x-dropdown-top-fill::before {
	content: "";
	position: absolute;
	top: 0;
	left: 90px;
	border-radius: 2px;
	background: inherit;
	width: 36px;
	height: 36px;
	transform: rotate(45deg);
}

.x-dropdown-bottom-fill {
	top: -48px;
	height: 0;
}

.x-dropdown-content {
	position: absolute;
	top: 0;
	right: 48px;
	left: 48px;
	padding: 24px 0 18px;
	opacity: 0;
	pointer-events: none;
}

.x-dropdown-content[aria-hidden="false"] {
	z-index: 1;
	pointer-events: auto;
}

.x-dropdown-content-separator {
	height: 1px;
	background: #e9eff5;
	margin: 9px 0 12px;
}

.x-dropdown-link {
	display: block;
	padding: 6px 12px 6px 24px;
	font-size: 0.875rem;
	font-weight: 600;
	line-height: 19px;
	color: #135e96 !important;
}

.x-dropdown-link:hover,
.x-dropdown-link:active,
.x-dropdown-link:focus {
	background: #e9eff5;
	color: #0a4b78;
	opacity: 1;
	transition-property: background, color;
}

.x-dropdown-link:active {
	background: #c5d9ed;
	color: #01263a;
}

.js-dynamic-type-on .x-dropdown-link {
	font-family: inherit;
	font-weight: 600;
}

.x-nav {
	display: flex;
	justify-content: space-between;
	height: 57px;
	padding: 0 24px;
	color: rgb(var(--lp-color-primary, 255, 255, 255));
}

.x-nav-list {
	display: flex;
	align-items: center;
	margin: 0;
	gap: 24px;
}

.x-nav-list--left {
	left: 0;
}

.x-nav-list--right {
	position: initial;
	.x-nav-link--primary {
		border: 1px solid var(--studio-gray-5);
		background-color: transparent;
		padding: 9px 12px 10px;
		border-radius: 4px;
	}
}

.x-nav-item {
	display: block;
}

.x-nav-item--wide {
	display: none;
}

.x-nav-link {
	display: block;
	font-size: 1rem;
	font-weight: 600;
	line-height: 16px;
	white-space: nowrap;
	cursor: pointer;
	color: #003c56;
}

.x-nav-link-chevron {
	position: relative;
	left: 1px;
	margin-right: -1px;
	opacity: 0.65;
}

.x-nav-link-chevron::before {
	content: "▾";
}

.x-nav-link--logo {
	padding-right: 16px;
}

.x-nav-link--menu {
	padding: 0 9px 0 9px;
	margin-top: 9px;
}

@media (min-width: 768px) {

	.x-nav-link--menu {
		padding: 0 9px 0 9px;
		margin-right: 15px;
	}
}

@media (min-width: 864px) {
	.x-nav-item--narrow {
		display: none;
	}

	.x-nav-item--wide {
		display: block;
	}

	.x-nav-link--logo {
		padding: 0 6px 0 0;
	}

	.x-nav-link--primary {
		position: relative;
		padding-right: 33px;
		padding-left: 18px;
	}

	.x-root .x-nav-link--primary[role="menuitem"]:focus {
		outline: none;
	}

	.x-nav-link--primary:focus::before {
		box-shadow: 0 0 0 1px currentColor;
		opacity: 1;
	}
}

@media (min-width: 1056px) {

	.x-nav-link--primary {
		padding: 9px 12px 10px;
	}

	.x-nav-link--primary::before {
		left: 12px;
	}
}

.x-menu {
	position: absolute;
	z-index: 9999999999;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	display: none;
	pointer-events: none;
}

.x-menu--active {
	display: block;
}

.x-menu--open {
	pointer-events: auto;
}

.x-menu-overlay {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background: rgba(16, 21, 23, 0.85);
	opacity: 0;
}

.x-menu--active .x-menu-overlay {
	transition: opacity 0.25s ease-out;
}

.x-menu--open .x-menu-overlay {
	opacity: 1;
	transition-timing-function: ease-in-out;
}

.x-menu-content {
	position: absolute;
	top: 12px;
	right: 12px;
	left: 12px;
	border-radius: 4px;
	background: #fff;
	font-size: 0.875rem;
	line-height: 19px;
	transform: scale(0);
	transform-origin: 100% 0;
	transform-origin: calc(100% - 19px) 15px;
	pointer-events: none;
}

@media (min-width: 480px) {
	.x-menu-content {
		left: auto;
		width: 408px;
	}
}

.x-menu--active .x-menu-content {
	transition: transform 0.25s ease-in-out;
}

.x-menu--open .x-menu-content {
	transform: scale(1);
	transition-duration: 0.35s;
	transition-timing-function: cubic-bezier(0.1, 0.6, 0.2, 1);
	pointer-events: auto;
}

.x-menu-button {
	box-sizing: border-box;
	position: absolute;
	z-index: 1000;
	top: -12px;
	right: -12px;
	width: 48px;
	height: 48px;
	padding-top: 12px;
	color: #3582c4;
}

.x-menu-list {
	padding: 12px 18px 15px;
}

.x-menu-list:first-of-type {
	padding-top: 18px;
}

.x-menu-list:last-of-type {
	padding-bottom: 24px;
}

.x-menu-list + .x-menu-list {
	border-top: 1px solid #e9eff5;
}

.x-menu-list-title {
	padding: 6px 6px 0;
	color: #646970;
	font-size: 0.875rem;
	font-weight: 400;
	line-height: 17px;
	text-transform: uppercase;
}

.x-menu-grid {
	display: flex;
	flex-wrap: wrap;
}

.x-menu-grid-item {
	width: 50%;
}

.x-menu-link {
	display: block;
	padding: 6px 6px 3px;
	color: #135e96 !important;
	font-size: 0.875rem;
	font-weight: 600;
	line-height: 19px;
}

.x-menu-link-chevron {
	display: inline-block;
	font-weight: 400;
	transition: transform 0.15s ease-out;
}

.x-menu-link-chevron::before {
	content: "›";
}

.x-menu-link:hover .x-menu-link-chevron,
.x-menu-link:active .x-menu-link-chevron {
	transform: translate3d(0.15em, 0, 0);
}

.js-dynamic-type-on .x-menu-grid-item {
	width: 100%;
}

.js-dynamic-type-on .x-menu-link {
	font-family: inherit;
	font-weight: 600;
}

.login-link .x-nav-link {
	&:hover {
		text-decoration: none;
	}
}

button.x-link:hover {
	background-image: none !important;
	background-color: transparent !important;
}
