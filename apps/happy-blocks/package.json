{"name": "happy-blocks", "description": "Blocks used on WordPress.com sites such as Forums and Support.", "version": "1.0.0", "private": true, "author": "Automattic Inc.", "license": "GPL-2.0-or-later", "repository": {"type": "git", "url": "git://github.com/Automattic/wp-calypso.git", "directory": "packages/happy-blocks"}, "scripts": {"build": "NODE_ENV=production yarn dev && yarn run build-translations-manifest", "teamcity:build-app": "./bin/ci-build-steps.sh", "build-calypso-strings": "wp-babel-makepot '../../{client,packages,apps}/**/*.{js,jsx,ts,tsx}' --ignore '**/node_modules/**,**/test/**,**/*.d.ts' --base '../../' --dir 'dist/strings' --output './dist/calypso-strings.pot' && rm -rf dist/strings", "build:pricing-plans": "calypso-build --env block='pricing-plans'", "build:search-card": "calypso-build --env block='search-card'", "build:universal-header": "calypso-build --env block='universal-header'", "build:support-content-footer": "calypso-build --env block='support-content-footer'", "build:education-header": "calypso-build --env block='education-header'", "build-translations-manifest": "yarn run build-calypso-strings && node bin/build-translations-manifest.js", "clean": "rm -r release-files block-library/*/build || true", "dev": "yarn run calypso-apps-builder --localPath / --remotePath /home/<USER>/public_html/wp-content/a8c-plugins/happy-blocks"}, "dependencies": {"@automattic/calypso-analytics": "workspace:^", "@automattic/calypso-build": "workspace:^", "@automattic/calypso-config": "workspace:^", "@automattic/calypso-products": "workspace:^", "@automattic/color-studio": "^4.1.0", "@automattic/components": "workspace:^", "@automattic/number-formatters": "^1.0.1", "@automattic/typography": "workspace:^", "@automattic/wp-babel-makepot": "workspace:^", "@automattic/wpcom-template-parts": "workspace:^", "@emotion/styled": "^11.11.0", "@wordpress/base-styles": "^5.23.0", "@wordpress/block-editor": "^14.18.0", "@wordpress/blocks": "^14.12.0", "@wordpress/components": "^29.9.0", "@wordpress/data": "^10.23.0", "@wordpress/element": "^6.23.0", "@wordpress/i18n": "^5.23.0", "clsx": "^2.1.1", "glob": "^7.2.3", "i18n-calypso": "workspace:^", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@automattic/calypso-apps-builder": "workspace:^", "@emotion/react": "^11.11.1", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@wordpress/readable-js-assets-webpack-plugin": "^3.24.0", "copy-webpack-plugin": "^10.2.4", "glob": "^7.2.3", "postcss": "^8.5.3", "webpack": "^5.99.8"}}