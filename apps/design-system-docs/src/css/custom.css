/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

@import '@wordpress/components/build-style/style.css';
@import '@wordpress/dataviews/build-style/style.css';

/* You can override the default Infima variables here. */
:root {
  /* Generated from WP Blueberry using the palette generator at https://docusaurus.io/docs/styling-layout#styling-your-site-with-infima */
  --ifm-color-primary: #3858e9;
  --ifm-color-primary-dark: #1e42e6;
  --ifm-color-primary-darker: #183cdd;
  --ifm-color-primary-darkest: #1431b6;
  --ifm-color-primary-light: #526eec;
  --ifm-color-primary-lighter: #5f79ed;
  --ifm-color-primary-lightest: #869af2;

  --ifm-code-font-size: 95%;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
}

/* TODO: Dark mode toggle is disabled until we figure this out. */
[data-theme='dark'] {
  --ifm-color-primary: #25c2a0;
  --ifm-color-primary-dark: #21af90;
  --ifm-color-primary-darker: #1fa588;
  --ifm-color-primary-darkest: #1a8870;
  --ifm-color-primary-light: #29d5b0;
  --ifm-color-primary-lighter: #32d8b4;
  --ifm-color-primary-lightest: #4fddbf;

  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.3);
}

.navbar__logo {
  height: 20px;
}
