# Automattic Design System Website

This website is built using [Docusaurus](https://docusaurus.io/).

## Package scripts

These scripts need to be run from the package directory, so run `cd apps/design-system-docs` first.

### Installation

```
$ yarn
```

### Local Development

```
$ yarn start
```

This command starts a local development server and opens up a browser window. Most changes are reflected live without having to restart the server.

### Build

```
$ yarn build
```

This command generates static content into the `dist` directory and can be served using any static contents hosting service.
