{"name": "@automattic/design-system-docs", "version": "0.0.0", "private": true, "license": "GPL-2.0-or-later", "author": "Automattic Inc.", "description": "Documentation site for the Automattic Design System.", "homepage": "https://github.com/Automattic/wp-calypso/tree/trunk/apps/design-system-docs", "repository": {"type": "git", "url": "https://github.com/Automattic/wp-calypso.git", "directory": "apps/design-system-docs"}, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start -p 42987", "build": "docusaurus build --out-dir dist", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve -p 42987", "teamcity:build-app": "yarn run build", "write-heading-ids": "docusaurus write-heading-ids", "typecheck": "tsc"}, "dependencies": {"@docusaurus/core": "^3.7.0", "@docusaurus/preset-classic": "^3.7.0", "@mdx-js/react": "^3.1.0", "@wordpress/base-styles": "^5.23.0", "@wordpress/browserslist-config": "^6.23.0", "@wordpress/components": "^29.9.0", "@wordpress/dataviews": "^5.0.1-next.719a03cbe.0", "docusaurus-plugin-sass": "^0.2.6", "prism-react-renderer": "^2.4.1", "react": "^18.3.1", "react-dom": "^18.3.1", "sass": "^1.54.0"}, "devDependencies": {"@docusaurus/module-type-aliases": "^3.7.0", "@docusaurus/tsconfig": "^3.7.0", "@docusaurus/types": "^3.7.0", "@types/react": "^18.3.23", "typescript": "^5.8.3"}, "optionalDependencies": {"sass-embedded": "^1.89.0"}, "browserslist": ["extends @wordpress/browserslist-config"], "engines": {"node": ">=18.0", "yarn": "^4.0.0"}, "sideEffects": ["*.css", "*.scss"]}