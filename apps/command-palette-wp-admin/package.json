{"name": "@automattic/command-palette-wp-admin", "version": "1.0.0", "description": "Provides utils to load the command palette in WP Admin.", "main": "dist/build.min.js", "sideEffects": true, "repository": {"type": "git", "url": "git://github.com/Automattic/wp-calypso.git", "directory": "apps/command-palette-wp-admin"}, "private": true, "author": "Automattic Inc.", "license": "GPL-2.0-or-later", "bugs": {"url": "https://github.com/Automattic/wp-calypso/issues"}, "homepage": "https://github.com/Automattic/wp-calypso", "scripts": {"clean": "npx rimraf dist", "build": "NODE_ENV=production yarn dev", "build:command-palette-wp-admin": "calypso-build", "teamcity:build-app": "yarn run build", "dev": "yarn run calypso-apps-builder --localPath dist --remotePath /home/<USER>/public_html/widgets.wp.com/command-palette", "translate": "rm -rf dist/strings && mkdir -p dist && wp-babel-makepot '../../{client,packages,apps}/**/*.{js,jsx,ts,tsx}' --ignore '**/node_modules/**,**/test/**,**/*.d.ts' --base '../../' --dir './dist/strings' --output './dist/command-palette-strings.pot' && build-app-languages --stringsFilePath='./dist/command-palette-strings.pot'"}, "dependencies": {"@automattic/command-palette": "workspace:^", "@automattic/sites": "workspace:^", "@tanstack/react-query": "^5.83.0", "@wordpress/data": "^10.23.0", "@wordpress/dom-ready": "^4.23.0", "@wordpress/i18n": "^5.23.0", "calypso": "workspace:^", "i18n-calypso": "workspace:^", "react": "^18.3.1", "react-dom": "^18.3.1", "redux": "^5.0.1"}, "devDependencies": {"@automattic/calypso-apps-builder": "workspace:^", "@automattic/calypso-build": "workspace:^", "@automattic/calypso-eslint-overrides": "workspace:^", "@automattic/languages": "workspace:^", "@automattic/wp-babel-makepot": "workspace:^", "@wordpress/dependency-extraction-webpack-plugin": "^6.24.0", "gettext-parser": "^6.0.0", "lodash": "^4.17.21", "npm-run-all": "^4.1.5", "postcss": "^8.5.3", "webpack": "^5.99.8", "webpack-bundle-analyzer": "^4.10.2"}}