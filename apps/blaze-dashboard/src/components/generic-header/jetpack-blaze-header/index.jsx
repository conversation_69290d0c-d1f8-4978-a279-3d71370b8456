import clsx from 'clsx';

import './style.scss';

const JetpackBlazeHeader = ( { className = '' } ) => (
	<header className={ clsx( 'jetpack-blaze-header', className ) }>
		<svg width="175" height="32" viewBox="0 0 175 32" fill="none">
			<g clipPath="url(#clip0_8399_69928)">
				<path
					d="M16.0006 32C24.8374 32 32.0012 24.8362 32.0012 15.9994C32.0012 7.16259 24.8374 0 16.0006 0C7.16377 0 0 7.16377 0 16.0006C0 24.8374 7.16377 32 16.0006 32Z"
					fill="#069E08"
				/>
				<path d="M16.7944 13.3132V28.8245L24.7947 13.3132H16.7944Z" fill="white" />
				<path d="M15.1765 18.6572V3.17554L7.20703 18.6572H15.1765Z" fill="white" />
				<path
					d="M41.3124 26.5651C40.8538 25.8625 40.4273 25.161 39.9995 24.4892C42.2591 23.1148 43.0221 22.0164 43.0221 19.9393V7.93884H40.3656V5.64966H46.0151V19.3291C46.0151 22.8102 45.008 24.7641 41.3124 26.5651Z"
					fill="black"
				/>
				<path
					d="M64.9781 18.3824C64.9781 19.5424 65.8028 19.6644 66.3526 19.6644C66.9023 19.6644 67.6962 19.4808 68.3064 19.2983V21.4358C67.4509 21.7107 66.5658 21.924 65.3442 21.924C63.8785 21.924 62.1687 21.3742 62.1687 18.809V12.5184H60.6118V10.3501H62.1687V7.14502H64.9781V10.3513H68.5197V12.5196H64.9781V18.3824Z"
					fill="black"
				/>
				<path
					d="M70.8403 27.634V10.3206H73.5276V11.3585C74.5964 10.5339 75.7872 10.0149 77.2529 10.0149C79.7873 10.0149 81.8028 11.7863 81.8028 15.6028C81.8028 19.3896 79.6049 21.8933 75.9709 21.8933C75.0858 21.8933 74.3831 21.7713 73.6497 21.6184V27.6032H70.8403V27.634ZM76.5195 12.3361C75.6948 12.3361 74.6568 12.733 73.6793 13.5885V19.4821C74.2895 19.6041 74.9317 19.6953 75.786 19.6953C77.7707 19.6953 78.9011 18.4429 78.9011 15.8172C78.9011 13.4048 78.0764 12.3361 76.5195 12.3361Z"
					fill="black"
				/>
				<path
					d="M92.8552 21.649H90.2295V20.3966H90.1679C89.2519 21.0992 88.1216 21.8623 86.4426 21.8623C84.9769 21.8623 83.3892 20.7935 83.3892 18.6252C83.3892 15.7246 85.862 15.1748 87.6026 14.9307L90.0754 14.5954V14.2601C90.0754 12.7328 89.4652 12.2446 88.0291 12.2446C87.3265 12.2446 85.6783 12.4579 84.3347 13.0077L84.0906 10.7481C85.3122 10.3204 86.9912 10.0159 88.3965 10.0159C91.1442 10.0159 92.9156 11.1154 92.9156 14.3821V21.649H92.8552ZM90.0458 16.3668L87.7246 16.7329C87.022 16.8242 86.2897 17.2519 86.2897 18.2899C86.2897 19.2058 86.8087 19.7248 87.5718 19.7248C88.3965 19.7248 89.2816 19.2366 90.0446 18.6868V16.3668H90.0458Z"
					fill="black"
				/>
				<path
					d="M104.459 21.2829C103.299 21.6798 102.261 21.9239 100.947 21.9239C96.7339 21.9239 95.0537 19.5115 95.0537 16.0007C95.0537 12.3062 97.3749 10.0159 101.13 10.0159C102.534 10.0159 103.389 10.26 104.336 10.5657V12.9473C103.511 12.6416 102.321 12.3062 101.161 12.3062C99.4508 12.3062 97.9851 13.2222 97.9851 15.8478C97.9851 18.7484 99.4508 19.6347 101.313 19.6347C102.199 19.6347 103.176 19.4511 104.489 18.9321V21.2829H104.459Z"
					fill="black"
				/>
				<path
					d="M109.772 15.2069C110.016 14.932 110.2 14.6571 113.742 10.3513H117.405L112.824 15.7259L117.832 21.6799H114.168L109.802 16.3053V21.6799H106.994V5.64966H109.803V15.2069H109.772Z"
					fill="black"
				/>
				<path
					d="M58.5031 21.2831C57.0374 21.7416 55.785 21.9241 54.3193 21.9241C50.7161 21.9241 48.4873 20.1231 48.4873 15.9085C48.4873 12.8242 50.3807 10.0149 54.0136 10.0149C57.6168 10.0149 58.8692 12.5185 58.8692 14.9001C58.8692 15.694 58.8076 16.1218 58.778 16.5791H51.5111C51.5727 19.052 52.9768 19.6325 55.0835 19.6325C56.2435 19.6325 57.2815 19.3577 58.4735 18.9299V21.2807H58.5031V21.2831ZM55.939 14.7177C55.939 13.3432 55.4805 12.1524 53.9851 12.1524C52.5811 12.1524 51.7256 13.1596 51.5419 14.7177H55.939Z"
					fill="black"
				/>
			</g>
			<path
				d="M132.547 13.5715C133.039 13.643 133.479 13.7739 133.868 13.9644C134.265 14.1549 134.602 14.4009 134.88 14.7025C135.166 14.9961 135.384 15.3493 135.535 15.762C135.685 16.1668 135.761 16.6311 135.761 17.1549C135.761 17.5279 135.705 17.9207 135.594 18.3334C135.483 18.7382 135.297 19.135 135.035 19.5239C134.781 19.9049 134.443 20.258 134.023 20.5834C133.61 20.9088 133.094 21.1747 132.475 21.3811C131.999 21.5398 131.471 21.6509 130.892 21.7144C130.312 21.7779 129.618 21.8096 128.809 21.8096H124.689V5.88106H128.666C129.031 5.88106 129.36 5.88899 129.654 5.90487C129.955 5.9128 130.229 5.92868 130.475 5.95249C130.729 5.9763 130.963 6.00804 131.178 6.04772C131.392 6.07947 131.594 6.11915 131.785 6.16677C132.285 6.30169 132.737 6.4882 133.142 6.7263C133.555 6.95645 133.904 7.23423 134.189 7.55963C134.475 7.88503 134.693 8.25407 134.844 8.66677C135.003 9.07947 135.082 9.52788 135.082 10.012C135.082 10.4009 135.035 10.7739 134.939 11.1311C134.852 11.4882 134.705 11.8176 134.499 12.1192C134.301 12.4207 134.039 12.6906 133.713 12.9287C133.396 13.1668 133.007 13.3652 132.547 13.5239V13.5715ZM129.582 12.7739C129.963 12.7739 130.293 12.758 130.57 12.7263C130.848 12.6866 131.098 12.6311 131.32 12.5596C131.916 12.3612 132.348 12.0517 132.618 11.6311C132.896 11.2104 133.035 10.7144 133.035 10.143C133.035 9.55566 132.892 9.0755 132.606 8.70249C132.32 8.32947 131.872 8.05169 131.261 7.86915C130.959 7.78185 130.61 7.72233 130.213 7.69058C129.816 7.6509 129.348 7.63106 128.809 7.63106H126.63V12.7739H129.582ZM126.63 14.5239V20.0596H128.975C129.531 20.0596 130.047 20.0358 130.523 19.9882C130.999 19.9326 131.404 19.8493 131.737 19.7382C132.102 19.6192 132.412 19.4684 132.666 19.2858C132.92 19.0953 133.122 18.889 133.273 18.6668C133.432 18.4366 133.543 18.1945 133.606 17.9406C133.678 17.6866 133.713 17.4287 133.713 17.1668C133.713 16.8652 133.678 16.5834 133.606 16.3215C133.535 16.0517 133.416 15.8136 133.249 15.6072C133.09 15.393 132.876 15.2065 132.606 15.0477C132.344 14.889 132.015 14.766 131.618 14.6787C131.38 14.6231 131.106 14.5834 130.797 14.5596C130.495 14.5358 130.118 14.5239 129.666 14.5239H126.63Z"
				fill="black"
			/>
			<path d="M138.681 21.8096V4.57153H140.526V21.8096H138.681Z" fill="black" />
			<path
				d="M150.482 20.5596H150.434C150.283 20.7501 150.093 20.9406 149.863 21.1311C149.633 21.3136 149.367 21.4803 149.065 21.6311C148.764 21.7739 148.434 21.889 148.077 21.9763C147.72 22.0636 147.339 22.1072 146.934 22.1072C146.387 22.1072 145.883 22.0239 145.422 21.8572C144.97 21.6985 144.577 21.4644 144.244 21.1549C143.918 20.8374 143.664 20.4525 143.482 20.0001C143.299 19.5477 143.208 19.0358 143.208 18.4644C143.208 17.9009 143.307 17.3969 143.506 16.9525C143.712 16.508 143.998 16.1311 144.363 15.8215C144.728 15.512 145.164 15.2739 145.672 15.1072C146.188 14.9406 146.756 14.8572 147.375 14.8572C147.962 14.8652 148.506 14.9168 149.006 15.012C149.506 15.1072 149.95 15.2263 150.339 15.3692H150.387V14.5834C150.387 14.2739 150.367 14.0001 150.327 13.762C150.287 13.5239 150.22 13.3136 150.125 13.1311C149.918 12.7501 149.589 12.4366 149.137 12.1906C148.684 11.9366 148.073 11.8096 147.303 11.8096C146.732 11.8096 146.192 11.8652 145.684 11.9763C145.184 12.0795 144.684 12.2263 144.184 12.4168V10.8692C144.375 10.7739 144.597 10.6866 144.851 10.6072C145.113 10.5199 145.387 10.4485 145.672 10.393C145.958 10.3295 146.26 10.2819 146.577 10.2501C146.895 10.2184 147.212 10.2025 147.529 10.2025C148.672 10.2025 149.605 10.4049 150.327 10.8096C151.057 11.2144 151.581 11.7501 151.899 12.4168C152.018 12.6707 152.101 12.9525 152.149 13.262C152.204 13.5636 152.232 13.8969 152.232 14.262V21.8096H150.684L150.482 20.5596ZM150.387 16.8453C150.045 16.7501 149.641 16.6588 149.172 16.5715C148.704 16.4842 148.188 16.4366 147.625 16.4287C146.839 16.4287 146.224 16.5953 145.779 16.9287C145.335 17.262 145.113 17.7739 145.113 18.4644C145.113 18.8136 145.168 19.1192 145.279 19.3811C145.391 19.643 145.541 19.8612 145.732 20.0358C145.93 20.2025 146.16 20.3295 146.422 20.4168C146.692 20.4961 146.978 20.5358 147.279 20.5358C147.676 20.5358 148.045 20.4803 148.387 20.3692C148.728 20.2501 149.029 20.1112 149.291 19.9525C149.561 19.7938 149.787 19.635 149.97 19.4763C150.16 19.3176 150.299 19.1945 150.387 19.1072V16.8453Z"
				fill="black"
			/>
			<path
				d="M160.902 12.1311V12.0834H154.688V10.5001H163.247V11.6668L156.961 20.1787V20.2263H163.664V21.8096H154.497V20.8096L160.902 12.1311Z"
				fill="black"
			/>
			<path
				d="M170.346 11.7858C169.925 11.7858 169.532 11.8692 169.167 12.0358C168.81 12.2025 168.496 12.4287 168.227 12.7144C167.957 13.0001 167.739 13.3374 167.572 13.7263C167.405 14.1072 167.302 14.516 167.262 14.9525H172.977C172.977 14.508 172.917 14.0953 172.798 13.7144C172.687 13.3255 172.52 12.9882 172.298 12.7025C172.076 12.4168 171.802 12.1945 171.477 12.0358C171.151 11.8692 170.774 11.7858 170.346 11.7858ZM171.358 20.5001C171.969 20.5001 172.524 20.4525 173.024 20.3572C173.532 20.2541 174.036 20.1112 174.536 19.9287V21.4644C174.131 21.6628 173.631 21.8176 173.036 21.9287C172.441 22.0477 171.802 22.1072 171.119 22.1072C170.31 22.1072 169.548 22.0041 168.834 21.7977C168.127 21.5914 167.508 21.258 166.977 20.7977C166.445 20.3374 166.024 19.7382 165.715 19.0001C165.413 18.2541 165.262 17.3572 165.262 16.3096C165.262 15.2779 165.405 14.385 165.691 13.6311C165.977 12.8692 166.358 12.2342 166.834 11.7263C167.31 11.2184 167.858 10.8374 168.477 10.5834C169.096 10.3295 169.742 10.2025 170.417 10.2025C171.052 10.2025 171.643 10.3096 172.191 10.5239C172.746 10.7303 173.227 11.0596 173.631 11.512C174.036 11.9644 174.354 12.5477 174.584 13.262C174.822 13.9763 174.941 14.8334 174.941 15.8334C174.941 15.9287 174.941 16.012 174.941 16.0834C174.941 16.1469 174.937 16.2977 174.929 16.5358H167.167C167.167 17.258 167.274 17.8731 167.489 18.3811C167.711 18.8811 168.008 19.2898 168.381 19.6072C168.762 19.9168 169.207 20.143 169.715 20.2858C170.223 20.4287 170.77 20.5001 171.358 20.5001Z"
				fill="black"
			/>
			<defs>
				<clipPath id="clip0_8399_69928">
					<rect width="117.833" height="32" fill="white" />
				</clipPath>
			</defs>
		</svg>
	</header>
);

export default JetpackBlazeHeader;
