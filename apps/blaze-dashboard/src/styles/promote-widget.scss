// Overrides styles for the Create campaign widget
div.Mui<PERSON>opover-root,
div.<PERSON><PERSON><PERSON>ickersPopper-root,
div.MuiDialog-root,
div.MuiModal-root,
div.MuiAutocomplete-popper {
	z-index: 99999;
}

// Fixes double scrolls on widget creation overlay
html.has-blank-canvas {
	overflow: hidden;
	body {
		overflow: hidden;
	}
}

// Lower the z-index to match WordPress core.
// This is a fix for simple sites due to #wpadminbar being modified to have a really higher z-index.
// We only want to override the CSS for the promote post widget to not break anything else.
.is-section-promote-post-i2 #wpadminbar {
	z-index: 99999 !important;
}

.blazepress-widget {
	z-index: 99999;

	// Fix conflict with the WP forms-css file
	input[type="text"],
	input[type="password"],
	input[type="color"],
	input[type="date"],
	input[type="datetime"],
	input[type="datetime-local"],
	input[type="email"],
	input[type="month"],
	input[type="number"],
	input[type="search"],
	input[type="tel"],
	input[type="time"],
	input[type="url"],
	input[type="week"],
	select {
		box-shadow: none;
		border-radius: 0;
		border: none;
		background-color: inherit;
		color: currentcolor;

		padding: 16.5px 14px;
		line-height: inherit;
		min-height: inherit;

		&:focus {
			outline: none;
		}
	}
	textarea {
		box-shadow: none;
		border-radius: 0;
		border: none;
		background-color: inherit;
		color: currentcolor;

		padding: 0;
		line-height: inherit;
		min-height: inherit;
		height: auto;

		&:focus {
			outline: none;
		}
	}
	input.MuiInputBase-inputAdornedStart {
		padding: 0;
	}
}
