{"name": "@automattic/notifications", "version": "1.0.0", "description": "WordPress.com Notifications Panel.", "main": "dist/build.min.js", "sideEffects": true, "repository": {"type": "git", "url": "git://github.com/Automattic/wp-calypso.git", "directory": "apps/notifications"}, "private": true, "author": "Automattic Inc.", "license": "GPL-2.0-or-later", "bugs": "https://github.com/Automattic/wp-calypso/issues", "homepage": "https://github.com/Automattic/wp-calypso", "scripts": {"analyze-bundles": "EMIT_STATS=true yarn run build && webpack-bundle-analyzer stats.json dist -h 127.0.0.1 -s gzip", "clean": "rm -rf dist stats.json", "build:notifications": "calypso-build", "dev": "yarn run calypso-apps-builder --localPath dist --remotePath /home/<USER>/public_html/widgets.wp.com/notifications", "build": "NODE_ENV=production yarn dev", "teamcity:build-app": "yarn build", "translate": "rm -rf dist/strings && wp-babel-makepot '../../{client,packages,apps}/**/*.{js,jsx,ts,tsx}' --ignore '**/node_modules/**,**/test/**,**/*.d.ts' --base './' --dir './dist/strings' --output './dist/notifications.pot' && build-app-languages --stringsFilePath='./dist/notifications.pot' --outputPath='./dist/languages'"}, "dependencies": {"@automattic/calypso-color-schemes": "workspace:^", "@automattic/calypso-polyfills": "workspace:^", "@automattic/calypso-router": "workspace:^", "@automattic/components": "workspace:^", "@automattic/i18n-utils": "workspace:^", "@automattic/webpack-extensive-lodash-replacement-plugin": "workspace:^", "@wordpress/components": "^29.9.0", "@wordpress/data": "^10.23.0", "@wordpress/i18n": "^5.23.0", "autoprefixer": "^10.4.21", "calypso": "workspace:^", "clsx": "^2.1.1", "debug": "^4.4.1", "i18n-calypso": "workspace:^", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-redux": "^9.2.0", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "webpack-bundle-analyzer": "^4.10.2", "wpcom": "workspace:^", "wpcom-proxy-request": "workspace:^"}, "devDependencies": {"@automattic/calypso-apps-builder": "workspace:^", "@automattic/calypso-build": "workspace:^", "@automattic/calypso-eslint-overrides": "workspace:^", "@automattic/languages": "workspace:^", "@automattic/wp-babel-makepot": "workspace:^", "html-webpack-plugin": "^5.6.3", "postcss": "^8.5.3", "postcss-custom-properties": "^12.1.11", "webpack": "^5.99.8"}}