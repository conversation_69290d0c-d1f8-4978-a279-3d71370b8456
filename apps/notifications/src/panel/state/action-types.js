export const APPROVE_NOTE = 'APPROVE_NOTE';
export const CLOSE_PANEL = 'CLOSE_PANEL';
export const LIKE_NOTE = 'LIKE_NOTE';
export const NOTES_ADD = 'NOTES_ADD';
export const NOTES_REMOVE = 'NOTES_REMOVE';
export const NOTES_LOADING = 'NOTES_LOADING';
export const NOTES_LOADED = 'NOTES_LOADED';
export const SELECT_NOTE = 'SELECT_NOTE';
export const READ_NOTE = 'READ_NOTE';
export const RESET_LOCAL_APPROVAL = 'RESET_LOCAL_APPROVAL';
export const RESET_LOCAL_LIKE = 'RESET_LOCAL_LIKE';
export const SET_IS_SHOWING = 'SET_IS_SHOWING'; // special! do not use
export const SET_LAYOUT = 'SET_LAYOUT';
export const SPAM_NOTE = 'SPAM_NOTE';
export const SUGGESTIONS_FETCH = 'SUGGESTIONS_FETCH';
export const SUGGESTIONS_STORE = 'SUGGESTIONS_STORE';
export const TRASH_NOTE = 'TRASH_NOTE';
export const UNDO_ACTION = 'UNDO_ACTION';
export const VIEW_SETTINGS = 'VIEW_SETTINGS';
export const CLOSE_SHORTCUTS_POPOVER = 'CLOSE_SHORTCUTS_POPOVER';
export const TOGGLE_SHORTCUTS_POPOVER = 'TOGGLE_SHORTCUTS_POPOVER';
export const SET_FILTER = 'SET_FILTER';
export const EDIT_COMMENT = 'EDIT_COMMENT';
export const ANSWER_PROMPT = 'ANSWER_PROMPT';
export const DISABLE_KEYBOARD_SHORTCUTS = 'DISABLE_KEYBOARD_SHORTCUTS';
export const ENABLE_KEYBOARD_SHORTCUTS = 'ENABLE_KEYBOARD_SHORTCUTS';
