$notification-panel-width: 450px;
$no-sidebar-min-page-width: 800px;
$with-sidebar-min-page-width: 1114px;

@mixin setToggleNoteBehaviour {
	.wpnc__list-view.wpnc__current {
		display: block;

		.wpnc__selected-note {
			animation-name: wpnc__selectIn;
			animation-timing-function: ease-in;
			animation-duration: 0.4s;
			animation-iteration-count: 1;
		}

		box-shadow: none;
	}

	.wpnc__note-list {
		left: auto;
		width: $notification-panel-width; // For reader/notifications screen.
	}

	.wpnc__single-view {
		right: $notification-panel-width;
		left: 10px;
		top: 0;
		bottom: 0;
		z-index: -1;

		header {
			nav {
				display: none;
			}
		}

		.wpnc__note {
			margin-top: 0;
		}

		-webkit-transform: translate3d(0, 0, 0); // fix for getting scrollbar in right z-index
		animation-name: wpnc__slideIn;
		animation-timing-function: ease-out;
		animation-fill-mode: forwards;
		animation-duration: 0.2s;
		animation-iteration-count: 1;
	}
}

.wpnc {
	margin: 0;
}

.wpnc__main {
	background-color: var(--color-surface);

	font: {
		family: $sans;
		size: $wpnc__font-size;
	}
	line-height: $wpnc__line-height;
	color: var(--color-neutral-70);
	// Fixes font anti-aliasing in iframes: andrewmoreton.co.uk/typekit-iframes-safari-weird-antialiasing/
	-webkit-font-smoothing: subpixel-antialiased;

	/* stylelint-disable-next-line unit-allowed-list */
	@media (-webkit-min-device-pixel-ratio: 1.25), (min-resolution: 120dpi) {
		body.font-smoothing-antialiased & {
			text-rendering: optimizeLegibility;
			-moz-osx-font-smoothing: grayscale;
			-webkit-font-smoothing: antialiased;
		}
	}

	@media only screen and (max-width: 799px) {
		background-color: var(--color-neutral-0);
	}
	@media only screen and (min-width: 409px) and (max-width: 430px) {
		background-color: var(--color-surface);
	}

	// Text elements
	b,
	strong {
		font-weight: 600;
	}

	// Links
	a,
	a:visited {
		color: var(--color-primary);
		text-decoration: none;
	}

	a:hover,
	a:focus,
	a:active {
		color: var(--color-primary);
	}

	button:not(.components-toggle-group-control-option-base) {
		background-color: transparent;
		border: none;
		color: var(--color-primary);
		cursor: pointer;
		font-size: inherit;
		outline: none;
		margin: 0;
		padding: 0;

		&[disabled] {
			color: var(--color-neutral-light);
			cursor: default;
		}
	}

	header {
		border-bottom: 1px solid var(--color-neutral-5);
		box-sizing: border-box;
		background-color: var(--color-surface);
		font-size: $wpnc__capital-font-size;
		height: $wpnc__title-bar-height + 1px;
		line-height: $wpnc__title-bar-height;
		padding: 0 $wpnc__padding-medium;
		text-align: center;

		nav {
			display: inline;
		}

		nav > div {
			display: inline;
			float: right;
			margin-right: -5px;
		}

		h1 {
			@extend %headertext;
			color: var(--color-neutral-40);
			display: inline;
		}

		button {
			line-height: 38px;
		}

		.wpnc__back {
			@extend %headertext;
			margin-left: -5px;
			display: inline;
			float: left;

			.gridicon {
				margin-right: 4px;
				vertical-align: -4px;
			}
		}

		.wpnc__prev,
		.wpnc__next {
			float: left;
			outline: none;

			.gridicon {
				vertical-align: middle;
			}
		}

		.wpnc__prev {
			margin-right: 8px;
		}

		.disabled {
			opacity: 0.5;
		}
	}

	.wpnc__list-view.wpnc__current {
		display: none;
	}
	.wpnc__single-view:not(.wpnc__current) {
		display: none;
	}

	.gridicon {
		fill: currentColor;
	}

	.wpnc__user a:focus,
	.wpnc__user a:focus span.wpnc__excerpt,
	.wpnc__user button:focus {
		outline: var(--color-primary-light) solid 2px;
	}

	.wpnc__user .wpnc__user__site .wpnc__excerpt {
		margin: 2px 0 0 2px;
	}

	.wpnc__user__username,
	span.wpnc__user {
		font-weight: 600;
		a.wpnc__user__home {
			color: var(--color-neutral-70);
		}
	}

	.wpnc__header a.wpnc__user {
		font-weight: 600;
		color: var(--color-neutral-70);
	}

	.wpnc__header a.wpnc__post {
		color: var(--color-text-subtle);

		&:hover {
			color: var(--color-primary);
		}
	}

	span.wpnc__post {
		font-weight: 600;
	}

	%headertext {
		text-transform: uppercase;
		text-decoration: none;
		font-weight: 600;
		font-size: $wpnc__capital-font-size;
	}

	.rtl header .back::after {
		transform: rotate(90deg);
	}

	.wpnc__filter {
		width: 100%;
		background-color: var(--color-surface);
		color: var(--color-text-subtle);
		border-bottom: 1px solid var(--color-neutral-5);
		border-left: 1px solid var(--color-border-inverted);
		text-align: center; // Center filter in IE 9
		height: $wpnc__filter-height;
		box-sizing: border-box;
		direction: ltr;
		display: table; //fallback for browsers not supporting flexbox

		.components-base-control {
			padding: 6px 8px;
		}
	
		.components-toggle-group-control {
			border: 1px solid var(--wp-components-color-gray-600, #949494);
			padding: 3px;

			> div {
				flex-basis: auto;
			}
		}

		.components-toggle-group-control-option-base {
			padding: 0 6px;
			white-space: nowrap;
			z-index: initial;
		}
	}

	.wpnc__note-list:not(.is-note-open) .wpnc__filter {
		border-left: 1px solid var(--color-neutral-0);
	}

	.wpnc__list-view .wpnc__notes,
	.error {
		background-color: var(--color-surface);
	}

	.wpnc__note {
		line-height: $wpnc__line-height;
		font-size: $wpnc__font-size;
		font-weight: normal;
		position: relative;
		clear: both;
		border-bottom: 1px solid var(--color-neutral-5);

		div.wpnc__body > p,
		div.wpnc__preface p {
			line-height: $wpnc__line-height;
			text-align: center;
		}

		.wpnc__note-icon {
			display: block;
			width: $wpnc__icon-size;
			height: $wpnc__icon-size;
			position: relative;
			float: left;
			margin: 0 $wpnc__padding-large 0 $wpnc__padding-large;
		}

		.wpnc__note-icon img {
			width: $wpnc__icon-size;
			height: $wpnc__icon-size;
		}

		.wpnc__note-icon .wpnc__gridicon {
			display: flex;
			align-items: center;
			justify-content: center;
			color: #fff;
			height: 16px;
			width: 16px;
			border: {
				width: 1px;
				style: solid;
				radius: 50%;
			}
		}
	}

	.wpnc__done-message {
		background: var(--color-neutral-0);
		color: var(--color-text-subtle);
		text-align: center;
		line-height: 50px;
		font-style: italic;
	}

	.wpnc__empty-notes-container {
		background-color: var(--color-neutral-0);
	}

	.wpnc__empty-notes {
		text-align: center;
		position: relative;
		top: 50%;
		padding: 0 32px;
		transform: translateY(-50%);

		h2 {
			font: 400 21px/24px $sans;
			margin-bottom: 4px;
		}

		p {
			font: 400 16px/24px $sans;
		}
	}

	.wpnc__loading-indicator {
		display: block;
		background-color: var(--color-neutral-0);
		height: 90px;
	}

	.wpnc__note-list {
		position: absolute;
		display: flex;
		flex-direction: column;
		top: 0;
		right: 0;
		left: 0;
		bottom: 0;
		overflow: hidden;
		&:not(.is-note-open) {
			box-shadow: -3px 1px 10px -2px color-mix(in srgb, var(--color-neutral-70) 7.5%, transparent);
		}
	}

	.wpnc__list-view {
		height: 100%;
		background-color: var(--color-neutral-0);
		overflow-y: scroll;
		-webkit-overflow-scrolling: touch;
		overscroll-behavior: contain;
		border-left: 1px solid var(--color-neutral-0);

		&.is-empty-list {
			overflow-y: hidden;
		}

		@media only screen and (min-width: 409px) and (max-width: 430px) {
			left: 9px;
		}

		h1 {
			text-align: center;
		}

		.wpnc__note {
			cursor: pointer;
		}

		.wpnc__note-icon .wpnc__gridicon {
			position: absolute;
			bottom: -5px;
			right: -8px;
			background-color: var(--color-neutral-20);
			border-color: var(--color-border-inverted);
		}

		.wpnc__note .wpnc__note-icon img {
			border-radius: 50%;
		}

		.unread .wpnc__note-icon .wpnc__gridicon {
			background: var(--color-primary);
			border-color: var(--color-neutral-0);
		}

		.wpnc__comment-unapproved .wpnc__note-icon .wpnc__gridicon {
			background: var(--color-warning);
			border-color: var(--color-warning-0);
		}

		.unread {
			background: color-mix(in srgb, var(--color-primary) 10%, transparent); // rgba is used to meet AA contrast standard
		}

		.wpnc__traffic_surge .wpnc__note-icon img {
			width: 32px;
			height: 32px;
			background: var(--color-primary);
			border-color: var(--color-primary);
			border-width: 4px;
			border-style: solid;
		}

		.wpnc__note:focus,
		.wpnc__selected-note {
			box-shadow: inset 4px 0 0 var(--color-primary);

			&.wpnc__comment-unapproved {
				box-shadow: inset 4px 0 0 var(--color-warning);
			}
		}

		.wpnc__text-summary {
			padding: 0 $wpnc__padding-large 0 2 * $wpnc__padding-large + $wpnc__icon-size;
			word-wrap: break-word;
			text-align: left;

			.wpnc__subject {
				max-height: 4em;
				-webkit-line-clamp: 3;
				@extend %ellipsy-box;
				font-size: $wpnc__font-size;
				line-height: $wpnc__line-height;
				letter-spacing: 0.15px;
			}

			.wpnc__subject .wpnc__gridicon {
				line-height: 1;
				vertical-align: -3px;
				color: var(--color-neutral-light);
				padding: 2px 5px 0 0;
			}

			.wpnc__subject .wpnc__comment {
				font-weight: 600;
			}

			.wpnc__subject .wpnc__user__site {
				font-weight: 600;
			}

			.wpnc__subject .wpnc__site {
				font-weight: 600;
			}

			.wpnc__excerpt {
				max-height: 3em;
				-webkit-line-clamp: 2;
				@extend %ellipsy-box;
				color: var(--color-text-subtle);
				font-size: $wpnc__font-size;
				letter-spacing: 0.25px;
				padding-top: 2px;
			}
		}

		.wpnc__time-group-title {
			align-items: center;
			display: flex;
			@extend %headertext;
			color: var(--color-neutral-40);
			padding: 6px 0;
			background: rgba(255, 255, 255, 0.95);
			border-bottom: 1px solid var(--color-neutral-5);

			.gridicons-time {
				align-self: center;
				margin-left: $wpnc__padding-medium;
				margin-right: 4px;
			}

			.gridicons-cog {
				cursor: pointer;
				align-self: center;
				border-radius: 2px;
				margin-left: auto;
				margin-right: $wpnc__padding-small;
			}

			.wpnc__time-title {
				align-items: center;
				display: flex;
				flex: 1;
			}
		}

		.wpnc__time-group-wrap {
			height: $wpnc__header-height;
			top: 0;
			z-index: 100;
			text-align: left;
			position: sticky;

			.wpnc__settings,
			.wpnc__keyboard-shortcuts-button {
				display: flex;

				&:focus svg {
					outline: var(--color-primary-light) solid 2px;
				}
			}

			.wpnc__keyboard-shortcuts-button {
				flex-grow: 0;
				padding-right: 8px;
				color: var(--color-neutral-40, #787c82);
			}
		}
	}

	.wpnc__undo-item {
		background: var(--color-error);
		color: var(--color-text-inverted);

		p {
			padding-top: 1em;
			padding-bottom: 1em;
			display: flex;
			align-items: center;
		}

		.wpnc__undo-link {
			margin-left: 1em;
			text-transform: uppercase;
			color: var(--color-text-inverted);
		}

		.wpnc__undo-message {
			margin-left: 2em;
		}
	}

	button.wpnc__close-link {
		color: var(--color-text-inverted);
		margin-left: auto;
		padding: 0 10px;
		cursor: pointer;
		user-select: none;
	}

	.error-view {
		h1 {
			text-align: center;
		}
	}

	.wpnc__single-view {
		position: absolute;
		display: flex;
		flex-direction: column;
		height: 100%;
		left: 0;
		right: 0;
		box-shadow: -3px 1px 10px -2px color-mix(in srgb, var(--color-neutral-70) 7.5%, transparent);

		@media only screen and (min-width: 480px) {
			border-left: 1px solid var(--color-neutral-0);
		}

		background-color: var(--color-neutral-0);
		ol {
			height: 100%;
			overflow-y: auto;
			-webkit-overflow-scrolling: touch;
		}

		.wpnc__image {
			display: block;
			margin: auto;
			max-width: 100%;
		}

		.wpnc__user {
			p {
				@extend %ellipsy-box;
				-webkit-line-clamp: 1;
				font-size: $wpnc__font-size;

				.wpnc__user__username,
				a.wpnc__user__site {
					@extend %ellipsy;
					white-space: nowrap;
					display: inline;
				}
			}

			.wpnc__user__meta {
				&:not(.wpnc__user__bulleted) {
					.wpnc__user__site {
						-webkit-line-clamp: 1;
						@extend %ellipsy-box;
					}
				}
			}
		}

		.wpnc__header {
			@extend %calypso-border;

			.wpnc__user__usertitle {
				-webkit-line-clamp: 1;
				@extend %ellipsy-box;
				white-space: nowrap;
				display: block;
			}

			.wpnc__excerpt {
				color: var(--color-secondary);
				-webkit-line-clamp: 1;
				@extend %ellipsy-box;
				white-space: nowrap;
				display: block;
			}
		}

		.wpnc__comment .wpnc__user p.wpnc__excerpt {
			color: var(--color-secondary);
			max-height: 1.5em;
			font-size: 14px;
		}

		.wpnc__reply {
			color: var(--color-text-subtle);
			padding: $wpnc__padding-medium 0;
			border-bottom: 1px solid var(--color-neutral-5);

			.wpnc__gridicon {
				padding: 0 10px;

				.gridicon {
					vertical-align: top;
				}
			}

			a {
				font-weight: 600;
				color: var(--color-text-subtle);
				text-decoration: underline;
			}
		}

		.wpnc__note:not(.wpnc__current) {
			display: none;
		}

		.wpnc__note {
			background-color: var(--color-surface);
			border: none;
		}

		.wpnc__note-icon .wpnc__gridicon {
			font-size: 2em;
			background-color: var(--color-neutral-10);
			border-color: var(--color-neutral-10);
		}

		.wpnc__summary {
			color: var(--color-text-subtle);

			p {
				@extend %ellipsy-box;
				-webkit-line-clamp: 3;
			}
		}

		.wpnc__preface {
			@extend %ellipsy-box;
			-webkit-line-clamp: 3;
			margin-bottom: 1em;
		}

		.wpnc__preface p {
			display: inline;

			&::after {
				content: " ";
			}

			&:first-of-type {
				display: block;
			}
		}

		.wpnc__time-group-title {
			display: none;
		}

		.wpnc__time-group-wrap {
			display: none;
		}
	}

	.wpnc__summary {
		@extend %container;
		padding: $wpnc__padding-large 0;
	}

	.time-notification {
		float: right;
		color: var(--color-text-subtle);
		margin-left: 0.25em;
		/* stylelint-disable-next-line declaration-property-unit-allowed-list */
		line-height: 1em;
		margin-top: 0.2em;
	}

	.wpnc__body {
		@extend %container;
		.wpnc__paragraph {
			word-wrap: break-word;
			margin-top: $wpnc__padding-large;

			a {
				text-decoration: underline;

				&.wpnc__button,
				&.mention {
					text-decoration: none;
				}
			}

			span.list {
				display: inline-block;
				margin-left: 2em;
				br {
					content: " ";
					display: block;
				}
			}

			.wpnc__gridicon {
				vertical-align: text-top;
			}

			pre {
				background: var(--color-neutral-0);
				border: 1px solid var(--color-neutral-10);
				border-radius: 3px;
				padding: 4px;
				white-space: normal;

				code {
					border: none;
					background: none;
				}
			}

			code {
				font-family: $code;
				font-size: 90%;
				color: var(--color-neutral-50);
				background: var(--color-neutral-0);
				border: 1px solid var(--color-neutral-10);
				border-radius: 3px;
				padding: 0 2px;
			}

			// Use important to overwrite the styles as the priority of #wpnc-panel selector is too high
			// See: client/notifications/style.scss
			ol.wpnc__ol--outside {
				margin-left: 1.2em !important;
				list-style-position: outside !important;
				overflow: visible;
			}

			hr.wpnc__hr-separator {
				width: 100px;
				height: 2px;
				margin: 4px auto;
			}
		}

		.wpnc__paragraph:first-of-type {
			margin-top: 0;
		}

		.wpnc__body-list {
			list-style: disc;
			padding: 0 16px;
		}

		.wpnc__body-todo {
			list-style: none;

			.wpnc__todo-done::before {
				/* stylelint-disable-next-line no-irregular-whitespace */
				content: "◉ ";
			}

			.wpnc__todo-not-done::before {
				/* stylelint-disable-next-line no-irregular-whitespace */
				content: "◎ ";
			}
		}

		blockquote {
			margin: 0 $wpnc__padding-medium $wpnc__padding-medium;
			font-style: italic;
			color: var(--color-text-subtle);
			background: transparent;
		}
	}

	.wpnc__single-view .wpnc__comment .wpnc__body .wpnc__user {
		border: none;
	}

	.wpnc__single-view img {
		max-width: 128px;
		height: auto;
	}

	.wpnc__single-view .wpnc__badge .wpnc__body .wpnc__body-content .wpnc__paragraph {
		font-family: $sans;
	}

	.wpnc__single-view .wpnc__badge img {
		display: block;
		margin: 0 auto;
		padding: 36px $wpnc__padding-large $wpnc__padding-large;
	}

	.wpnc__body .wpnc__body-content .wpnc__paragraph {
		font-family: $sans;
		padding: 0 $wpnc__padding-large;
		text-align: left;
	}

	.wpnc__body .wpnc__body-content .wpnc__paragraph:last-child {
		padding: 0 $wpnc__padding-large $wpnc__padding-medium;
	}

	.wpnc__comment .wpnc__body .wpnc__body-content,
	.wpnc__new_post .wpnc__body .wpnc__body-content,
	.wpnc__automattcher .wpnc__body .wpnc__body-content,
	.wpnc__blogging_prompts_note .wpnc__body .wpnc__body-content {
		border-bottom: 1px solid var(--color-neutral-5);
		padding-top: $wpnc__padding-small;
	}

	.wpnc__blogging_prompts_note {
		svg.gridicons-bell-off {
			position: absolute;
			top: 10px;
			right: 20px;

			&:hover {
				path.bell {
					stroke: var(--color-link);
				}

				rect {
					fill: var(--color-link);
				}

				g {
					opacity: 1;
				}
			}
		}

		.wpnc__action-link {
			&:only-child {
				padding: 0;
			}
			&:hover {
				svg.gridicons-pinned path {
					fill: var(--color-link);
				}
			}
		}
	}

	.wpnc__comment .wpnc__body .wpnc__body-content {
		box-shadow: inset 4px 0 0 var(--color-neutral-0);
		margin: 0 0 0 $wpnc__padding-medium;
	}

	.wpnc__body-content .match {
		font-weight: 600;
	}

	.wpnc__comment-unapproved .wpnc__body {
		.wpnc__body-content {
			box-shadow: inset 4px 0 0 var(--color-warning);
		}

		.blockquote {
			color: $wpnc__red-darker;
		}
	}

	.wpnc__comment-unapproved div.wpnc__user .wpnc__user__meta a:hover {
		text-decoration: underline;
	}

	.wpnc__comment .wpnc__body .wpnc__body-content .wpnc__user {
		padding-top: 0;
		padding-left: $wpnc__padding-medium;
	}

	.comment-self p,
	.comment-other {
		padding: 0 $wpnc__padding-medium;
	}

	.wpnc__post .wpnc__paragraph {
		padding: $wpnc__padding-large $wpnc__padding-medium 0;
	}

	// Check if global-sidebar-visible not present
	body:not(:has(.is-global-sidebar-visible)) & {
		@media only screen and (min-width: $no-sidebar-min-page-width) {
			@include setToggleNoteBehaviour;
		}
	}

	// Override the CSS variable value if .global-sidebar-visible is present
	body:has(.is-global-sidebar-visible) & {
		@media only screen and (min-width: $with-sidebar-min-page-width) {
			@include setToggleNoteBehaviour;
		}
	}

	@keyframes wpnc__slideIn {
		from {
			-webkit-transform: translateX(100%);
			transform: translateX(100%);
		}
		to {
			-webkit-transform: translateX(0%);
			transform: translateX(0%);
		}
	}

	@keyframes wpnc__selectIn {
		from {
			background-color: var(--color-neutral-0);
		}
		to {
			background-color: var(--color-surface);
		}
	}
}

.wpnc__keyboard-shortcuts-popover.popover {
	font: {
		family: $sans;
		size: $wpnc__capital-font-size;
	}

	.popover__arrow {
		z-index: 1000;
	}
	.popover__inner {
		width: 250px;
		z-index: 999;
		left: 0;

		h2 {
			padding: 10px;
			margin: 0;
			border-bottom: 1px solid var(--color-neutral-10);
			text-align: left;
			font-weight: bold;
			font-size: $wpnc__capital-font-size;
		}

		ul {
			padding: 10px 20px 10px 10px;
			margin: 0;
		}

		li {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 8px;

			.shortcut {
				border: 1px solid var(--color-neutral-10);
				border-radius: 4px;
				min-width: 25px;
				min-height: 25px;

				&.letter {
					font-weight: bold;
					line-height: 25px;
				}

				&.has-icon {
					svg {
						margin-top: 4px;
					}
				}
			}
		}
	}
}
