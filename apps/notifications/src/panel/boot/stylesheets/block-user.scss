div.wpnc__user {
	@extend %container;

	text-align: left;

	img {
		height: $wpnc__icon-size-detail;
		width: $wpnc__icon-size-detail;
		float: left;
		margin: $wpnc__user-margin-adjustment $wpnc__padding-medium 0 0;
		border-radius: $wpnc__icon-size-detail*0.5;
	}

	p {
		font-size: 0.85em;
		margin: 0;
	}

	padding: ($wpnc__padding-large - $wpnc__user-margin-adjustment) $wpnc__padding-medium $wpnc__padding-small;

	.follow-link {
		margin-top: -0.1em;
		color: var(--color-text-subtle);
	}

	.follow-link .gridicon {
		fill: var(--color-neutral-light);
		vertical-align: middle;
		margin-right: 0.2em;
	}

	.wpnc__user__bulleted .wpnc__user__bullet::before {
		content: "\0020\2022\0020";
	}

	.wpnc__user__meta {
		font-weight: 400;
		color: var(--color-text-subtle);
	}
}

.wpnc__comment div.wpnc__user .wpnc__user__meta {
	a {
		color: var(--color-text-subtle);

		&:hover {
			color: var(--color-primary);
		}
	}
}

.wpnc__single-view div.wpnc__user {
	@extend %calypso-border;
}

.wpnc__comment .wpnc__body div.wpnc__user .follow-link,
.automattcher .wpnc__body div.wpnc__user .follow-link {
	display: none;
}

.wpnc__follow .wpnc__body div.wpnc__user,
.wpnc__like .wpnc__body div.wpnc__user,
.wpnc__comment_like .wpnc__body div.wpnc__user,
.wpnc__reblog .wpnc__body div.wpnc__user {
	/* stylelint-disable-next-line declaration-property-unit-allowed-list */
	line-height: 1.3em;
}

.wpnc__follow .wpnc__body div.wpnc__user img,
.wpnc__like .wpnc__body div.wpnc__user img,
.wpnc__comment_like .wpnc__body div.wpnc__user img,
.wpnc__reblog .wpnc__body div.wpnc__user img {
	height: $wpnc__icon-size;
	width: $wpnc__icon-size;
	border-radius: $wpnc__icon-size*0.5;
}
