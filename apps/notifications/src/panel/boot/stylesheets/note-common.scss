div.wpnc__note-actions {
	@extend %container;
	color: var(--color-neutral-70);
	/* stylelint-disable-next-line declaration-property-unit-allowed-list */
	line-height: 1em;
	text-align: center;
	padding-top: $wpnc__padding-large;

	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;

	a {
		color: inherit;
		margin-right: 0.5em;
	}
}

.wpnc__comment-unapproved div.wpnc__note-actions {
	border-top: none;
	margin-top: 0;
}

// Taken from packages/components/src/screen-reader-text/style.scss
.wpnc__note-list button.screen-reader-text,
.wpnc__note-body button.screen-reader-text {
	clip: rect(1px, 1px, 1px, 1px);
	position: absolute !important;

	&:hover,
	&:active,
	&:focus {
		background-color: #f1f1f1;
		border-radius: 3px;
		box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
		clip: auto !important;
		color: #21759b;
		display: block;
		font-size: 14px;
		font-weight: bold;
		height: auto;
		left: 5px;
		line-height: normal;
		padding: 15px 23px 14px;
		text-decoration: none;
		top: 5px;
		width: auto;
		z-index: z-index("screen-reader-text-parent", ".screen-reader-text:focus"); // Above WP toolbar
	}
}
