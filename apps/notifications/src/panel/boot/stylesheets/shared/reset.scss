.wpnc__main {
	div,
	span,
	applet,
	object,
	iframe,
	h1,
	h2,
	h3,
	h4,
	h5,
	h6,
	p,
	blockquote,
	pre,
	a,
	abbr,
	acronym,
	address,
	big,
	cite,
	code,
	del,
	dfn,
	em,
	font,
	ins,
	kbd,
	q,
	s,
	samp,
	small,
	strike,
	strong,
	sub,
	sup,
	tt,
	var,
	dl,
	dt,
	dd,
	ol,
	ul,
	li,
	fieldset,
	form,
	label,
	legend,
	table,
	caption,
	tbody,
	tfoot,
	thead,
	tr,
	th,
	td {
		border: 0;
		margin: 0;
		font-family: inherit;
		font-size: 100%;
		font-style: inherit;
		font-weight: inherit;
		outline: 0;
		padding: 0;
		vertical-align: baseline;
	}
	article,
	aside,
	details,
	figcaption,
	figure,
	footer,
	header,
	hgroup,
	nav,
	section {
		display: block;
	}
	ol,
	ul {
		list-style: none;
	}
	table {
		/* tables still need 'cellspacing="0"' in the markup */
		border-collapse: separate;
		border-spacing: 0;
	}
	caption,
	th,
	td {
		font-weight: normal;
		text-align: left;
	}
	blockquote::before,
	blockquote::after,
	q::before,
	q::after {
		content: "";
	}
	blockquote,
	q {
		quotes: "" "";
	}
	a:focus {
		outline: var(--color-primary-light) solid 2px;
	}
	a:hover,
	a:active {
		/* Improves readability when focused and also mouse hovered in all browsers people.opera.com/patrickl/experiments/keyboard/test */
		outline: 0;
	}
	a img {
		border: 0;
	}

	// Reset the default styling on form inputs in Webkit/iOS
	input,
	textarea {
		border-radius: 0;
		-webkit-appearance: none; // Autoprefixer does not support appearance
		appearance: none;
		overflow: auto;
	}
	input[type="radio"],
	input[type="checkbox"] {
		-webkit-appearance: none;
	}
}
