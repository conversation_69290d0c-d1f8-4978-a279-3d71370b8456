.wpnc__main {
	.wpnc__spinner {
		display: flex;
		align-items: center;
		position: relative;
		top: 50%;
	}

	@keyframes wpnc__rotate-spinner {
		100% {
			transform: rotate(360deg);
		}
	}

	.wpnc__spinner__outer,
	.wpnc__spinner__inner {
		margin: auto;
		box-sizing: border-box;
		border: 0.1em solid transparent;
		border-radius: 50%;
		animation: 3s linear infinite;
		animation-name: wpnc__rotate-spinner;
	}

	.wpnc__spinner__outer {
		border-top-color: var(--color-primary);
	}

	.wpnc__spinner__inner {
		width: 100%;
		height: 100%;
		border-top-color: var(--color-primary);
		border-right-color: var(--color-primary);
		opacity: 0.4;
	}
}
