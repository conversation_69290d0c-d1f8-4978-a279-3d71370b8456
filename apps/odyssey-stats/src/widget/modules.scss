@import "../styles/typography";

$emerald-hover-color: #414141;
$jp-gray: #dcdcde;

.stats-widget-modules {
	margin-top: 24px;
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	column-gap: 24px;
}

.stats-widget-module__icon {
	margin-bottom: 16px;
}

.stats-widget-module__title {
	font-family: $font-sf-pro-text;
	font-weight: 500;
	font-size: $font-body-small;
	line-height: 20px;
	letter-spacing: -0.24px;
}

.stats-widget-module__value {
	margin-top: 4px;
	font-family: $font-sf-pro-display;
	font-weight: 400;
	font-size: $font-headline-small;
	line-height: 1;
	letter-spacing: -0.02em;
}

.stats-widget-module__info {
	margin-top: 16px;

	a {
		font-size: $font-body-extra-small;
		line-height: 0.5;
	}
}

// Use base component styling of `@automattic/components/Button`
.button.jetpack-emerald-button {
	font-weight: 600;

	&:focus {
		box-shadow: none;
	}

	&.is-primary {
		color: var(--studio-white);
		background: var(--studio-black);
	}

	// Use `transparent` styling as secondary styles
	&.is-transparent {
		color: var(--studio-black);
		background: inset 0 0 0 1.5px var(--studio-white);

		&:hover:not(.is-busy) {
			color: var(--studio-black);
			background: var(--studio-gray-0);
		}

		&.is-busy {
			background: $jp-gray;
			color: var(--studio-gray-20);
			border: 1px solid transparent;
		}
	}
}
