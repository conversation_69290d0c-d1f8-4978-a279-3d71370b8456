$offset-padding-hovering: 8px;

.stats-widget-highlights {
	.stats-widget-highlights__header {
		display: flex;
		justify-content: space-between;

		label {
			font-weight: 500;
			font-size: $font-body-large;
			line-height: 26px;
		}
	}

	.stats-widget-highlights__tabs {
		display: none;
		margin-top: 16px;
	}

	.stats-widget-highlights__body {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		column-gap: 24px;
		margin: 16px (-$offset-padding-hovering) 0;
	}

	.stats-widget-highlights__column {
		// Make sure the highlight section doesn't overflow the container, which makes ellipsis work.
		min-width: 0;
	}

	.stats-widget-highlights-card__title {
		display: block;
		font-weight: 500;
		font-size: $font-body;
		line-height: 26px;
		margin: 0 0 16px $offset-padding-hovering;
	}

	.stats-widget-highlights-card__empty {
		min-height: 40px;
		margin: 0 0 0 $offset-padding-hovering;
		color: var(--studio-gray-40);
	}

	.stats-widget-highlights-card__list {
		list-style: none;
		padding: 0;
		margin: 0;

		li {
			&:not(:last-child) {
				margin-bottom: 8px;
			}

			&:hover {
				& > a {
					background-color: var(--studio-gray-0);
					border-radius: 4px;
				}

				svg {
					display: block;
				}
			}

			// For the wrapper padding
			& > * {
				padding: 4px $offset-padding-hovering;
			}

			& > a {
				display: block;
				position: relative;
				color: inherit;
				// For the svg icon
				padding-right: 26px;
				cursor: pointer;

				&:focus {
					outline: 0;
					box-shadow: none;
				}

				p {
					text-decoration: underline;
				}

				svg {
					display: none;
					position: absolute;
					top: 12px;
					right: 10px;
				}
			}

			p {
				margin: 0;

				font-size: $font-body-small;
				font-weight: 500;
				letter-spacing: -0.24px;
				line-height: 20px;

				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}

			span {
				font-size: $font-body-small;
				font-weight: 400;
				letter-spacing: -0.24px;
				line-height: 20px;
				color: var(--studio-gray-60);
			}
		}
	}

	.stats-widget-highlights-card__view-all {
		margin: 16px 0 0 $offset-padding-hovering;
		font-weight: 600;
		font-size: $font-body-small;
		line-height: 20px;
		letter-spacing: -0.24px;

		a {
			color: inherit;
			text-decoration: none;

			&:visited {
				color: var(--studio-gray-100);
			}
		}
	}
}
