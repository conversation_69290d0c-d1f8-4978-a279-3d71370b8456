{"name": "@automattic/odyssey-stats", "version": "0.1.0", "description": "Calypso Stats served within wp-admin via the Jetpack plugin.", "main": "dist/build.min.js", "sideEffects": true, "repository": {"type": "git", "url": "git://github.com/Automattic/wp-calypso.git", "directory": "apps/stats"}, "private": true, "author": "Automattic Inc.", "license": "GPL-2.0-or-later", "bugs": {"url": "https://github.com/Automattic/wp-calypso/issues"}, "homepage": "https://github.com/Automattic/wp-calypso", "scripts": {"clean": "npx rimraf dist", "build": "NODE_ENV=production yarn dev && yarn translate", "build:stats": "calypso-build", "dev": "yarn run calypso-apps-builder --localPath dist --remotePath /home/<USER>/public_html/widgets.wp.com/odyssey-stats/v1", "show-stats": "NODE_ENV=production EMIT_STATS=true yarn build", "test:js": "yarn run -T test-apps apps/odyssey-stats", "test:js:watch": "yarn test:js --watch", "test:size": "size-limit", "teamcity:build-app": "yarn run build && yarn run test:size", "translate": "rm -rf dist/strings && mkdir -p dist && wp-babel-makepot '../../{client,packages,apps}/**/*.{js,jsx,ts,tsx}' --ignore '**/node_modules/**,**/test/**,**/*.d.ts' --base '../../' --dir './dist/strings' --output './dist/odyssey-strings.pot' && build-app-languages --stringsFilePath=./dist/odyssey-strings.pot"}, "dependencies": {"@automattic/calypso-color-schemes": "workspace:^", "@automattic/calypso-polyfills": "workspace:^", "@automattic/calypso-router": "workspace:^", "@automattic/calypso-url": "workspace:^", "@automattic/components": "workspace:^", "@tanstack/react-query": "^5.83.0", "@wordpress/base-styles": "^5.23.0", "@wordpress/data": "^10.23.0", "@wordpress/icons": "^10.23.0", "calypso": "workspace:^", "clsx": "^2.1.1", "debug": "^4.4.1", "moment": "^2.30.1", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-redux": "^9.2.0", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "wpcom": "workspace:^"}, "devDependencies": {"@automattic/babel-plugin-transform-wpcalypso-async": "workspace:^", "@automattic/calypso-apps-builder": "workspace:^", "@automattic/calypso-babel-config": "workspace:^", "@automattic/calypso-build": "workspace:^", "@automattic/calypso-eslint-overrides": "workspace:^", "@automattic/languages": "workspace:^", "@automattic/webpack-extensive-lodash-replacement-plugin": "workspace:^", "@automattic/webpack-inline-constant-exports-plugin": "workspace:^", "@automattic/wp-babel-makepot": "workspace:^", "@babel/core": "^7.27.1", "@size-limit/file": "^8.2.6", "@wordpress/dependency-extraction-webpack-plugin": "^6.24.0", "autoprefixer": "^10.4.21", "babel-jest": "^29.7.0", "gettext-parser": "^6.0.0", "html-webpack-plugin": "^5.6.3", "jest": "^29.7.0", "lodash": "^4.17.21", "path-browserify": "^1.0.1", "postcss": "^8.5.3", "size-limit": "^8.2.6", "webpack": "^5.99.8", "webpack-bundle-analyzer": "^4.10.2"}}