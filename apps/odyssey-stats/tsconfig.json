{"extends": "@automattic/calypso-typescript-config/mixed-package.json", "compilerOptions": {"rootDir": "../..", "noEmit": true, "typeRoots": ["./node_modules/@types", "../../node_modules/@types"], "types": ["node"]}, "include": ["./src/**/*", "./package.json", "../../config/production.json"], "exclude": ["./**/node_modules/**/*", "./**/test/**/*", "./**/.stories.tsx"], "references": [{"path": "../../packages"}, {"path": "../../client/tsconfig-reference.json"}]}