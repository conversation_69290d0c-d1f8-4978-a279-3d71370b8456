{"autoload": {"psr-4": {"Automattic\\WCServices\\": "src/"}}, "require-dev": {"phpunit/phpunit": "^9.6.8", "squizlabs/php_codesniffer": "^3.10", "dealerdirect/phpcodesniffer-composer-installer": "^1.0", "wp-coding-standards/wpcs": "^3.1", "woocommerce/qit-cli": "^0.10.0", "woocommerce/woocommerce-sniffs": "^0.0.2"}, "require": {"automattic/jetpack-connection": "^6.2.0", "automattic/jetpack-config": "^3.0.0", "automattic/jetpack-autoloader": "^5.0.0", "automattic/jetpack-status": "^5.0.1"}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "automattic/jetpack-autoloader": true}}, "scripts": {"test": ["phpunit"], "phpcs": ["./vendor/bin/phpcs -s -p --ignore=vendor,.git,assets,node_modules,dist,docker,release,bin --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra"], "phpcbf": ["./vendor/bin/phpcbf -p --ignore=vendor,.git,assets,node_modules,dist,docker,release,bin --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra "], "check-security": ["./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules,dist,docker,release,bin --standard=./.phpcs.security.xml  --report-full --report-summary"], "check-php": ["./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules,dist,docker,release,bin --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"], "check-php:fix": ["./vendor/bin/phpcbf . --ignore=vendor,.git,assets,node_modules,dist,docker,release,bin --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"], "check-all": ["./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules,dist,docker,release,bin --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors"], "check-all:fix": ["./vendor/bin/phpcbf . --ignore=vendor,.git,assets,node_modules,dist,docker,release,bin --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors"], "qit:security": ["npm run build && composer install && ./vendor/bin/qit run:security woocommerce-services --zip=woocommerce-services.zip"]}}